version: '3.0'
services:
  amap-test-execution-service:
    image: 'amap-test-execution-service'
    container_name: 'amap-test-execution-service'
    build:
      context: .
      dockerfile: Dockerfile
    ports:
      - '4205:4205'
    networks:
      - default
      - kafka-network
    environment:
      - KAFKA_BOOTSTRAP_SERVERS=kafka:9092
      - REDIS_HOST=test-execution-redis
      - REDIS_PORT=6382
    restart: on-failure
    depends_on:
      - test-execution-redis

  test-execution-redis:
    image: 'bitnami/redis:latest'
    container_name: test-execution-redis
    restart: unless-stopped
    tty: true
    volumes:
      - test-execution-redis-data:/data
    ports:
      - '6382:6382'
    environment:
      - ALLOW_EMPTY_PASSWORD=yes
      - REDIS_PORT_NUMBER=6382

volumes:
  test-execution-redis-data:
    driver: local

networks:
  kafka-network:
    external:
      name: amap-kafka_kafka-network
