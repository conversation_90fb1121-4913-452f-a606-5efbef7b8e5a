# Dodokpo Test Execution Service
Dodokpo Assessment Platform (Test Execution Service) is a microservice responsible for handling test execution, proctoring, and automated assessment marking within the Amalitech Assessment Platform.
## Overview
This service manages the test-taking process, including:
- Assessment execution and progress tracking
- Proctoring features (screenshots, camera captures, window violation tracking)
- AI-powered essay marking using OpenAI
- Test submission and result processing
- Survey collection
## Technology Stack
- **Backend**: Node.js with Express and TypeScript
- **Database**: PostgreSQL with Prisma ORM
- **Messaging**: Kafka for event-driven communication
- **Caching**: Redis
- **AI Integration**: OpenAI API for essay marking
- **Containerization**: Docker and Docker Compose
- **CI/CD**: Jenkins pipeline
## Getting Started
### Prerequisites
- Node.js (LTS version)
- PostgreSQL
- Redis
- Kafka (optional for local development)
- Docker and Docker Compose (for containerized deployment)
### Installation
1. Clone the repository:
   ```
   git clone https://github.com/Amali-Tech/amap-test-execution-service.git
   cd amap-test-execution-service
   ```
2. Install dependencies:
   ```
   npm install
   ```
3. Set up environment variables:
   Create a `.env` file in the root directory with the following variables:
   ```
   DATABASE_URL=postgresql://username:password@localhost:5432/database_name
   REDIS_HOST=localhost
   REDIS_PORT=6379
   KAFKA_BOOTSTRAP_SERVERS=localhost:9092
   AI_SECRET=your_openai_api_key
   ```
4. Generate Prisma client:
   ```
   npm run prisma-generate
   ```
5. Create and migrate the database:
   ```
   npm run create-schema
   ```
6. Seed the database (optional):
   ```
   npm run prisma-seed
   ```
### Development
Start the development server:
```
npm run dev
```
### Testing
Run tests:
```
npm run test
```
Run tests in watch mode:
```
npm run test-dev
```
### Database Management
- Create database: `npm run db`
- Run migrations: `npm run migrate`
- Reset database: `npm run rebuild`
- Open Prisma Studio: `npm run prisma-studio`
## Deployment
### Docker Deployment
Build and run with Docker Compose:
```
docker-compose up --build
```
### CI/CD Pipeline
The project uses Jenkins for CI/CD with deployment to AWS:
- Develop branch: Deploys to development environment
- Staging branch: Deploys to staging environment
- Main branch: Deploys to production environment
## Project Structure
- `src/`: Source code
  - `bin/`: Server initialization
  - `controllers/`: Request handlers
  - `middlewares/`: Express middlewares
  - `routes/`: API routes
  - `helpers/`: Utility functions
  - `repository/`: Data access layer
  - `dtos/`: Data transfer objects
- `prisma/`: Prisma schema and migrations
- `tests/`: Test files
- `scripts/`: Deployment scripts
## Features
- Assessment taking and progress tracking
- Proctoring with screenshot and camera capture
- Window violation detection
- AI-powered essay marking
- Test submission and result processing
- Survey collection and processing
## License






