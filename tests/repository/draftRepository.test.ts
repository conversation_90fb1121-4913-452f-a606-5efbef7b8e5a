import sinon from 'sinon';
import prisma from '../../src/prisma';
import { DraftRepository } from '../../src/repository/draftRepository';
import { expect } from 'chai';
import { Draft, MatchMatrixDraftAnswer } from '@prisma/client';
import { CreateMatchMatrixAnswerDraft } from '../../src/helpers/types';

describe('DraftRepository', () => {
  afterEach(() => {
    sinon.restore();
  });

  describe('createOrUpdateStringArrayAnswer', () => {
    it('should update the draft if it already exists', async () => {
      const mockData = {
        assessmentTakerId: 'taker123',
        testId: 'test123',
        questionId: 'question123',
        questionType: 'Multi_select',
        testTakerAnswers: ['Answer1', 'Answer2'],
      };

      const existingDraft = { id: 'existing-draft-id' };
      const savedDraft = {
        id: 'existing-draft-id',
        assessmentTakerId: 'taker123',
        testId: 'test123',
        questionId: 'question123',
        questionType: 'Multi_select',
        stringArrayAnswers: ['Answer1', 'Answer2'],
      } as Draft;

      // Mock `findFirst` to return an existing draft
      prisma.draft.findFirst = sinon.stub().resolves(existingDraft);
      prisma.draft.create = sinon.stub();
      // Mock `update` to return updated draft
      prisma.draft.update = sinon.stub().resolves(savedDraft);

      const result = await DraftRepository.createOrUpdateStringArrayAnswer(
        mockData
      );

      expect((prisma.draft.update as sinon.SinonStub).calledOnce).to.be.true;
      expect((prisma.draft.create as sinon.SinonStub).calledOnce).to.be.false;
      expect(result).to.deep.equal(savedDraft);
      expect((prisma.draft.findFirst as sinon.SinonStub).calledOnce).to.be.true;
    });

    it('should create a new draft if does not exists', async () => {
      const mockData = {
        assessmentTakerId: 'taker123',
        testId: 'test123',
        questionId: 'question123',
        questionType: 'StringArray',
        testTakerAnswers: ['Answer1', 'Answer2'],
      };

      const savedDraft = {
        id: 'new-draft-id',
        assessmentTakerId: 'taker123',
        testId: 'test123',
        questionId: 'question123',
        questionType: 'StringArray',
        stringArrayAnswers: ['Answer1', 'Answer2'],
      } as Draft;
      prisma.draft.update = sinon.stub().resolves();
      // Mock `findFirst` to return null
      prisma.draft.findFirst = sinon.stub().resolves(null);

      // Mock `create` to return a new draft
      prisma.draft.create = sinon.stub().resolves(savedDraft);

      const result = await DraftRepository.createOrUpdateStringArrayAnswer(
        mockData
      );

      expect((prisma.draft.create as sinon.SinonStub).calledOnce).to.be.true;
      expect(result).to.deep.equal(savedDraft);
      expect((prisma.draft.update as sinon.SinonStub).calledOnce).to.be.false;
    });
  });

  describe('createOrUpdateMatchMatrixAnswer', () => {
    it('should update the draft and delete old match matrix answers if draft exists', async () => {
      const mockData = {
        assessmentTakerId: 'taker123',
        testId: 'test123',
        questionId: 'question123',
        questionType: 'MatchMatrix',
        testTakerAnswers: [
          { subquestionId: 'sub1', answers: ['new-answer-1', 'new-answer1'] },
        ],
      } as CreateMatchMatrixAnswerDraft;

      const existingDraft = { id: 'existing-draft-id' } as Draft;
      const savedDraft = {
        id: 'existing-draft-id',
        matchMatrixAnswers: [{ answers: ['new-answer-1', 'new-answer1'] }],
      } as Draft & { matchMatrixAnswers: MatchMatrixDraftAnswer[] };

      // Mock `findFirst` to return an existing draft
      prisma.draft.findFirst = sinon.stub().resolves(existingDraft);

      // Mock `deleteMany` and `update`
      prisma.matchMatrixDraftAnswer.deleteMany = sinon.stub().resolves();
      prisma.draft.update = sinon.stub().resolves(savedDraft);

      const result = await DraftRepository.createOrUpdateMatchMatrixAnswer(
        mockData
      );

      expect(
        (prisma.matchMatrixDraftAnswer.deleteMany as sinon.SinonStub).calledOnce
      ).to.be.true;
      expect((prisma.draft.update as sinon.SinonStub).calledOnce).to.be.true;
      expect(result).to.deep.equal(savedDraft);
    });

    it('should create a new draft if does not exists', async () => {
      const mockData = {
        assessmentTakerId: 'taker123',
        testId: 'test123',
        questionId: 'question123',
        questionType: 'MatchMatrix',
        testTakerAnswers: [
          { subquestionId: 'sub1', answers: ['answer-1', 'answer1'] },
        ],
      } as CreateMatchMatrixAnswerDraft;
      const savedDraft = {
        id: 'new-draft-id',
        matchMatrixAnswers: [{ answers: ['new-answer-1', 'new-answer1'] }],
      } as Draft & { matchMatrixAnswers: MatchMatrixDraftAnswer[] };

      // Mock `findFirst` to return null
      prisma.draft.findFirst = sinon.stub().resolves(null);

      // Mock `create` to return a new draft
      prisma.draft.create = sinon.stub().resolves(savedDraft);

      const result = await DraftRepository.createOrUpdateMatchMatrixAnswer(
        mockData
      );

      expect((prisma.draft.create as sinon.SinonStub).calledOnce).to.be.true;
      expect(result).to.equal(savedDraft);
    });
  });
});
