import { expect } from 'chai';
import * as sinon from 'sinon';
import proxyquire from 'proxyquire';
import { AssessmentService } from '../../src/services/assessment';
import {
  assessmentProgress,
  assessmentTakerResult,
  deleteResult,
  draftCandidateProgress,
  getAssessmentTakerCaptureShots,
  getAssessmentTakerIdentityInfo,
  getAssessmentTakerScreenshot,
  getAssessmentTakingStatus,
  getCandidateAssessmentsInfo,
  getCandidatePreSignedURL,
  submitAssessment,
  submitAssessmentTest,
} from '../../src/controllers/assessment';
import { mockReq, mockRes } from 'sinon-express-mock';
import { flagQuestionPayload } from '../fakeData/mockarguments';
import {
  fakeAssessmentTakerResult,
  fakeFlaggedQuestion,
  fakeIdentity,
  fakeShotResponse,
  fakeSurveyResponse,
  questionflagResponse,
} from '../fakeData/mockresponse';
import { AssessmentTaker } from '@prisma/client';
import { ReqMock, ResMock } from '../types';
import { AppError } from '../../src/middlewares/errorHandler';
import prisma from '../../src/prisma';
import { AssessmentResult } from '../../src/helpers/types';

describe('Assessment Controller', () => {
  describe('assessment in progress ', () => {
    let reqMock: ReqMock;
    let resMock: ResMock;
    let nextMock: sinon.SinonStub;
    beforeEach(() => {
      reqMock = mockReq({
        assessmentTakerInfo: {} as AssessmentTaker,
        body: { id: 'fake-assessmentTaker-Id' },
      });
      resMock = mockRes();

      nextMock = sinon.stub();
    });

    after(() => {
      sinon.restore();
    });
    it('Should response a 200 with data', async () => {
      const mockAssessmentServiceResponse = undefined; // will add mockecresponse
      const assessmentServiceStub = sinon
        .stub(AssessmentService, 'progress')
        .resolves();

      await assessmentProgress(reqMock, resMock, nextMock);

      expect(assessmentServiceStub.calledOnce).to.be.true;
      expect(resMock.status.calledWithExactly(200)).to.be.true;
      expect(
        resMock.send.alwaysCalledWithExactly({
          success: true,
          data: mockAssessmentServiceResponse,
        })
      ).to.be.true;

      assessmentServiceStub.restore();
    });

    it('Should call next function on error', async () => {
      const assessmentServiceStud = sinon
        .stub(AssessmentService, 'progress')
        .throws();
      await assessmentProgress(reqMock, resMock, nextMock);

      expect(assessmentServiceStud.calledOnce).to.be.true;
      expect(assessmentServiceStud.threw()).to.be.true;
      expect(nextMock.called).to.be.true;

      assessmentServiceStud.restore();
    });
  });

  describe('getAssessmentTakerIdentityInfo', () => {
    let reqMock: ReqMock;
    let resMock: ResMock;
    let nextMock: sinon.SinonStub;
    beforeEach(() => {
      reqMock = mockReq({
        assessmentTakerInfo: {} as AssessmentTaker,
      });
      resMock = mockRes();

      nextMock = sinon.stub();
    });

    after(() => {
      sinon.restore();
    });

    it('Should return status of 200 with data', async () => {
      const getAssessmentIdentityInfoSpy = sinon
        .stub(AssessmentService, 'getAssessmentIdentityInfo')
        .resolves(fakeIdentity);

      await getAssessmentTakerIdentityInfo(reqMock, resMock, nextMock);

      expect(getAssessmentIdentityInfoSpy.calledOnce).to.be.true;
      expect(resMock.status.calledWithExactly(200)).to.be.true;
      expect(
        resMock.send.calledWith({
          success: true,
          data: { identity: fakeIdentity },
        })
      ).to.be.true;
      expect(nextMock.notCalled).to.be.true;

      getAssessmentIdentityInfoSpy.restore();
    });
    it('Should call next function on error', async () => {
      const expection = new Error('error');
      const getAssessmentIdentityInfoSpy = sinon
        .stub(AssessmentService, 'getAssessmentIdentityInfo')
        .throwsException(expection);

      await getAssessmentTakerIdentityInfo(reqMock, resMock, nextMock);

      expect(getAssessmentIdentityInfoSpy.calledOnce).to.be.true;
      expect(nextMock.calledWith(expection)).to.be.true;

      getAssessmentIdentityInfoSpy.restore();
    });
  });

  describe('Question Flagging', () => {
    let reqMock: ReqMock;
    let resMock: ResMock;
    let nextMock: sinon.SinonStub;
    let createFlagStub: sinon.SinonStub;
    let questionFlaggingPublishStub: sinon.SinonStub;
    let flagQuestionController;

    beforeEach(() => {
      // Create stubs
      createFlagStub = sinon
        .stub()
        .resolves({ questionFlagged: {} as unknown });
      questionFlaggingPublishStub = sinon.stub().resolves();

      const serviceMock = {
        createFlag: createFlagStub,
      };

      const kafkaPublishMessageMock = {
        questionFlaggingPublish: questionFlaggingPublishStub,
      };

      // Use proxyquire to inject both mocks
      flagQuestionController = proxyquire('../../src/controllers/assessment', {
        '../services/questionFlagService': serviceMock,
        '../helpers/kafkaPublishMessage': kafkaPublishMessageMock,
      }).flagQuestion;

      reqMock = mockReq({
        body: flagQuestionPayload,
        assessmentTakerInfo: {} as AssessmentTaker,
      });
      resMock = mockRes();
      nextMock = sinon.stub();
    });

    afterEach(() => {
      sinon.restore();
    });

    it('Should return status of 201 with data', async () => {
      const createFlageMockResponse = { questionFlagged: questionflagResponse };
      createFlagStub.resolves(createFlageMockResponse);

      await flagQuestionController(reqMock, resMock, nextMock);

      expect(createFlagStub.calledOnce).to.be.true;
      expect(
        createFlagStub.calledWithExactly(
          flagQuestionPayload,
          reqMock.assessmentTakerInfo
        )
      ).to.be.true;
      expect(resMock.status.calledWithExactly(201)).to.be.true;
      expect(
        resMock.send.calledWithExactly({
          success: true,
          data: createFlageMockResponse.questionFlagged,
        })
      ).to.be.true;
    });

    it('Should call next function on error', async () => {
      const error = new Error('Test Error');
      createFlagStub.rejects(error);
      questionFlaggingPublishStub.rejects(error); // Also make the Kafka stub reject with same error

      await flagQuestionController(reqMock, resMock, nextMock);

      expect(nextMock.calledOnce).to.be.true;
      expect(nextMock.firstCall.args[0]).to.deep.equal(error);
    });
  });

  describe('Question Unflagging', () => {
    let reqMock: ReqMock;
    let resMock: ResMock;
    let nextMock: sinon.SinonStub;
    let getFlagQuestionByStub: sinon.SinonStub;
    let deleteFlagQuestionStub: sinon.SinonStub;
    let questionUnflaggingPublishStub: sinon.SinonStub;
    let unflagQuestionController;

    beforeEach(() => {
      // Create stubs
      getFlagQuestionByStub = sinon.stub().resolves(fakeFlaggedQuestion);
      deleteFlagQuestionStub = sinon.stub().resolves();
      questionUnflaggingPublishStub = sinon.stub().resolves();

      // Create service mock
      const questionFlagServiceMock = {
        getFlagQuestionBy: getFlagQuestionByStub,
        deleteFlagQuestion: deleteFlagQuestionStub,
      };

      const kafkaPublishMessageMock = {
        questionUnflaggingPublish: questionUnflaggingPublishStub,
      };

      // Use proxyquire to inject mocks
      unflagQuestionController = proxyquire(
        '../../src/controllers/assessment',
        {
          '../../src/services/questionFlagService': questionFlagServiceMock,
          '../../src/helpers/kafkaPublishMessage': kafkaPublishMessageMock,
        }
      ).unflagQuestion;

      // Setup request and response mocks
      reqMock = mockReq({
        assessmentTakerInfo: {} as AssessmentTaker,
        body: { questionId: 'fake-question-Id' },
      });
      resMock = mockRes();
      nextMock = sinon.stub();
    });

    after(() => {
      sinon.restore();
    });

    it('Should return status of 200 with message', async () => {
      // Setup stubs for this test
      getFlagQuestionByStub.resolves(fakeFlaggedQuestion);

      // Test function
      await unflagQuestionController(reqMock, resMock, nextMock);

      // Assertions
      expect(getFlagQuestionByStub.calledOnce).to.be.true;
      expect(deleteFlagQuestionStub.calledOnce).to.be.true;
      expect(questionUnflaggingPublishStub.calledOnce).to.be.true;
      expect(resMock.status.calledWithExactly(200)).to.be.true;
      expect(
        resMock.send.calledWith({
          success: true,
          data: { message: 'Question unflagged successfully' },
        })
      ).to.be.true;
      expect(nextMock.notCalled).to.be.true;
    });

    it('Should call next function when flagged question is not found', async () => {
      // Setup stub for this test
      getFlagQuestionByStub.resolves(undefined);

      // Test function
      // eslint-disable-next-line @typescript-eslint/ban-ts-comment
      // @ts-ignore
      await unflagQuestionController(reqMock, resMock, nextMock);

      // Assertions
      expect(getFlagQuestionByStub.calledOnce).to.be.true;
      expect(nextMock.calledOnce).to.be.true;
      // If your error is an AppError instance:
      // expect(nextMock.firstCall.args[0]).to.be.instanceOf(AppError);
    });
  });

  describe('Assessment Taker Result', () => {
    let reqMock: ReqMock;
    let resMock: ResMock;
    let nextMock: sinon.SinonStub;
    beforeEach(() => {
      reqMock = mockReq({
        body: flagQuestionPayload,
        assessmentTakerInfo: {} as AssessmentTaker,
      });
      resMock = mockRes();

      nextMock = sinon.stub();
    });

    after(() => {
      sinon.restore();
    });

    it('Should return status of 201 with data', async () => {
      const getAssessmentTakerResultStub = sinon
        .stub(
          AssessmentService,
          'getAssessmentTakerResult'
          // eslint-disable-next-line
          // @ts-ignore
        )
        .resolves(fakeAssessmentTakerResult);

      // test function
      await assessmentTakerResult(reqMock, resMock, nextMock);

      expect(getAssessmentTakerResultStub.calledOnce).to.be.true;
      expect(resMock.status.calledWithExactly(200)).to.be.true;
      expect(
        resMock.json.calledWith({
          success: true,
          data: {
            assessmentTakerResult: fakeAssessmentTakerResult,
          },
        })
      ).to.be.true;
      expect(nextMock.notCalled).to.be.true;

      getAssessmentTakerResultStub.restore();
    });

    it('Should call next function on error', async () => {
      const exception = new Error('error');
      const getAssessmentTakerResultStub = sinon
        .stub(AssessmentService, 'getAssessmentTakerResult')
        .throwsException(exception);

      try {
        // test function
        await assessmentTakerResult(reqMock, resMock, nextMock);
      } catch (error) {
        // assertion
        expect(getAssessmentTakerResultStub.calledOnce).to.be.true;
        expect(nextMock.calledOnce).to.be.true;
        expect(error).to.be.instanceOf(Error);
      }
    });
  });

  describe('Delete Result', () => {
    let reqMock: ReqMock;
    let resMock: ResMock;
    let nextMock: sinon.SinonStub;
    beforeEach(() => {
      reqMock = mockReq({
        assessmentTakerInfo: {} as AssessmentTaker,
        body: { id: 'fake-assessmentTaker-Id' },
      });
      resMock = mockRes();

      nextMock = sinon.stub();
    });

    after(() => {
      sinon.restore();
    });

    it('Should return status of 200 with message', async () => {
      prisma.testTakerBasicAssessmentResult.delete = sinon.stub().resolves();
      // test function
      await deleteResult(reqMock, resMock, nextMock);

      // assertion
      expect(
        (prisma.testTakerBasicAssessmentResult.delete as sinon.SinonStub)
          .calledOnce
      ).to.be.true;
      expect(resMock.status.calledWithExactly(200)).to.be.true;
      expect(
        resMock.send.calledWith({
          success: true,
          data: { message: 'Result deleted successfully' },
        })
      ).to.be.true;
      expect(nextMock.notCalled).to.be.true;
    });

    it('Should call next function on error', async () => {
      const exception = new Error('error');
      prisma.testTakerBasicAssessmentResult.delete = sinon
        .stub()
        .throwsException(exception);
      try {
        // test function
        await deleteResult(reqMock, resMock, nextMock);
      } catch (error) {
        // assertion
        expect(
          (prisma.testTakerBasicAssessmentResult.delete as sinon.SinonStub)
            .calledOnce
        ).to.be.true;
        expect(nextMock.calledOnce).to.be.true;
        expect(error).to.be.instanceOf(Error);
      }
    });
  });

  describe('Submit Assesment', () => {
    let reqMock: ReqMock;
    let resMock: ResMock;
    let nextMock: sinon.SinonStub;
    beforeEach(() => {
      reqMock = mockReq({
        assessmentTakerInfo: {} as AssessmentTaker,
        body: { id: 'fake-assessmentTaker-Id' },
      });
      resMock = mockRes();

      nextMock = sinon.stub();
    });

    after(() => {
      sinon.restore();
    });

    it('Should return status of 200 with message', async () => {
      const submitAssessmentStub = sinon
        .stub(AssessmentService, 'submitAssessment')
        .resolves();

      // test function
      await submitAssessment(reqMock, resMock, nextMock);

      // assertion
      expect(submitAssessmentStub.calledOnce).to.be.true;
      expect(resMock.status.calledWithExactly(200)).to.be.true;
      expect(
        resMock.send.calledWith({
          success: true,
          data: { message: 'Assessment submitted successfully' },
        })
      ).to.be.true;
      expect(nextMock.notCalled).to.be.true;

      submitAssessmentStub.restore();
    });

    it('Should call next function on error', async () => {
      const exception = new Error('error');
      const submitAssessmentStub = sinon
        .stub(AssessmentService, 'submitAssessment')
        .throwsException(exception);
      try {
        //  test function
        await submitAssessment(reqMock, resMock, nextMock);
      } catch (error) {
        // assertion
        expect(submitAssessmentStub.calledOnce).to.be.true;
        expect(nextMock.calledOnce).to.be.true;
        expect(error).to.be.instanceOf(Error);
      }
    });
  });

  describe('Send Survey', () => {
    let reqMock: ReqMock;
    let resMock: ResMock;
    let nextMock: sinon.SinonStub;
    let sendAssessmentSurveyStub: sinon.SinonStub;
    let sendSurveyController;

    beforeEach(() => {
      // Create stub
      sendAssessmentSurveyStub = sinon.stub().resolves(fakeSurveyResponse);

      // Create mock
      const kafkaPublishMessageMock = {
        sendAssessmentSurvey: sendAssessmentSurveyStub,
      };

      // Use proxyquire to inject mock
      sendSurveyController = proxyquire('../../src/controllers/assessment', {
        '../../src/helpers/kafkaPublishMessage': kafkaPublishMessageMock,
      }).sendSurvey;

      // Setup request and response mocks
      reqMock = mockReq({
        assessmentTakerInfo: {} as AssessmentTaker,
        body: { id: 'fake-assessmentTaker-Id' },
      });
      resMock = mockRes();
      nextMock = sinon.stub();
    });

    after(() => {
      sinon.restore();
    });

    it('Should return status of 200 with data', async () => {
      // Test function
      await sendSurveyController(reqMock, resMock, nextMock);

      // Assertions
      expect(sendAssessmentSurveyStub.calledOnce).to.be.true;
      expect(resMock.status.calledWithExactly(200)).to.be.true;
      expect(
        resMock.send.calledWith({
          success: true,
          data: { survey: fakeSurveyResponse },
        })
      ).to.be.true;
      expect(nextMock.notCalled).to.be.true;
    });

    it('Should call next function on error', async () => {
      const exception = new Error('error');
      sendAssessmentSurveyStub.rejects(exception);

      // Test function
      await sendSurveyController(reqMock, resMock, nextMock);

      // Assertions
      expect(sendAssessmentSurveyStub.calledOnce).to.be.true;
      expect(nextMock.calledOnce).to.be.true;
      expect(nextMock.firstCall.args[0]).to.equal(exception);
    });
  });

  describe('Assessment Taker Screenshot', () => {
    let reqMock: ReqMock;
    let resMock: ResMock;
    let nextMock: sinon.SinonStub;
    beforeEach(() => {
      reqMock = mockReq({
        assessmentTakerInfo: {} as AssessmentTaker,
      });
      resMock = mockRes();

      nextMock = sinon.stub();
    });

    after(() => {
      sinon.restore();
    });

    it('Should return status of 200 with data', async () => {
      const getAssessmentTakerScreenshotStub = sinon
        .stub(
          AssessmentService,
          // eslint-disable-next-line
          // @ts-ignore
          'getAssessmentTakerScreenshot'
        )
        .resolves(fakeShotResponse);

      // test function
      await getAssessmentTakerScreenshot(reqMock, resMock, nextMock);

      // assertion
      expect(getAssessmentTakerScreenshotStub.calledOnce).to.be.true;
      expect(resMock.status.calledWithExactly(200)).to.be.true;
      expect(
        resMock.json.calledWith({
          success: true,
          data: { assessmentScreenShots: fakeShotResponse },
        })
      ).to.be.true;
      expect(nextMock.notCalled).to.be.true;

      getAssessmentTakerScreenshotStub.restore();
    });

    it('Should call next function on error', async () => {
      const exception = new Error('error');
      const getAssessmentTakerScreenshotStub = sinon
        .stub(AssessmentService, 'getAssessmentTakerScreenshot')
        .throwsException(exception);
      try {
        //  test function
        await getAssessmentTakerScreenshot(reqMock, resMock, nextMock);
      } catch (error) {
        // assertion
        expect(getAssessmentTakerScreenshotStub.calledOnce).to.be.true;
        expect(nextMock.calledOnce).to.be.true;
        expect(error).to.be.instanceOf(Error);
      }
    });
  });

  describe('Assessment Taker Capture Shot', () => {
    let reqMock: ReqMock;
    let resMock: ResMock;
    let nextMock: sinon.SinonStub;
    beforeEach(() => {
      reqMock = mockReq({
        assessmentTakerInfo: {} as AssessmentTaker,
      });
      resMock = mockRes();

      nextMock = sinon.stub();
    });

    after(() => {
      sinon.restore();
    });

    it('Should return status of 200 with data', async () => {
      const getAssessmentTakerCaptureShotsStub = sinon
        .stub(
          AssessmentService,
          // eslint-disable-next-line
          // @ts-ignore
          'getAssessmentTakerCaptureShots'
        )
        .resolves(fakeShotResponse);

      // test function
      await getAssessmentTakerCaptureShots(reqMock, resMock, nextMock);

      // assertion
      expect(getAssessmentTakerCaptureShotsStub.calledOnce).to.be.true;
      expect(resMock.status.calledWithExactly(200)).to.be.true;
      expect(
        resMock.json.calledWith({
          success: true,
          data: { assessmentTakerCaptureShots: fakeShotResponse },
        })
      ).to.be.true;
      expect(nextMock.notCalled).to.be.true;

      getAssessmentTakerCaptureShotsStub.restore();
    });

    it('Should call next function on error', async () => {
      const exception = new Error('error');
      const getAssessmentTakerCaptureShotsStub = sinon
        .stub(AssessmentService, 'getAssessmentTakerCaptureShots')
        .throwsException(exception);
      try {
        //  test function
        await getAssessmentTakerCaptureShots(reqMock, resMock, nextMock);
      } catch (error) {
        // assertion
        expect(getAssessmentTakerCaptureShotsStub.calledOnce).to.be.true;
        expect(nextMock.calledOnce).to.be.true;
        expect(error).to.be.instanceOf(Error);
      }
    });
  });

  // describe('Sample Test Marking', () => {
  //   let reqMock: ReqMock;
  //   let resMock: ResMock;
  //   let nextMock: sinon.SinonStub;
  //   beforeEach(() => {
  //     reqMock = mockReq({
  //       assessmentTakerInfo: {} as AssessmentTaker,
  //       body: { id: 'fake-assessmentTaker-Id' },
  //     });
  //     resMock = mockRes();

  //     nextMock = sinon.stub();
  //   });

  //   after(() => {
  //     sinon.restore();
  //   });

  //   it('Should return status of 201 with data', async () => {
  //     const sampleTestMarkingStub = sinon
  //       .stub(
  //         AssessmentService,
  //         // eslint-disable-next-line
  //         // @ts-ignore
  //         'sampleTestMarking'
  //       )
  //       .resolves(sampleTestMarkingResponse);

  //     // test function
  //     await sampleTestMarking(reqMock, resMock, nextMock);

  //     // assertion
  //     expect(sampleTestMarkingStub.calledOnce).to.be.true;
  //     expect(resMock.status.calledWithExactly(201)).to.be.true;
  //     expect(
  //       resMock.json.calledWith({
  //         success: true,
  //         data: { testResult: sampleTestMarkingResponse },
  //       })
  //     ).to.be.true;
  //     expect(nextMock.notCalled).to.be.true;

  //     sampleTestMarkingStub.restore();
  //   });

  //   it('Should call next function on error', async () => {
  //     const exception = new Error('error');
  //     const sampleTestMarkingStub = sinon
  //       .stub(
  //         AssessmentService,
  //         // eslint-disable-next-line
  //         // @ts-ignore
  //         'sampleTestMarking'
  //       )
  //       .throwsException(exception);
  //     try {
  //       //  test function
  //       await sampleTestMarking(reqMock, resMock, nextMock);
  //     } catch (error) {
  //       // assertion
  //       expect(sampleTestMarkingStub.calledOnce).to.be.true;
  //       expect(nextMock.calledOnce).to.be.true;
  //       expect(error).to.be.instanceOf(Error);
  //     }
  //   });
  // });

  describe('Assessment Taker Result', () => {
    let reqMock: ReqMock;
    let resMock: ResMock;
    let nextMock: sinon.SinonStub;
    beforeEach(() => {
      reqMock = mockReq({
        assessmentTakerInfo: {} as AssessmentTaker,
      });
      resMock = mockRes();

      nextMock = sinon.stub();
    });

    after(() => {
      sinon.restore();
    });

    it('Should return status of 200 with data', async () => {
      const getAssessmentTakingStatusStub = sinon
        .stub(
          AssessmentService,
          'getAssessmentTakingStatus'
          // eslint-disable-next-line
          // @ts-ignore
        )
        .resolves({ status: 'completed' });

      // test function
      await getAssessmentTakingStatus(reqMock, resMock, nextMock);

      // assertion
      expect(getAssessmentTakingStatusStub.calledOnce).to.be.true;
      expect(resMock.status.calledWithExactly(200)).to.be.true;
      expect(
        resMock.json.calledWith({
          success: true,
          data: { status: 'completed' },
        })
      ).to.be.true;
      expect(nextMock.notCalled).to.be.true;

      getAssessmentTakingStatusStub.restore();
    });

    it('Should call next function on error', async () => {
      const exception = new Error('error');
      const getAssessmentTakingStatusStub = sinon
        .stub(AssessmentService, 'getAssessmentTakingStatus')
        .throwsException(exception);
      try {
        //  test function
        await getAssessmentTakingStatus(reqMock, resMock, nextMock);
      } catch (error) {
        // assertion
        expect(getAssessmentTakingStatusStub.calledOnce).to.be.true;
        expect(nextMock.calledOnce).to.be.true;
        expect(error).to.be.instanceOf(Error);
      }
    });
  });

  describe('Submit Assessment test', () => {
    let reqMock: ReqMock;
    let resMock: ResMock;
    let nextMock: sinon.SinonStub;
    beforeEach(() => {
      reqMock = mockReq({
        assessmentTakerInfo: {} as AssessmentTaker,
      });
      resMock = mockRes();

      nextMock = sinon.stub();
    });

    after(() => {
      sinon.restore();
    });

    it('Should return status of 200 with message', async () => {
      const submitAssessmentTestStub = sinon
        .stub(
          AssessmentService,
          'submitAssessmentTest'
          // eslint-disable-next-line
          // @ts-ignore
        )
        .resolves({ title: 'Fake-test-title' });

      // test function
      await submitAssessmentTest(reqMock, resMock, nextMock);

      // assertion
      expect(submitAssessmentTestStub.calledOnce).to.be.true;
      expect(resMock.status.calledWithExactly(200)).to.be.true;
      expect(
        resMock.json.calledWith({
          success: true,
          data: { message: 'Fake-test-title Test submitted successfully' },
        })
      ).to.be.true;
      expect(nextMock.notCalled).to.be.true;

      submitAssessmentTestStub.restore();
    });

    it('Should call next function on error', async () => {
      const exception = new Error('error');
      const submitAssessmentTestStub = sinon
        .stub(AssessmentService, 'submitAssessmentTest')
        .throwsException(exception);
      try {
        //  test function
        await submitAssessmentTest(reqMock, resMock, nextMock);
      } catch (error) {
        // assertion
        expect(submitAssessmentTestStub.calledOnce).to.be.true;
        expect(nextMock.calledOnce).to.be.true;
        expect(error).to.be.instanceOf(Error);
      }
    });
  });

  describe('Draft Candidate Progress', () => {
    let reqMock: ReqMock;
    let resMock: ResMock;
    let nextMock: sinon.SinonStub;
    beforeEach(() => {
      reqMock = mockReq({
        assessmentTakerInfo: {} as AssessmentTaker,
      });
      resMock = mockRes();

      nextMock = sinon.stub();
    });

    after(() => {
      sinon.restore();
    });

    it('Should return status of 201 with message', async () => {
      const draftCandidateProgressStub = sinon
        .stub(AssessmentService, 'draftCandidateProgress')
        .resolves();

      // test function
      await draftCandidateProgress(reqMock, resMock, nextMock);

      expect(draftCandidateProgressStub.calledOnce).to.be.true;
      expect(resMock.status.calledWithExactly(201)).to.be.true;
      expect(
        resMock.json.calledWith({
          success: true,
          message: 'Progress drafted',
        })
      ).to.be.true;
      expect(nextMock.notCalled).to.be.true;

      draftCandidateProgressStub.restore();
    });

    it('Should call next function on error', async () => {
      const exception = new Error('error');
      const draftCandidateProgressStub = sinon
        .stub(AssessmentService, 'draftCandidateProgress')
        .throwsException(exception);
      try {
        //  test function
        await draftCandidateProgress(reqMock, resMock, nextMock);
      } catch (error) {
        // assertion
        expect(draftCandidateProgressStub.calledOnce).to.be.true;
        expect(nextMock.calledOnce).to.be.true;
        expect(error).to.be.instanceOf(Error);
      }
    });
  });

  describe('getCandidatePreSignedURL', () => {
    let reqMock: ReqMock;
    let resMock: ResMock;
    let nextMock: sinon.SinonStub;
    beforeEach(() => {
      reqMock = mockReq({
        assessmentTakerInfo: { id: 'fake-candidate-id' } as AssessmentTaker,
        query: { mimeType: 'image/png', keyType: 'test' },
      });
      resMock = mockRes();

      nextMock = sinon.stub();
    });

    after(() => {
      sinon.restore();
    });
    it('should return status of 200 with message', async () => {
      const getCandidatePreSignedURLStub = sinon
        .stub(AssessmentService, 'getCandidatePreSignedURL')
        .resolves({
          url: 'https://presigned.com',
          location: 'https://location.com',
          headers: {
            'Content-Type': 'image/png',
            'Content-MD5': 'mock-content-md5',
            'x-amz-object-lock-mode': 'GOVERNANCE',
            'x-amz-object-lock-retain-until-date': '2023-01-01T00:00:00.000Z',
          },
        });

      const dateStub = sinon
        .stub(Date.prototype, 'toISOString')
        .returns('2023-01-01T00:00:00.000Z');
      // test function
      await getCandidatePreSignedURL(reqMock, resMock, nextMock);

      expect(getCandidatePreSignedURLStub.calledOnce).to.be.true;
      expect(resMock.status.calledWithExactly(200)).to.be.true;
      expect(resMock.json.calledOnce).to.be.true;
      expect(resMock.json.args[0][0]).to.deep.equal({
        success: true,
        data: {
          url: 'https://presigned.com',
          location: 'https://location.com',
          headers: {
            'Content-Type': 'image/png',
            'Content-MD5': 'mock-content-md5',
            'x-amz-object-lock-mode': 'GOVERNANCE',
            'x-amz-object-lock-retain-until-date': '2023-01-01T00:00:00.000Z',
          },
        },
      });
      expect(resMock.json.calledWith()).to.be.true;
      expect(nextMock.notCalled).to.be.true;

      getCandidatePreSignedURLStub.restore();
      dateStub.restore();
    });
    it('should call next function on error', async () => {
      const exception = new Error('some-error');
      const getCandidatePreSignedURLStub = sinon
        .stub(AssessmentService, 'getCandidatePreSignedURL')
        .rejects(exception);
      await getCandidatePreSignedURL(reqMock, resMock, nextMock);
      expect(getCandidatePreSignedURLStub.calledOnce).to.be.true;
      expect(nextMock.calledOnce).to.be.true;
      expect(nextMock.calledWith(exception)).to.be.true;

      getCandidatePreSignedURLStub.restore();
    });
  });
  describe('getCandidateAssessmentsInfo', () => {
    let reqMock: ReqMock;
    let resMock: ResMock;
    let nextMock: sinon.SinonStub;

    beforeEach(() => {
      reqMock = mockReq({
        body: {
          assessmentIds: ['assessment-id-1', 'assessment-id-2'],
          email: '<EMAIL>',
        },
      }) as unknown as ReqMock;
      resMock = mockRes();
      nextMock = sinon.stub();
    });

    afterEach(() => {
      sinon.restore();
    });

    it('should return status 200 with candidate assessments data', async () => {
      const mockCandidateAssessments = [
        { id: 'assessment-id-1', name: 'Assessment 1' },
        { id: 'assessment-id-2', name: 'Assessment 2' },
      ];

      const getCandidateAssessmentsInfoStub = sinon
        .stub(AssessmentService, 'getCandidateAssessmentsInfo')
        .resolves(mockCandidateAssessments as unknown as AssessmentResult[]);

      await getCandidateAssessmentsInfo(reqMock, resMock, nextMock);

      expect(getCandidateAssessmentsInfoStub.calledOnce).to.be.true;
      expect(
        getCandidateAssessmentsInfoStub.calledWithExactly(
          reqMock.body.assessmentIds,
          reqMock.body.email
        )
      ).to.be.true;
      expect(resMock.status.calledWithExactly(200)).to.be.true;
      expect(
        resMock.send.calledWithExactly({
          success: true,
          data: {
            candidateAssessments: mockCandidateAssessments,
          },
        })
      ).to.be.true;
      expect(nextMock.notCalled).to.be.true;

      getCandidateAssessmentsInfoStub.restore();
    });

    it('should call next with AppError when request body is invalid', async () => {
      reqMock.body = { assessmentIds: null, email: '' };

      await getCandidateAssessmentsInfo(reqMock, resMock, nextMock);

      expect(nextMock.calledOnce).to.be.true;
      const error = nextMock.args[0][0];
      expect(error).to.be.instanceOf(AppError);
      expect(error.httpCode).to.equal(400);
    });

    it('should call next with error when service throws an exception', async () => {
      const exception = new Error('Service error');
      const getCandidateAssessmentsInfoStub = sinon
        .stub(AssessmentService, 'getCandidateAssessmentsInfo')
        .throwsException(exception);

      await getCandidateAssessmentsInfo(reqMock, resMock, nextMock);

      expect(getCandidateAssessmentsInfoStub.calledOnce).to.be.true;
      expect(nextMock.calledOnce).to.be.true;
      expect(nextMock.calledWith(exception)).to.be.true;

      getCandidateAssessmentsInfoStub.restore();
    });
  });
});
