import Sinon from 'sinon';
import { AssessmentService } from '../../src/services/assessment';
import {
  assessmentTakerDependencyMockData,
  fakeAssessmentTaker,
  assessmentTakerDependencyMockMarkData,
  markQuestionResultMock,
  fakeIdentity,
  fakeScreenShotListData,
  fakeAssessmentTakerCaptureListData,
  fakeTestResult,
} from '../fakeData/mockresponse';
import {
  AssessmentInProgressDto,
  PresignedURLDto,
} from '../../src/helpers/types';
import { expect } from 'chai';
import { AppError, HttpCode } from '../../src/middlewares/errorHandler';
import prisma from '../../src/prisma';

import {
  fakeCandidateDraftPayload,
  submitAssessmentMockPayload,
} from '../fakeData/mockPayload';
import { MarkQuestion } from '../../src/helpers/markQuestion';
import { sampleTestDtoMock } from '../fakeData/mockarguments';
import { AssessmentTrackStatus } from '../../src/helpers/assessmentTrack';
import { DraftRepository } from '../../src/repository/draftRepository';
import { TestResultRepository } from '../../src/repository/testResultRepository';
import S3KeysGenerator from '../../src/helpers/s3Keys';
import s3Service from '../../src/helpers/awss3Service';
import proxyquire from 'proxyquire';

describe('Assessment Service', () => {
  describe('progress', () => {
    let assessmentService;
    let prismaMock;
    let redisCacheMock;
    let dataRestructorMock;
    let kafkaPublishMessageMock;
    let assessmentTakingLogsMock;

    // Create stubs
    let getAssessmentTakerDependencyDataStub;
    let assessmentRestructorStub;
    let assessmentInProgressPublishStub;
    let createAssessmentLogStub;
    let setOrExtendMarkCachedDataStub;
    let prismaAssessmentTakerUpdateStub;

    beforeEach(() => {
      // Set up stubs
      getAssessmentTakerDependencyDataStub = Sinon.stub().resolves(
        assessmentTakerDependencyMockData
      );
      assessmentRestructorStub = Sinon.stub().resolves(
        assessmentTakerDependencyMockData.assessmentTaker.assessment
      );
      assessmentInProgressPublishStub = Sinon.stub().resolves();
      createAssessmentLogStub = Sinon.stub();
      setOrExtendMarkCachedDataStub = Sinon.stub();
      prismaAssessmentTakerUpdateStub =
        Sinon.stub().resolves(fakeAssessmentTaker);

      // Create mocks
      redisCacheMock = {
        getAssessmentTakerDependencyData: getAssessmentTakerDependencyDataStub,
        setOrExtendMarkCachedData: setOrExtendMarkCachedDataStub,
      };

      dataRestructorMock = {
        assessmentRestructor: assessmentRestructorStub,
      };

      kafkaPublishMessageMock = {
        assessmentInProgressPublish: assessmentInProgressPublishStub,
      };

      assessmentTakingLogsMock = {
        createAssessmentLog: createAssessmentLogStub,
      };

      prismaMock = {
        assessmentTaker: {
          create: Sinon.stub().resolves(fakeAssessmentTaker),
          update: prismaAssessmentTakerUpdateStub,
        },
      };

      // Use proxyquire to inject mocks
      const AssessmentServiceModule = proxyquire(
        '../../src/services/assessment',
        {
          '../../src/prisma': { default: prismaMock },
          '../../src/helpers/redisCache': redisCacheMock,
          '../../src/helpers/dataRestructor': dataRestructorMock,
          '../../src/helpers/kafkaPublishMessage': kafkaPublishMessageMock,
          '../../src/helpers/assessmentTakingLogs': assessmentTakingLogsMock,
        }
      );

      assessmentService = AssessmentServiceModule.AssessmentService;
    });

    afterEach(() => {
      Sinon.restore();
    });

    it('Should throw error if already is in progress', async () => {
      try {
        await assessmentService.progress(
          fakeAssessmentTaker,
          {} as AssessmentInProgressDto
        );
      } catch (error) {
        expect(error).to.be.instanceOf(AppError);
        expect(error.message).to.equal('Assessment already in progress');
      }
    });

    it('Should mark candidate assessment as in progress', async () => {
      // Test function
      await assessmentService.progress(
        { ...fakeAssessmentTaker, logSummary: [] },
        {} as AssessmentInProgressDto
      );

      // Assertions
      expect(getAssessmentTakerDependencyDataStub.calledOnce).to.be.true;
      expect(assessmentRestructorStub.calledOnce).to.be.true;
      expect(assessmentInProgressPublishStub.calledOnce).to.be.true;
      expect(createAssessmentLogStub.calledOnce).to.be.true;
      expect(setOrExtendMarkCachedDataStub.calledOnce).to.be.true;
    });
  });

  describe('Assessment Service', () => {
    describe('submitAssessmentTest', () => {
      // Set up mocks and stubs
      let assessmentService;
      let redisCacheMock;
      let testBaseOperationMock;

      // Create stubs
      let getAssessmentTakerDependencyDataStub;
      let getAssessmentTakerDependencyDataMarkStub;
      let testSubmissionProcessorStub;

      beforeEach(() => {
        // Set up stubs
        getAssessmentTakerDependencyDataStub = Sinon.stub().resolves(
          assessmentTakerDependencyMockData
        );
        getAssessmentTakerDependencyDataMarkStub = Sinon.stub().resolves(
          assessmentTakerDependencyMockMarkData
        );
        testSubmissionProcessorStub = Sinon.stub().resolves();

        // Create mocks
        redisCacheMock = {
          getAssessmentTakerDependencyData:
            getAssessmentTakerDependencyDataStub,
          getAssessmentTakerDependencyDataMark:
            getAssessmentTakerDependencyDataMarkStub,
        };

        testBaseOperationMock = {
          testSubmissionProcessor: testSubmissionProcessorStub,
        };

        // Use proxyquire to inject mocks
        const AssessmentServiceModule = proxyquire(
          '../../src/services/assessment',
          {
            '../../src/helpers/redisCache': redisCacheMock,
            '../../src/helpers/testBaseOperation': testBaseOperationMock,
          }
        );

        assessmentService = AssessmentServiceModule.AssessmentService;
      });

      afterEach(() => {
        Sinon.restore();
      });

      it('Should throw error if section is already submitted', async () => {
        try {
          await assessmentService.submitAssessmentTest(
            fakeAssessmentTaker,
            submitAssessmentMockPayload
          );
        } catch (error) {
          expect(error).to.be.instanceOf(AppError);
          expect(error.message).to.equal('Section already submitted');
        }
      });

      it('Should throw error if section does not exist', async () => {
        const oldTestId = submitAssessmentMockPayload.testResults[0].testId;
        submitAssessmentMockPayload.testResults[0].testId = 'some-test-id';

        try {
          await assessmentService.submitAssessmentTest(
            { ...fakeAssessmentTaker, logSummary: [], submittedTests: [] },
            submitAssessmentMockPayload
          );

          expect(getAssessmentTakerDependencyDataStub.calledOnce).to.be.true;
        } catch (error) {
          expect(error).to.be.instanceOf(AppError);
          expect(error.message).to.equal('Section does not exist');
        }

        // Reset for other tests
        submitAssessmentMockPayload.testResults[0].testId = oldTestId;
      });

      it('Should throw error when expected number of questions are not submitted', async () => {
        const oldTestQuestions =
          submitAssessmentMockPayload.testResults[0].questionResults;
        submitAssessmentMockPayload.testResults[0].questionResults = [];

        try {
          await assessmentService.submitAssessmentTest(
            { ...fakeAssessmentTaker, logSummary: [], submittedTests: [] },
            submitAssessmentMockPayload
          );
        } catch (error) {
          expect(error).to.be.instanceOf(AppError);
          expect(error.message).to.equal(
            'Provide all question data for this section'
          );
        }

        // Reset for other tests
        submitAssessmentMockPayload.testResults[0].questionResults =
          oldTestQuestions;
      });

      it('Should save submitted Test', async () => {
        await assessmentService.submitAssessmentTest(
          { ...fakeAssessmentTaker, logSummary: [], submittedTests: [] },
          submitAssessmentMockPayload
        );

        expect(getAssessmentTakerDependencyDataStub.calledOnce).to.be.true;
        expect(getAssessmentTakerDependencyDataMarkStub.calledOnce).to.be.true;
        expect(testSubmissionProcessorStub.calledOnce).to.be.true;
      });
    });

    describe('submitAssessment', () => {
      // Set up mocks and stubs
      let assessmentService;
      let redisCacheMock;
      let testBaseOperationMock;
      let assessmentProcessMock;
      let assessmentReportMock;

      // Create stubs
      let getAssessmentTakerDependencyDataStub;
      let getAssessmentTakerDependencyDataMarkStub;
      let testSubmissionProcessorStub;
      let markDraftedQuestionsStub;
      let sendAssessmentReportStub;

      beforeEach(() => {
        // Set up stubs
        getAssessmentTakerDependencyDataStub = Sinon.stub().resolves(
          assessmentTakerDependencyMockData
        );
        getAssessmentTakerDependencyDataMarkStub = Sinon.stub().resolves(
          assessmentTakerDependencyMockMarkData
        );
        testSubmissionProcessorStub = Sinon.stub().resolves();
        markDraftedQuestionsStub = Sinon.stub().resolves();
        sendAssessmentReportStub = Sinon.stub().resolves();

        // Create mocks
        redisCacheMock = {
          getAssessmentTakerDependencyData:
            getAssessmentTakerDependencyDataStub,
          getAssessmentTakerDependencyDataMark:
            getAssessmentTakerDependencyDataMarkStub,
        };

        testBaseOperationMock = {
          testSubmissionProcessor: testSubmissionProcessorStub,
        };

        assessmentProcessMock = {
          markDraftedQuestions: markDraftedQuestionsStub,
        };

        assessmentReportMock = {
          sendAssessmentReport: sendAssessmentReportStub,
        };

        // Use proxyquire to inject mocks
        const AssessmentServiceModule = proxyquire(
          '../../src/services/assessment',
          {
            '../../src/helpers/redisCache': redisCacheMock,
            '../../src/helpers/testBaseOperation': testBaseOperationMock,
            '../../src/helpers/assessmentProcess': {
              AssessmentProcess: assessmentProcessMock,
            },
            '../../src/helpers/assessmentReport': {
              AssessmentReport: assessmentReportMock,
            },
          }
        );

        assessmentService = AssessmentServiceModule.AssessmentService;
      });

      afterEach(() => {
        Sinon.restore();
      });

      it('Should throw error when assessment is already progress', async () => {
        try {
          await assessmentService.submitAssessment(
            submitAssessmentMockPayload,
            {
              ...fakeAssessmentTaker,
              logSummary: ['ASSESSMENT SUBMISSION'],
            }
          );
        } catch (error) {
          expect(error).to.be.instanceOf(AppError);
          expect(error.message).to.equal('Assessment already submitted');
        }
      });

      it('Should throw error when last section/test submitted', async () => {
        try {
          await assessmentService.submitAssessment(
            submitAssessmentMockPayload,
            {
              ...fakeAssessmentTaker,
              logSummary: [],
            }
          );
        } catch (error) {
          expect(error).to.be.instanceOf(AppError);
          expect(error.message).to.equal('Section already submitted');
        }
      });

      it('Should throw error when section/test does not exist', async () => {
        const oldTestId = submitAssessmentMockPayload.testResults[0].testId;
        submitAssessmentMockPayload.testResults[0].testId = 'some-test-id';

        try {
          await assessmentService.submitAssessment(
            submitAssessmentMockPayload,
            {
              ...fakeAssessmentTaker,
              logSummary: [],
            }
          );
        } catch (error) {
          expect(error).to.be.instanceOf(AppError);
          expect(error.message).to.equal('Section does not exist');
        }

        // Reset for other tests
        submitAssessmentMockPayload.testResults[0].testId = oldTestId;
      });

      it('Should throw error when expected number of questions are not submitted', async () => {
        const oldTestQuestions =
          submitAssessmentMockPayload.testResults[0].questionResults;
        submitAssessmentMockPayload.testResults[0].questionResults = [];

        try {
          await assessmentService.submitAssessment(
            submitAssessmentMockPayload,
            {
              ...fakeAssessmentTaker,
              logSummary: [],
              submittedTests: [],
            }
          );
        } catch (error) {
          expect(error).to.be.instanceOf(AppError);
          expect(error.message).to.equal(
            'Provide all question data for this section'
          );
        }

        // Reset for other tests
        submitAssessmentMockPayload.testResults[0].questionResults =
          oldTestQuestions;
      });

      it('Should save submitted Test', async () => {
        await assessmentService.submitAssessment(submitAssessmentMockPayload, {
          ...fakeAssessmentTaker,
          logSummary: [],
          submittedTests: [],
        });

        expect(getAssessmentTakerDependencyDataStub.calledOnce).to.be.true;
        expect(getAssessmentTakerDependencyDataMarkStub.calledOnce).to.be.true;
        expect(testSubmissionProcessorStub.calledOnce).to.be.true;
        expect(markDraftedQuestionsStub.calledOnce).to.be.true;
        expect(sendAssessmentReportStub.calledOnce).to.be.true;
      });
    });
  });

  describe('sampleTestMarking', () => {
    it('Should mark sample test', async () => {
      const singleChoiceTypeStub = Sinon.stub(
        MarkQuestion,
        'singleChoiceType'
      ).returns(markQuestionResultMock);

      const response = AssessmentService.sampleTestMarking(sampleTestDtoMock);

      expect(singleChoiceTypeStub.callCount).to.equal(
        sampleTestDtoMock.test.questions.length
      );
      expect(response.testResult).to.be.a('array');
      expect(response.testScore).to.be.a('number');
      expect(response.testTakerPercentageScore).to.be.a('string');
      expect(response.testTakerScore).to.be.a('number');

      singleChoiceTypeStub.restore();
    });
  });

  describe('getAssessmentTakingStatus', () => {
    afterEach(() => {
      Sinon.restore();
    });
    it('Should register new candidate', async () => {
      const registerNewCandidateStub = Sinon.stub(
        AssessmentTrackStatus,
        'registerNewCandidate'
      ).resolves();

      prisma.assessmentTaker.findFirst = Sinon.stub().resolves(null);
      await AssessmentService.getAssessmentTakingStatus(fakeAssessmentTaker.id);

      expect(registerNewCandidateStub.calledWith(fakeAssessmentTaker.id)).to.be
        .true;

      registerNewCandidateStub.restore();
    });

    it('Should get current candidate phase for old candidate', async () => {
      const getCandidateCurrentPhaseStub = Sinon.stub(
        AssessmentTrackStatus,
        'getCandidateCurrentPhase'
      ).resolves();

      prisma.assessmentTaker.findFirst =
        Sinon.stub().resolves(fakeAssessmentTaker);
      await AssessmentService.getAssessmentTakingStatus(fakeAssessmentTaker.id);

      expect(getCandidateCurrentPhaseStub.calledWith(fakeAssessmentTaker)).to.be
        .true;
    });
  });

  describe('getAssessmentIdentityInfo', () => {
    // Set up mocks and stubs
    let assessmentService;
    let redisCacheMock;
    let prismaMock;

    // Create stubs
    let getCachedDataStub;
    let setCacheDataStub;
    let prismaIdentityFindFirstStub;

    beforeEach(() => {
      // Set up stubs
      getCachedDataStub = Sinon.stub();
      setCacheDataStub = Sinon.stub().resolves();
      prismaIdentityFindFirstStub = Sinon.stub().resolves(fakeIdentity);

      // Create mocks
      redisCacheMock = {
        getCachedData: getCachedDataStub,
        setCacheData: setCacheDataStub,
      };

      prismaMock = {
        identity: {
          findFirst: prismaIdentityFindFirstStub,
        },
      };

      // Use proxyquire to inject mocks
      const AssessmentServiceModule = proxyquire(
        '../../src/services/assessment',
        {
          '../../src/prisma': { default: prismaMock },
          '../../src/helpers/redisCache': redisCacheMock,
        }
      );

      assessmentService = AssessmentServiceModule.AssessmentService;
    });

    afterEach(() => {
      Sinon.restore();
    });

    it('Should return identity info from database', async () => {
      // Setup stub behavior
      getCachedDataStub.resolves();

      const response = await assessmentService.getAssessmentIdentityInfo(
        fakeAssessmentTaker.id
      );

      expect(
        getCachedDataStub.calledWith(
          `assessmentTaker-${fakeAssessmentTaker.id}-identity`
        )
      ).to.be.true;
      expect(setCacheDataStub.args[0][0]).to.equal(
        `assessmentTaker-${fakeAssessmentTaker.id}-identity`
      );
      expect(setCacheDataStub.args[0][1]).to.deep.equal(fakeIdentity);
      expect(setCacheDataStub.args[0][2]).to.equal(3600);
      expect(response).to.deep.equal(fakeIdentity);
    });

    it('Should return identity info from memory', async () => {
      // Setup stub behavior
      getCachedDataStub.resolves(fakeIdentity);

      const response = await assessmentService.getAssessmentIdentityInfo(
        fakeAssessmentTaker.id
      );

      expect(
        getCachedDataStub.calledWith(
          `assessmentTaker-${fakeAssessmentTaker.id}-identity`
        )
      ).to.be.true;
      expect(response).to.deep.equal(fakeIdentity);
    });
  });

  describe('getAssessmentTakerScreenshot', () => {
    // Set up mocks and stubs
    let assessmentService;
    let redisCacheMock;
    let prismaMock;

    // Create stubs
    let getCachedDataStub;
    let setCacheDataStub;
    let prismaScreenShotFindManyStub;

    beforeEach(() => {
      // Set up stubs
      getCachedDataStub = Sinon.stub();
      setCacheDataStub = Sinon.stub().resolves();
      prismaScreenShotFindManyStub = Sinon.stub().resolves(
        fakeScreenShotListData
      );

      // Create mocks
      redisCacheMock = {
        getCachedData: getCachedDataStub,
        setCacheData: setCacheDataStub,
      };

      prismaMock = {
        screenShot: {
          findMany: prismaScreenShotFindManyStub,
        },
      };

      // Use proxyquire to inject mocks
      const AssessmentServiceModule = proxyquire(
        '../../src/services/assessment',
        {
          '../../src/prisma': { default: prismaMock },
          '../../src/helpers/redisCache': redisCacheMock,
        }
      );

      assessmentService = AssessmentServiceModule.AssessmentService;
    });

    afterEach(() => {
      Sinon.restore();
    });

    it('Should return screenshots from database', async () => {
      // Setup stub behavior
      getCachedDataStub.resolves();

      const response = await assessmentService.getAssessmentTakerScreenshot(
        fakeAssessmentTaker.id
      );

      expect(response).to.deep.equal(fakeScreenShotListData);
      expect(
        getCachedDataStub.calledWith(
          `assessmentTaker-${fakeAssessmentTaker.id}-screenShots`
        )
      ).to.be.true;
      expect(setCacheDataStub.args[0][0]).to.equal(
        `assessmentTaker-${fakeAssessmentTaker.id}-screenShots`
      );
      expect(setCacheDataStub.args[0][1]).to.deep.equal(fakeScreenShotListData);
      expect(setCacheDataStub.args[0][2]).to.equal(3600);
    });

    it('Should return screenshots from memory', async () => {
      // Setup stub behavior
      getCachedDataStub.resolves(fakeScreenShotListData);

      const response = await assessmentService.getAssessmentTakerScreenshot(
        fakeAssessmentTaker.id
      );

      expect(response).to.deep.equal(fakeScreenShotListData);
      expect(
        getCachedDataStub.calledWith(
          `assessmentTaker-${fakeAssessmentTaker.id}-screenShots`
        )
      ).to.be.true;
    });
  });

  describe('getAssessmentTakerCaptureShots', () => {
    // Set up mocks and stubs
    let assessmentService;
    let redisCacheMock;
    let prismaMock;

    // Create stubs
    let getCachedDataStub;
    let setCacheDataStub;
    let prismaAssessmentTakerCaptureFindManyStub;

    beforeEach(() => {
      // Set up stubs
      getCachedDataStub = Sinon.stub();
      setCacheDataStub = Sinon.stub().resolves();
      prismaAssessmentTakerCaptureFindManyStub = Sinon.stub().resolves(
        fakeAssessmentTakerCaptureListData
      );

      // Create mocks
      redisCacheMock = {
        getCachedData: getCachedDataStub,
        setCacheData: setCacheDataStub,
      };

      prismaMock = {
        assessmentTakerCapture: {
          findMany: prismaAssessmentTakerCaptureFindManyStub,
        },
      };

      // Use proxyquire to inject mocks
      const AssessmentServiceModule = proxyquire(
        '../../src/services/assessment',
        {
          '../../src/prisma': { default: prismaMock },
          '../../src/helpers/redisCache': redisCacheMock,
        }
      );

      assessmentService = AssessmentServiceModule.AssessmentService;
    });

    afterEach(() => {
      Sinon.restore();
    });

    it('Should return candidate capture shots from database', async () => {
      // Setup stub behavior
      getCachedDataStub.resolves();

      const response = await assessmentService.getAssessmentTakerCaptureShots(
        fakeAssessmentTaker.id
      );

      expect(response).to.deep.equal(fakeAssessmentTakerCaptureListData);
      expect(
        getCachedDataStub.calledWith(
          `assessmentTaker-${fakeAssessmentTaker.id}-captureShots`
        )
      ).to.be.true;
      expect(setCacheDataStub.args[0][0]).to.equal(
        `assessmentTaker-${fakeAssessmentTaker.id}-captureShots`
      );
      expect(setCacheDataStub.args[0][1]).to.deep.equal(
        fakeAssessmentTakerCaptureListData
      );
      expect(setCacheDataStub.args[0][2]).to.equal(3600);
    });

    it('Should return candidate capture shots from memory', async () => {
      // Setup stub behavior
      getCachedDataStub.resolves(fakeAssessmentTakerCaptureListData);

      const response = await assessmentService.getAssessmentTakerCaptureShots(
        fakeAssessmentTaker.id
      );

      expect(response).to.deep.equal(fakeAssessmentTakerCaptureListData);
      expect(
        getCachedDataStub.calledWith(
          `assessmentTaker-${fakeAssessmentTaker.id}-captureShots`
        )
      ).to.be.true;
    });
  });

  describe('getAssessmentTakerCaptureShots', () => {
    afterEach(() => {
      Sinon.restore();
    });

    it('Should throw error if assessment is not configured to show results', async () => {
      try {
        await AssessmentService.getAssessmentTakerResult({
          ...fakeAssessmentTaker,
          showResults: false,
        });
      } catch (error) {
        expect(error).to.be.instanceOf(AppError);
        expect(error.message).to.be.equal(
          'You are not authorized to view this assessment result. Please ensure you have the necessary permissions.'
        );
      }
    });

    it('Should return candidate results', async () => {
      prisma.testResult.findMany = Sinon.stub().resolves(fakeTestResult);

      const response = await AssessmentService.getAssessmentTakerResult(
        fakeAssessmentTaker
      );

      expect(response.id).to.equal(fakeAssessmentTaker.id);
      expect(response.assessmentName).to.equal(
        fakeAssessmentTaker.assessmentName as string
      );
      expect(response.testResults).to.be.an('array');
      expect(response.overallAssessmentPassScore).to.be.a('number');
      expect(response.overallAssessmentPercentage).to.be.a('number');
      expect(response.overallAssessmentScore).to.be.a('number');
    });
  });

  describe('draftCandidateProgress', () => {
    it('Should draft candidate progress ', async () => {
      const createOrUpdateStringArrayAnswerStub = Sinon.stub(
        DraftRepository,
        'createOrUpdateStringArrayAnswer'
      ).resolves();
      const createOrUpdateMatchMatrixAnswer = Sinon.stub(
        DraftRepository,
        'createOrUpdateMatchMatrixAnswer'
      ).resolves();

      const draftProctoringDataStub = Sinon.stub(
        TestResultRepository,
        'draftProctoringData'
      ).resolves();

      await AssessmentService.draftCandidateProgress(
        fakeAssessmentTaker,
        fakeCandidateDraftPayload
      );

      expect(createOrUpdateStringArrayAnswerStub.calledOnce).to.be.true;
      expect(draftProctoringDataStub.called).to.be.true;
      expect(createOrUpdateMatchMatrixAnswer.called).to.be.true;

      createOrUpdateStringArrayAnswerStub.restore();
      createOrUpdateMatchMatrixAnswer.restore();
      draftProctoringDataStub.restore();
    });
  });

  describe('getCandidatePreSignedURL', () => {
    const query: PresignedURLDto = {
      mimeType: 'image/png',
      keyType: 'head-shot',
      contentMD5: 'mock-content-md5',
    };

    let getKeyStub: sinon.SinonStub;
    let getPreSignedURLStub: sinon.SinonStub;
    let getFileLocationStub: sinon.SinonStub;

    beforeEach(() => {
      getKeyStub = Sinon.stub(S3KeysGenerator, 'getKey');
      getPreSignedURLStub = Sinon.stub(s3Service, 'getPreSignedURL');
      getFileLocationStub = Sinon.stub(s3Service, 'getFileLocation');
    });

    afterEach(() => {
      Sinon.restore();
    });

    it('should throw an error if the candidate is not authorized', async () => {
      try {
        await AssessmentService.getCandidatePreSignedURL(
          { ...fakeAssessmentTaker, proctorFeatures: [] },
          query
        );
      } catch (error) {
        expect(error).to.be.instanceOf(AppError);
        expect(error.httpCode).to.equal(HttpCode.UNAUTHORIZED);
        expect(error.message).to.equal(
          'You not authorize to make this request'
        );
      }
    });

    it('should call S3KeysGenerator and s3Service to generate a URL', async () => {
      const mockKey = 'mock-s3-key';
      const mockUrl = 'https://mock-s3-url';
      const mockHeaders = {
        'Content-Type': 'image/png',
        'Content-MD5': 'mock-content-md5',
        'x-amz-object-lock-mode': 'GOVERNANCE',
        'x-amz-object-lock-retain-until-date': '2023-01-01T00:00:00.000Z',
      };
      const mockLocation = 'https://mock-s3-location';
      const mockgetPreSignedURLResponse = {
        url: mockUrl,
        headers: mockHeaders,
      };
      const contentMD5 = 'mock-content-md5';
      // Define stub behaviors
      getKeyStub.returns(mockKey);
      getPreSignedURLStub.resolves(mockgetPreSignedURLResponse);
      getFileLocationStub.returns(mockLocation);

      const result = await AssessmentService.getCandidatePreSignedURL(
        { ...fakeAssessmentTaker, proctorFeatures: ['ID Capture'] },
        query
      );

      expect(
        getKeyStub.calledOnceWithExactly(
          {
            assessmentName: fakeAssessmentTaker.assessmentName,
            assessmentTakerId: fakeAssessmentTaker.id,
            email: fakeAssessmentTaker.email,
            expiryDate: new Date(
              fakeAssessmentTaker.expireDate as Date
            ).toLocaleDateString('en-CA'),
            organizationId: fakeAssessmentTaker.organizationId,
          },
          query
        )
      ).to.be.true;
      expect(getFileLocationStub.calledOnceWithExactly(mockKey)).to.be.true;
      expect(
        getPreSignedURLStub.calledOnceWithExactly(
          mockKey,
          query.mimeType,
          contentMD5
        )
      ).to.be.true;
      expect(result).to.deep.equal({
        url: mockUrl,
        location: mockLocation,
        headers: mockHeaders,
      });
    });
  });
});
