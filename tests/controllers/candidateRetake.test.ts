import { expect, use } from 'chai';
import * as sinon from 'sinon';
import sinon<PERSON>hai from 'sinon-chai';
import proxyquire from 'proxyquire';
import { mockReq, mockRes } from 'sinon-express-mock';
import { AppError, HttpCode } from '../../src/middlewares/errorHandler';

use(sinonChai);

describe('Candidate Retake Controller', () => {
  let candidateRetakeController: any;
  let retakeDelayStub: any;
  let prismaStub: any;
  let sandbox: sinon.SinonSandbox;

  beforeEach(() => {
    sandbox = sinon.createSandbox();

    // Mock RetakeDelay static methods
    retakeDelayStub = {
      canRetakeAssessment: sandbox.stub(),
      getHoursRemaining: sandbox.stub(),
      getAssessmentRetakeDelay: sandbox.stub(),
    };

    // Mock Prisma
    prismaStub = {
      assessmentTaker: {
        create: sandbox.stub(),
        findMany: sandbox.stub(),
        count: sandbox.stub(),
      },
    };

    // Load the controller with mocked dependencies using proxyquire
    candidateRetakeController = proxyquire(
      '../../src/controllers/candidateRetake',
      {
        '../helpers/retakeDelay': { RetakeDelay: retakeDelayStub },
        '../prisma': { default: prismaStub },
        '@noCallThru': true,
      }
    );
  });

  afterEach(() => {
    sandbox.restore();
  });

  describe('checkRetakeEligibility', () => {
    it('should return eligible status when candidate can retake', async () => {
      const req = mockReq({
        body: {
          email: '<EMAIL>',
          assessmentId: 'assessment-123',
        },
      });
      const res = mockRes();
      const next = sandbox.stub();

      retakeDelayStub.canRetakeAssessment.resolves(true);
      retakeDelayStub.getHoursRemaining.resolves(0);
      retakeDelayStub.getAssessmentRetakeDelay.resolves(24);

      await candidateRetakeController.checkRetakeEligibility(req, res, next);

      expect(retakeDelayStub.canRetakeAssessment).to.have.been.calledWith(
        '<EMAIL>',
        'assessment-123'
      );
      expect(res.status).to.have.been.calledWith(200);
      expect(res.json).to.have.been.calledWith({
        success: true,
        data: sinon.match({
          canRetake: true,
          hoursRemaining: 0,
          retakeDelayHours: 24,
          checkedAt: sinon.match.string,
        }),
      });
      expect(next).to.not.have.been.called;
    });

    it('should return not eligible status when candidate cannot retake', async () => {
      const req = mockReq({
        body: {
          email: '<EMAIL>',
          assessmentId: 'assessment-123',
        },
      });
      const res = mockRes();
      const next = sandbox.stub();

      retakeDelayStub.canRetakeAssessment.resolves(false);
      retakeDelayStub.getHoursRemaining.resolves(12);
      retakeDelayStub.getAssessmentRetakeDelay.resolves(24);

      await candidateRetakeController.checkRetakeEligibility(req, res, next);

      expect(res.status).to.have.been.calledWith(200);
      expect(res.json).to.have.been.calledWith({
        success: true,
        data: sinon.match({
          canRetake: false,
          hoursRemaining: 12,
          retakeDelayHours: 24,
          checkedAt: sinon.match.string,
          nextRetakeAvailable: sinon.match.string,
          message: sinon.match.string,
        }),
      });
    });

    it.skip('should throw error when assessmentId is missing', async () => {
      const req = mockReq({
        body: {
          email: '<EMAIL>',
        },
      });
      const res = mockRes();
      const next = sandbox.stub();

      await candidateRetakeController.checkRetakeEligibility(req, res, next);

      expect(next).to.have.been.calledWith(
        sinon.match
          .instanceOf(AppError)
          .and(sinon.match.has('httpCode', HttpCode.BAD_REQUEST))
          .and(
            sinon.match.has(
              'description',
              'Email and assessment ID are required'
            )
          )
      );
    });

    it('should handle errors and call next', async () => {
      const req = mockReq({
        body: {
          email: '<EMAIL>',
          assessmentId: 'assessment-123',
        },
      });
      const res = mockRes();
      const next = sandbox.stub();

      const error = new Error('Database error');
      retakeDelayStub.canRetakeAssessment.rejects(error);

      await candidateRetakeController.checkRetakeEligibility(req, res, next);

      expect(next).to.have.been.calledWith(error);
    });
  });

  describe('generateRetakeLink', () => {
    it('should successfully generate retake link when eligible', async () => {
      const req = mockReq({
        body: {
          email: '<EMAIL>',
          assessmentId: 'assessment-123',
        },
      });
      const res = mockRes();
      const next = sandbox.stub();

      const mockAssessmentTaker = {
        id: '<EMAIL>-assessment-123-1234567890',
        email: '<EMAIL>',
        assessmentId: 'assessment-123',
        status: 'IN_PROGRESS',
        startTime: new Date(),
        createdAt: new Date(),
        updatedAt: new Date(),
      };

      retakeDelayStub.canRetakeAssessment.resolves(true);
      prismaStub.assessmentTaker.create.resolves(mockAssessmentTaker);

      // Mock Date.now() to return a fixed timestamp
      const dateNowStub = sandbox.stub(Date, 'now').returns(1234567890);

      await candidateRetakeController.generateRetakeLink(req, res, next);

      expect(retakeDelayStub.canRetakeAssessment).to.have.been.calledWith(
        '<EMAIL>',
        'assessment-123'
      );
      expect(prismaStub.assessmentTaker.create).to.have.been.calledWith({
        data: {
          id: '<EMAIL>-assessment-123-1234567890',
          email: '<EMAIL>',
          assessmentId: 'assessment-123',
          status: 'IN_PROGRESS',
          startTime: sinon.match.date,
          createdAt: sinon.match.date,
          updatedAt: sinon.match.date,
        },
      });
      expect(res.status).to.have.been.calledWith(201);
      expect(res.json).to.have.been.calledWith({
        success: true,
        data: {
          assessmentTakerId: '<EMAIL>-assessment-123-1234567890',
          assessmentTaker: mockAssessmentTaker,
          message: 'Retake assessment link generated successfully',
          generatedAt: sinon.match.string,
        },
      });

      dateNowStub.restore();
    });

    it.skip('should throw error when candidate is not eligible for retake', async () => {
      const req = mockReq({
        body: {
          email: '<EMAIL>',
          assessmentId: 'assessment-123',
        },
      });
      const res = mockRes();
      const next = sandbox.stub();

      retakeDelayStub.canRetakeAssessment.resolves(false);
      retakeDelayStub.getHoursRemaining.resolves(12);

      await candidateRetakeController.generateRetakeLink(req, res, next);

      expect(next).to.have.been.calledWith(
        sinon.match
          .instanceOf(AppError)
          .and(sinon.match.has('httpCode', HttpCode.FORBIDDEN))
          .and(
            sinon.match.has(
              'description',
              'You cannot retake this assessment yet. Please wait 12 more hours.'
            )
          )
      );
    });

    it.skip('should throw error when email is missing', async () => {
      const req = mockReq({
        body: {
          assessmentId: 'assessment-123',
        },
      });
      const res = mockRes();
      const next = sandbox.stub();

      await candidateRetakeController.generateRetakeLink(req, res, next);

      expect(next).to.have.been.calledWith(
        sinon.match
          .instanceOf(AppError)
          .and(sinon.match.has('httpCode', HttpCode.BAD_REQUEST))
          .and(
            sinon.match.has(
              'description',
              'Email and assessment ID are required'
            )
          )
      );
    });

    it('should handle database errors and call next', async () => {
      const req = mockReq({
        body: {
          email: '<EMAIL>',
          assessmentId: 'assessment-123',
        },
      });
      const res = mockRes();
      const next = sandbox.stub();

      retakeDelayStub.canRetakeAssessment.resolves(true);
      const error = new Error('Database error');
      prismaStub.assessmentTaker.create.rejects(error);

      await candidateRetakeController.generateRetakeLink(req, res, next);

      expect(next).to.have.been.calledWith(error);
    });
  });

  describe('getCandidateAssessmentHistory', () => {
    it('should return candidate assessment history successfully', async () => {
      const req = mockReq({
        query: {
          email: '<EMAIL>',
          assessmentId: 'assessment-123',
        },
      });
      const res = mockRes();
      const next = sandbox.stub();

      const mockAttempts = [
        {
          id: 'attempt-1',
          startTime: new Date('2023-01-01'),
          endTime: new Date('2023-01-01'),
          status: 'COMPLETED',
          assessmentDuration: 3600,
          createdAt: new Date('2023-01-01'),
          testResults: [
            {
              testPercentage: 80,
              totalScore: 80,
              totalPassedScore: 100,
              passStatus: 'PASS',
            },
          ],
        },
        {
          id: 'attempt-2',
          startTime: new Date('2023-01-02'),
          endTime: new Date('2023-01-02'),
          status: 'COMPLETED',
          assessmentDuration: 3500,
          createdAt: new Date('2023-01-02'),
          testResults: [
            {
              testPercentage: 70,
              totalScore: 70,
              totalPassedScore: 100,
              passStatus: 'FAIL',
            },
          ],
        },
      ];

      prismaStub.assessmentTaker.findMany.resolves(mockAttempts);
      retakeDelayStub.canRetakeAssessment.resolves(true);
      retakeDelayStub.getHoursRemaining.resolves(0);

      await candidateRetakeController.getCandidateAssessmentHistory(
        req,
        res,
        next
      );

      expect(prismaStub.assessmentTaker.findMany).to.have.been.calledWith({
        where: {
          email: '<EMAIL>',
          assessmentId: 'assessment-123',
          endTime: { not: null },
        },
        select: {
          id: true,
          startTime: true,
          endTime: true,
          status: true,
          assessmentDuration: true,
          createdAt: true,
          testResults: {
            select: {
              testPercentage: true,
              totalScore: true,
              totalPassedScore: true,
              passStatus: true,
            },
          },
        },
        orderBy: {
          endTime: 'desc',
        },
      });

      expect(res.status).to.have.been.calledWith(200);
      expect(res.json).to.have.been.calledWith({
        success: true,
        data: {
          email: '<EMAIL>',
          assessmentId: 'assessment-123',
          statistics: {
            totalAttempts: 2,
            completedAttempts: 2,
            passedAttempts: 1,
          },
          attempts: sinon.match.array,
          retakeInfo: {
            canRetake: true,
            hoursRemaining: 0,
            nextRetakeAvailable: null,
          },
        },
      });
    });

    it.skip('should throw error when email is missing', async () => {
      const req = mockReq({
        query: {
          assessmentId: 'assessment-123',
        },
      });
      const res = mockRes();
      const next = sandbox.stub();

      await candidateRetakeController.getCandidateAssessmentHistory(
        req,
        res,
        next
      );

      expect(next).to.have.been.calledWith(
        sinon.match
          .instanceOf(AppError)
          .and(sinon.match.has('httpCode', HttpCode.BAD_REQUEST))
          .and(
            sinon.match.has(
              'description',
              'Email and assessment ID are required as query parameters'
            )
          )
      );
    });

    it.skip('should throw error when assessmentId is missing', async () => {
      const req = mockReq({
        query: {
          email: '<EMAIL>',
        },
      });
      const res = mockRes();
      const next = sandbox.stub();

      await candidateRetakeController.getCandidateAssessmentHistory(
        req,
        res,
        next
      );

      expect(next).to.have.been.calledWith(
        sinon.match
          .instanceOf(AppError)
          .and(sinon.match.has('httpCode', HttpCode.BAD_REQUEST))
          .and(
            sinon.match.has(
              'description',
              'Email and assessment ID are required as query parameters'
            )
          )
      );
    });

    it('should handle database errors and call next', async () => {
      const req = mockReq({
        query: {
          email: '<EMAIL>',
          assessmentId: 'assessment-123',
        },
      });
      const res = mockRes();
      const next = sandbox.stub();

      const error = new Error('Database error');
      prismaStub.assessmentTaker.findMany.rejects(error);

      await candidateRetakeController.getCandidateAssessmentHistory(
        req,
        res,
        next
      );

      expect(next).to.have.been.calledWith(error);
    });

    it('should return empty history when no attempts found', async () => {
      const req = mockReq({
        query: {
          email: '<EMAIL>',
          assessmentId: 'assessment-123',
        },
      });
      const res = mockRes();
      const next = sandbox.stub();

      prismaStub.assessmentTaker.findMany.resolves([]);
      prismaStub.assessmentTaker.count.resolves(0);
      retakeDelayStub.canRetakeAssessment.resolves(true);
      retakeDelayStub.getHoursRemaining.resolves(0);

      await candidateRetakeController.getCandidateAssessmentHistory(
        req,
        res,
        next
      );

      expect(res.status).to.have.been.calledWith(200);
      expect(res.json).to.have.been.calledWith({
        success: true,
        data: {
          email: '<EMAIL>',
          assessmentId: 'assessment-123',
          statistics: {
            totalAttempts: 0,
            completedAttempts: 0,
            passedAttempts: 0,
          },
          attempts: [],
          retakeInfo: {
            canRetake: true,
            hoursRemaining: 0,
            nextRetakeAvailable: null,
          },
        },
      });
    });

    it('should calculate next retake available time when hours remaining', async () => {
      const req = mockReq({
        query: {
          email: '<EMAIL>',
          assessmentId: 'assessment-123',
        },
      });
      const res = mockRes();
      const next = sandbox.stub();

      prismaStub.assessmentTaker.findMany.resolves([]);
      prismaStub.assessmentTaker.count.resolves(0);
      retakeDelayStub.canRetakeAssessment.resolves(false);
      retakeDelayStub.getHoursRemaining.resolves(5);

      // Mock Date.now() to return a fixed timestamp
      const fixedTime = 1640995200000; // 2022-01-01 00:00:00 UTC
      const dateNowStub = sandbox.stub(Date, 'now').returns(fixedTime);

      await candidateRetakeController.getCandidateAssessmentHistory(
        req,
        res,
        next
      );

      const expectedNextRetakeTime = new Date(
        fixedTime + 5 * 60 * 60 * 1000
      ).toISOString();

      expect(res.json).to.have.been.calledWith(
        sinon.match.has(
          'data',
          sinon.match.has('retakeInfo', {
            canRetake: false,
            hoursRemaining: 5,
            nextRetakeAvailable: expectedNextRetakeTime,
          })
        )
      );

      dateNowStub.restore();
    });
  });
});
