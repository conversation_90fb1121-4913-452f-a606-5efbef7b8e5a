import { expect } from 'chai';
import sinon from 'sinon';
import proxyquire from 'proxyquire';
import { mockReq, mockRes } from 'sinon-express-mock';
import { AppError, HttpCode } from '../../src/middlewares/errorHandler';

describe('CodeExecution Controller', () => {
  let sandbox: sinon.SinonSandbox;
  let codeExecutionHelperStub: any;
  let loggerStub: any;
  let codeExecutionController: any;
  let req: any;
  let res: any;
  let next: sinon.SinonStub;

  beforeEach(() => {
    sandbox = sinon.createSandbox();

    // Create stubs for helper functions
    codeExecutionHelperStub = {
      generateRunnerCode: sandbox.stub(),
      createSourceCode: sandbox.stub(),
      executeCode: sandbox.stub(),
      prepareAndProcessTestCases: sandbox.stub(),
    };

    loggerStub = {
      error: sandbox.stub(),
    };

    // Load controller with mocked dependencies
    codeExecutionController = proxyquire(
      '../../src/controllers/codeExecution',
      {
        '../helpers/codeExecution': codeExecutionHelperStub,
        '../helpers/logger': { default: loggerStub },
      }
    );

    req = mockReq();
    res = mockRes();
    next = sandbox.stub();
  });

  afterEach(() => {
    sandbox.restore();
  });

  describe('runTestCase', () => {
    it('Should successfully run a test case', async () => {
      req.body = {
        code: 'console.log("Hello World");',
        language_id: 63,
        input: 'test input',
      };

      const mockOutput = 'Hello World';
      codeExecutionHelperStub.generateRunnerCode.returns('runner code');
      codeExecutionHelperStub.createSourceCode.returns('source code');
      codeExecutionHelperStub.executeCode.resolves(mockOutput);

      await codeExecutionController.runTestCase(req, res, next);

      expect(
        codeExecutionHelperStub.generateRunnerCode.calledWith(
          63,
          req.body.code,
          req.body.input
        )
      ).to.be.true;
      expect(
        codeExecutionHelperStub.createSourceCode.calledWith(
          req.body.code,
          'runner code',
          63
        )
      ).to.be.true;
      expect(
        codeExecutionHelperStub.executeCode.calledWith(
          'source code',
          63,
          req.body.input
        )
      ).to.be.true;

      expect(res.status.calledWith(200)).to.be.true;
      expect(
        res.send.calledWith({
          success: true,
          data: {
            output: mockOutput,
          },
        })
      ).to.be.true;
    });

    it('Should handle empty code validation', async () => {
      req.body = {
        code: '',
        language_id: 63,
        input: 'test input',
      };

      await codeExecutionController.runTestCase(req, res, next);

      expect(next.calledOnce).to.be.true;
      const error = next.firstCall.args[0];
      expect(error).to.be.instanceOf(AppError);
      expect(error.httpCode).to.equal(HttpCode.BAD_REQUEST);
      expect(error.message).to.equal('Code must be a non-empty string');
    });

    it('Should handle invalid language_id validation', async () => {
      req.body = {
        code: 'console.log("test");',
        language_id: 'invalid',
        input: 'test input',
      };

      await codeExecutionController.runTestCase(req, res, next);

      expect(next.calledOnce).to.be.true;
      const error = next.firstCall.args[0];
      expect(error).to.be.instanceOf(AppError);
      expect(error.httpCode).to.equal(HttpCode.BAD_REQUEST);
      expect(error.message).to.equal('Language ID must be a valid number');
    });

    it('Should handle missing input validation', async () => {
      req.body = {
        code: 'console.log("test");',
        language_id: 63,
      };

      await codeExecutionController.runTestCase(req, res, next);

      expect(next.calledOnce).to.be.true;
      const error = next.firstCall.args[0];
      expect(error).to.be.instanceOf(AppError);
      expect(error.httpCode).to.equal(HttpCode.BAD_REQUEST);
      expect(error.message).to.equal('Input is required');
    });

    it('Should handle object input by stringifying it', async () => {
      req.body = {
        code: 'console.log("Hello World");',
        language_id: 63,
        input: { test: 'data' },
      };

      const mockOutput = 'Hello World';
      codeExecutionHelperStub.generateRunnerCode.returns('runner code');
      codeExecutionHelperStub.createSourceCode.returns('source code');
      codeExecutionHelperStub.executeCode.resolves(mockOutput);

      await codeExecutionController.runTestCase(req, res, next);

      expect(
        codeExecutionHelperStub.generateRunnerCode.calledWith(
          63,
          req.body.code,
          JSON.stringify(req.body.input)
        )
      ).to.be.true;
    });

    it('Should handle execution errors', async () => {
      req.body = {
        code: 'console.log("Hello World");',
        language_id: 63,
        input: 'test input',
      };

      const executionError = new Error('Execution failed');
      codeExecutionHelperStub.generateRunnerCode.returns('runner code');
      codeExecutionHelperStub.createSourceCode.returns('source code');
      codeExecutionHelperStub.executeCode.rejects(executionError);

      await codeExecutionController.runTestCase(req, res, next);

      expect(
        loggerStub.error.calledWith('Error in runTestCase:', executionError)
      ).to.be.true;
      expect(next.calledWith(executionError)).to.be.true;
    });
  });

  describe('runMultipleTestCases', () => {
    beforeEach(() => {
      req.params = {
        assessmentTakerId: 'valid-uuid-1',
        questionId: 'valid-uuid-2',
      };
    });

    it('Should successfully run multiple test cases', async () => {
      req.body = {
        code: 'function add(a, b) { return a + b; }',
        language_id: 63,
        testCases: [
          {
            test_case_id: '1',
            input: '1,2',
            output: '3',
          },
        ],
        codeConstraints: {},
      };

      const mockResponse = { results: ['test result'] };
      codeExecutionHelperStub.prepareAndProcessTestCases.resolves(mockResponse);

      await codeExecutionController.runMultipleTestCases(req, res, next);

      expect(
        codeExecutionHelperStub.prepareAndProcessTestCases.calledWith(
          req.params.assessmentTakerId,
          req.params.questionId,
          req.body.code,
          req.body.language_id,
          req.body.testCases,
          req.body.codeConstraints
        )
      ).to.be.true;

      expect(res.status.calledWith(200)).to.be.true;
      expect(
        res.send.calledWith({
          success: true,
          data: mockResponse,
        })
      ).to.be.true;
    });

    it('Should validate empty test cases array', async () => {
      req.body = {
        code: 'function add(a, b) { return a + b; }',
        language_id: 63,
        testCases: [],
      };

      await codeExecutionController.runMultipleTestCases(req, res, next);

      expect(next.calledOnce).to.be.true;
      const error = next.firstCall.args[0];
      expect(error).to.be.instanceOf(AppError);
      expect(error.message).to.equal('Test cases must be a non-empty array');
    });

    it('Should validate too many test cases', async () => {
      req.body = {
        code: 'function add(a, b) { return a + b; }',
        language_id: 63,
        testCases: new Array(101).fill({
          test_case_id: '1',
          input: '1,2',
          output: '3',
        }),
      };

      await codeExecutionController.runMultipleTestCases(req, res, next);

      expect(next.calledOnce).to.be.true;
      const error = next.firstCall.args[0];
      expect(error).to.be.instanceOf(AppError);
      expect(error.message).to.equal(
        'Too many test cases (maximum 100 allowed)'
      );
    });

    it('Should validate missing assessmentTakerId', async () => {
      req.params.assessmentTakerId = '';
      req.body = {
        code: 'function add(a, b) { return a + b; }',
        language_id: 63,
        testCases: [{ test_case_id: '1', input: '1,2', output: '3' }],
      };

      await codeExecutionController.runMultipleTestCases(req, res, next);

      expect(next.calledOnce).to.be.true;
      const error = next.firstCall.args[0];
      expect(error).to.be.instanceOf(AppError);
      expect(error.message).to.equal('Valid assessment taker ID is required');
    });

    it('Should validate test case structure', async () => {
      req.body = {
        code: 'function add(a, b) { return a + b; }',
        language_id: 63,
        testCases: [
          {
            // Missing test_case_id, input, and output
          },
        ],
      };

      await codeExecutionController.runMultipleTestCases(req, res, next);

      expect(next.calledOnce).to.be.true;
      const error = next.firstCall.args[0];
      expect(error).to.be.instanceOf(AppError);
      expect(error.message).to.include(
        'Test case at index 0 is missing required fields'
      );
    });
  });

  describe('validateTestCases', () => {
    it('Should successfully validate test cases', async () => {
      req.body = {
        code: 'function add(a, b) { return a + b; }',
        language_id: 63,
        testCases: [
          {
            input: '1,2',
            expected_output: 3,
            description: 'Add two numbers',
            hidden: false,
          },
        ],
      };

      const mockOutput = '3';
      codeExecutionHelperStub.generateRunnerCode.returns('runner code');
      codeExecutionHelperStub.createSourceCode.returns('source code');
      codeExecutionHelperStub.executeCode.resolves(mockOutput);

      await codeExecutionController.validateTestCases(req, res, next);

      expect(res.status.calledWith(200)).to.be.true;
      const responseData = res.send.firstCall.args[0];
      expect(responseData.success).to.be.true;
      expect(responseData.data.validatedTestCases).to.have.length(1);
      expect(responseData.data.summary.totalTestCases).to.equal(1);
      expect(responseData.data.summary.validTestCases).to.equal(1);
    });

    it('Should handle test cases with too many cases for validation', async () => {
      req.body = {
        code: 'function add(a, b) { return a + b; }',
        language_id: 63,
        testCases: new Array(51).fill({
          input: '1,2',
          expected_output: 3,
        }),
      };

      await codeExecutionController.validateTestCases(req, res, next);

      expect(next.calledOnce).to.be.true;
      const error = next.firstCall.args[0];
      expect(error).to.be.instanceOf(AppError);
      expect(error.message).to.equal(
        'Too many test cases for validation (maximum 50 allowed)'
      );
    });

    it('Should handle test cases with missing required fields', async () => {
      req.body = {
        code: 'function add(a, b) { return a + b; }',
        language_id: 63,
        testCases: [
          {
            // Missing input and expected_output
            description: 'Invalid test case',
          },
        ],
      };

      codeExecutionHelperStub.generateRunnerCode.returns('runner code');
      codeExecutionHelperStub.createSourceCode.returns('source code');

      await codeExecutionController.validateTestCases(req, res, next);

      expect(res.status.calledWith(200)).to.be.true;
      const responseData = res.send.firstCall.args[0];
      expect(responseData.data.errorMessages).to.have.length(1);
      expect(responseData.data.errorMessages[0]).to.include(
        'Test case 1 is missing required fields'
      );
    });

    it('Should handle execution errors during validation', async () => {
      req.body = {
        code: 'function add(a, b) { return a + b; }',
        language_id: 63,
        testCases: [
          {
            input: '1,2',
            expected_output: 3,
          },
        ],
      };

      codeExecutionHelperStub.generateRunnerCode.returns('runner code');
      codeExecutionHelperStub.createSourceCode.returns('source code');
      codeExecutionHelperStub.executeCode.rejects(
        new Error('Execution failed')
      );

      await codeExecutionController.validateTestCases(req, res, next);

      expect(res.status.calledWith(200)).to.be.true;
      const responseData = res.send.firstCall.args[0];
      expect(responseData.data.errorMessages).to.have.length(1);
      expect(responseData.data.errorMessages[0]).to.include(
        'Test case 1 execution failed'
      );
    });

    it('Should correct wrong expected outputs', async () => {
      req.body = {
        code: 'function add(a, b) { return a + b; }',
        language_id: 63,
        testCases: [
          {
            input: '1,2',
            expected_output: 5, // Wrong expected output
          },
        ],
      };

      const mockOutput = '3'; // Correct output
      codeExecutionHelperStub.generateRunnerCode.returns('runner code');
      codeExecutionHelperStub.createSourceCode.returns('source code');
      codeExecutionHelperStub.executeCode.resolves(mockOutput);

      await codeExecutionController.validateTestCases(req, res, next);

      expect(res.status.calledWith(200)).to.be.true;
      const responseData = res.send.firstCall.args[0];
      expect(responseData.data.validatedTestCases[0].expected_output).to.equal(
        3
      ); // Should be the actual output since it was wrong
      expect(responseData.data.summary.correctedCount).to.equal(1);
    });

    it('Should handle JSON parsing of outputs', async () => {
      req.body = {
        code: 'function getObject() { return {"result": 42}; }',
        language_id: 63,
        testCases: [
          {
            input: '',
            expected_output: { result: 42 },
          },
        ],
      };

      const mockOutput = '{"result": 42}';
      codeExecutionHelperStub.generateRunnerCode.returns('runner code');
      codeExecutionHelperStub.createSourceCode.returns('source code');
      codeExecutionHelperStub.executeCode.resolves(mockOutput);

      await codeExecutionController.validateTestCases(req, res, next);

      expect(res.status.calledWith(200)).to.be.true;
      const responseData = res.send.firstCall.args[0];
      expect(responseData.data.validatedTestCases).to.have.length(0);
      expect(responseData.data.summary.correctedCount).to.equal(0);
    });
  });
});
