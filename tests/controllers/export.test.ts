import { expect } from 'chai';
import sinon from 'sinon';
import proxyquire from 'proxyquire';
import { mockReq, mockRes } from 'sinon-express-mock';
import { AppError, HttpCode } from '../../src/middlewares/errorHandler';

describe('Export Controller', () => {
  let sandbox: sinon.SinonSandbox;
  let prismaStub: any;
  let questionServiceStub: any;
  let loggerStub: any;
  let exportController: any;
  let req: any;
  let res: any;
  let next: sinon.SinonStub;

  beforeEach(() => {
    sandbox = sinon.createSandbox();

    // Create stubs
    prismaStub = {
      assessmentTaker: {
        findFirst: sandbox.stub(),
        findMany: sandbox.stub(),
      },
      testResult: {
        findMany: sandbox.stub(),
      },
    };

    questionServiceStub = {
      getQuestionTextsByIds: sandbox.stub(),
    };

    loggerStub = {
      error: sandbox.stub(),
    };

    // Load controller with mocked dependencies
    exportController = proxyquire('../../src/controllers/export', {
      '../prisma': { default: prismaStub },
      '../services/questionService': { QuestionService: questionServiceStub },
      '../helpers/logger': { default: loggerStub },
    });

    req = mockReq();
    res = mockRes();
    next = sandbox.stub();
  });

  afterEach(() => {
    sandbox.restore();
  });

  describe('exportTestTakerResults', () => {
    beforeEach(() => {
      req.params = {
        organizationId: 'org-123',
        assessmentTakerId: 'taker-456',
      };
      req.query = {};
    });

    it('Should successfully export test taker results', async () => {
      const mockAssessmentTaker = {
        id: 'taker-456',
        email: '<EMAIL>',
        firstName: 'John',
        lastName: 'Doe',
        overallAssessmentScore: 85,
        overallAssessmentPercentage: 85.5,
        passStatus: 'PASSED',
        submissionType: 'SUBMITTED',
        organizationId: 'org-123',
        createdAt: new Date('2024-01-01'),
        updatedAt: new Date('2024-01-02'),
        testResults: [
          {
            id: 'result-1',
            testTakerId: 'taker-456',
            result: [
              {
                questionId: 'q1',
                testTakerAnswers: ['answer1'],
                answerStatus: 'CORRECT',
                questionScore: 10,
                questionMaxScore: 10,
              },
            ],
          },
        ],
      };

      const mockQuestionTexts = {
        q1: {
          id: 'q1',
          questionText: 'What is 2+2?',
          questionType: 'SINGLE_CHOICE',
        },
      };

      prismaStub.assessmentTaker.findFirst.resolves(mockAssessmentTaker);
      questionServiceStub.getQuestionTextsByIds.resolves(mockQuestionTexts);

      await exportController.exportTestTakerResults(req, res, next);

      expect(prismaStub.assessmentTaker.findFirst.calledOnce).to.be.true;
      expect(questionServiceStub.getQuestionTextsByIds.calledOnce).to.be.true;

      expect(res.status.calledWith(200)).to.be.true;
      const responseData = res.json.firstCall.args[0];
      expect(responseData.success).to.be.true;
      expect(responseData.data).to.be.an('object');
    });

    it('Should handle missing organizationId', async () => {
      req.params.organizationId = '';

      await exportController.exportTestTakerResults(req, res, next);

      expect(next.calledOnce).to.be.true;
      const error = next.firstCall.args[0];
      expect(error).to.be.instanceOf(AppError);
      expect(error.httpCode).to.equal(HttpCode.BAD_REQUEST);
      expect(error.message).to.equal('Organization ID is required');
    });

    it('Should handle missing assessmentTakerId', async () => {
      req.params.assessmentTakerId = '';

      await exportController.exportTestTakerResults(req, res, next);

      expect(next.calledOnce).to.be.true;
      const error = next.firstCall.args[0];
      expect(error).to.be.instanceOf(AppError);
      expect(error.httpCode).to.equal(HttpCode.BAD_REQUEST);
      expect(error.message).to.equal('Assessment taker ID is required');
    });

    it('Should handle database errors', async () => {
      const dbError = new Error('Database connection failed');
      prismaStub.assessmentTaker.findFirst.rejects(dbError);

      await exportController.exportTestTakerResults(req, res, next);

      expect(
        loggerStub.error.calledWith(
          'Error exporting test taker results:',
          dbError
        )
      ).to.be.true;
      expect(next.calledWith(dbError)).to.be.true;
    });

    it('Should handle question service errors', async () => {
      const mockAssessmentTaker = {
        id: 'taker-456',
        email: '<EMAIL>',
        firstName: 'John',
        lastName: 'Doe',
        organizationId: 'org-123',
        testResults: [
          {
            id: 'result-1',
            result: [
              {
                questionId: 'q1',
                testTakerAnswers: ['answer1'],
                answerStatus: 'CORRECT',
              },
            ],
          },
        ],
      };

      const questionServiceError = new Error('Question service unavailable');
      prismaStub.assessmentTaker.findFirst.resolves(mockAssessmentTaker);
      questionServiceStub.getQuestionTextsByIds.rejects(questionServiceError);

      await exportController.exportTestTakerResults(req, res, next);

      expect(
        loggerStub.error.calledWith(
          'Error exporting test taker results:',
          questionServiceError
        )
      ).to.be.true;
      expect(next.calledWith(questionServiceError)).to.be.true;
    });

    it('Should handle assessment taker not found', async () => {
      prismaStub.assessmentTaker.findFirst.resolves(null);

      await exportController.exportTestTakerResults(req, res, next);

      expect(next.calledOnce).to.be.true;
      const error = next.firstCall.args[0];
      expect(error).to.be.instanceOf(AppError);
      expect(error.httpCode).to.equal(HttpCode.NOT_FOUND);
      expect(error.message).to.equal('Assessment taker not found.');
    });

    it('Should handle assessment taker with no test results', async () => {
      const mockAssessmentTaker = {
        id: 'taker-456',
        email: '<EMAIL>',
        organizationId: 'org-123',
        testResults: [],
      };

      prismaStub.assessmentTaker.findFirst.resolves(mockAssessmentTaker);

      await exportController.exportTestTakerResults(req, res, next);

      expect(next.calledOnce).to.be.true;
      const error = next.firstCall.args[0];
      expect(error).to.be.instanceOf(AppError);
      expect(error.httpCode).to.equal(HttpCode.NOT_FOUND);
      expect(error.message).to.equal(
        'No test results found for this assessment taker.'
      );
    });
  });
});
