import { expect } from 'chai';
import { Request, Response } from 'express';
import { indexController } from '../src/controllers';

describe('indexController', () => {
  it('should return a response with status 200 and a message', () => {
    const req = {} as Request;
    const res = {
      status: (code: number) => ({
        send: (data: string) => {
          expect(code).to.equal(200);
          expect(data).to.deep.equal({
            success: true,
            message: 'DODOKPO TEST EXECUTION',
          });
        },
      }),
    } as unknown as Response;
    indexController(req, res);
  });
});
