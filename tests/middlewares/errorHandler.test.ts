import { expect } from 'chai';
import sinon from 'sinon';
import proxyquire from 'proxyquire';
import { AppError, HttpCode } from '../../src/middlewares/errorHandler';
import express from 'express';

describe('ErrorHandler', () => {
  let ErrorHandler; // Will hold the mocked instance
  let winstonStub;
  let sendMailStub;
  let processExitStub;
  let originalEnv: NodeJS.ProcessEnv;
  // eslint-disable-next-line @typescript-eslint/no-unused-vars
  let isTrustedErrorStub: sinon.SinonStub;

  beforeEach(() => {
    // Backup original process.env
    originalEnv = process.env;
    process.env = {
      ...originalEnv,
      DEV_EMAIL_1: '<EMAIL>',
      DEV_EMAIL_2: '<EMAIL>',
      DEV_EMAIL_3: '<EMAIL>',
    };

    // Stub process.exit
    processExitStub = sinon.stub(process, 'exit');

    // Mock winston
    winstonStub = {
      format: {
        combine: sinon.stub(),
        timestamp: sinon.stub(),
        json: sinon.stub(),
        prettyPrint: sinon.stub(),
      },
      transports: {
        Console: sinon.stub(),
        File: sinon.stub(),
      },
      createLogger: sinon.stub().returns({
        error: sinon.stub(),
      }),
    };

    // Mock sendMail
    sendMailStub = sinon.stub().resolves();

    // Use proxyquire to require the module without the default export initially
    const ErrorHandlerModule = proxyquire(
      '../../src/middlewares/errorHandler',
      {
        winston: winstonStub,
        '../helpers/sendmail': {
          sendMail: sendMailStub,
        },
      }
    );

    // Get the default exported instance
    ErrorHandler = ErrorHandlerModule.default;

    isTrustedErrorStub = sinon
      .stub(ErrorHandler, 'isTrustedError')
      .callsFake((error) => {
        return error instanceof AppError && error.isOperational;
      });
  });

  afterEach(() => {
    // Restore original process.env
    process.env = originalEnv;
    sinon.restore();
  });

  describe('isTrustedError', () => {
    it('should return true for operational AppError', () => {
      const error = new AppError({
        httpCode: HttpCode.BAD_REQUEST,
        description: 'Test error',
        isOperational: true,
      });
      expect(ErrorHandler.isTrustedError(error)).to.be.true;
    });

    it('should return false for non-operational AppError', () => {
      const error = new AppError({
        httpCode: HttpCode.BAD_REQUEST,
        description: 'Test error',
        isOperational: false,
      });
      expect(ErrorHandler.isTrustedError(error)).to.be.false;
    });

    it('should return false for regular Error', () => {
      const error = new Error('Test error');
      expect(ErrorHandler.isTrustedError(error)).to.be.false;
    });
  });

  describe('handleTrustedError', () => {
    it('should send correct response for trusted error', () => {
      const res = {
        status: sinon.stub().returnsThis(),
        send: sinon.stub(),
      } as unknown as express.Response;

      const error = new AppError({
        httpCode: HttpCode.FORBIDDEN,
        description: 'Access denied',
        data: { reason: 'insufficient permissions' },
      });

      ErrorHandler.handleTrustedError(error, res);

      expect(res.status.calledWith(HttpCode.FORBIDDEN)).to.be.true;
      expect(
        res.send.calledWith({
          success: false,
          message: 'Access denied',
          data: { reason: 'insufficient permissions' },
        })
      ).to.be.true;
    });
  });

  describe('handleCriticalError', () => {
    it('should send 500 response and exit process when response exists', () => {
      const res = {
        status: sinon.stub().returnsThis(),
        send: sinon.stub(),
      } as unknown as express.Response;

      const error = new Error('Critical error');
      ErrorHandler.handleCriticalError(error, res);

      expect(res.status.calledWith(HttpCode.INTERNAL_SERVER_ERROR)).to.be.true;
      expect(
        res.send.calledWith({
          success: false,
          message: 'Internal Server Error',
        })
      ).to.be.true;
      expect(processExitStub.calledWith(1)).to.be.true;
    });

    it('should just exit process when no response exists', () => {
      const error = new Error('Critical error');
      ErrorHandler.handleCriticalError(error);

      expect(processExitStub.calledWith(1)).to.be.true;
    });
  });

  describe('handleError', () => {
    let req: express.Request;
    let res: express.Response;

    beforeEach(() => {
      req = {
        method: 'GET',
        path: '/test',
      } as express.Request;

      res = {
        status: sinon.stub().returnsThis(),
        send: sinon.stub(),
      } as unknown as express.Response;
    });

    it('should handle untrusted error correctly', async () => {
      const error = new Error('System failure');

      await ErrorHandler.handleError(error, req, res);

      expect(winstonStub.createLogger().error.calledOnce).to.be.true;
      expect(sendMailStub.calledOnce).to.be.true;
      expect(processExitStub.calledWith(1)).to.be.true;
    });

    it('should handle email sending failures', async () => {
      const mailError = new Error('Mail failed');
      sendMailStub.rejects(mailError);
      const error = new Error('System failure');

      await ErrorHandler.handleError(error, req, res);

      expect(winstonStub.createLogger().error.calledTwice).to.be.true;
      expect(sendMailStub.calledOnce).to.be.true;
      expect(processExitStub.calledWith(1)).to.be.true;
    });
  });

  describe('sendMailToDevelopers', () => {
    it('should send email with correct parameters', async () => {
      const req = {
        method: 'POST',
        path: '/api/test',
      } as express.Request;
      const error = new Error('Test error');

      await ErrorHandler.sendMailToDevelopers(error, req);

      expect(sendMailStub.calledOnce).to.be.true;
      const mailArgs = sendMailStub.firstCall.args;
      expect(mailArgs[0]).to.deep.equal([
        '<EMAIL>',
        '<EMAIL>',
        '<EMAIL>',
      ]);
      expect(mailArgs[1]).to.equal('Application Server Error');
      expect(mailArgs[2]).to.include('Test error');
      expect(mailArgs[2]).to.include('POST /api/test');
      expect(mailArgs[3][0].filename).to.equal('error.log');
    });

    it('should log email sending failures', async () => {
      const req = {
        method: 'POST',
        path: '/api/test',
      } as express.Request;
      const error = new Error('Test error');
      const mailError = new Error('Mail failed');
      sendMailStub.rejects(mailError);

      await ErrorHandler.sendMailToDevelopers(error, req);

      expect(winstonStub.createLogger().error.calledOnce).to.be.true;
      const logEntry = winstonStub.createLogger().error.firstCall.args[0];
      expect(logEntry.error).to.equal('Error sending mail to developers');
      expect(logEntry.path).to.equal('POST: /api/test');
    });
  });
});
