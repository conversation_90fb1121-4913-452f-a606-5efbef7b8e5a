import { expect } from 'chai';
import sinon from 'sinon';
import proxyquire from 'proxyquire';
import { NextFunction, Request, Response } from 'express';
import Jo<PERSON> from 'joi';

describe('Validation Middleware', () => {
  let validationMiddleware;
  let redisStub;
  let req: Request;
  let res: Response;
  let next: NextFunction;

  beforeEach(() => {
    // Create stubs
    redisStub = {
      getCachedData: sinon.stub(),
    };

    // Mock dependencies
    validationMiddleware = proxyquire('../../src/middlewares/validation', {
      joi: Joi,
      '../helpers/redisCache': {
        getCachedData: redisStub.getCachedData,
      },
    });

    // Mock request, response, and next
    req = {
      params: {},
      headers: {},
      body: {},
    } as Request;

    res = {
      status: sinon.stub().returnsThis(),
      json: sinon.stub(),
    } as unknown as Response;

    next = sinon.stub();
  });

  afterEach(() => {
    sinon.restore();
  });

  describe('validateCreateFlagQuestion', () => {
    it('should pass validation for valid flag request', () => {
      req.body = {
        testId: 'valid-test-id',
        questionId: 'valid-question-id',
        questionText: 'Valid question text',
        reasonOfFlagging: ['Inappropriate content'],
      };

      validationMiddleware.validateCreateFlagQuestion(req, res, next);

      expect(next.calledOnce).to.be.true;
    });

    it('should return 400 for missing required fields', () => {
      req.body = {};

      validationMiddleware.validateCreateFlagQuestion(req, res, next);

      expect(res.status.calledWith(400)).to.be.true;
      expect(
        res.json.calledWithMatch({
          success: false,
          message: sinon.match.string,
        })
      ).to.be.true;
    });

    it('should return 400 for empty flag reasons', () => {
      req.body = {
        testId: 'valid-test-id',
        questionId: 'valid-question-id',
        questionText: 'Valid question text',
        reasonOfFlagging: [],
      };

      validationMiddleware.validateCreateFlagQuestion(req, res, next);

      expect(res.status.calledWith(400)).to.be.true;
      expect(
        res.json.calledWithMatch({
          success: false,
          message: sinon.match.string,
        })
      ).to.be.true;
    });
  });

  describe('validateCreateUnflagQuestion', () => {
    it('should pass validation for valid unflag request', () => {
      req.body = {
        testId: 'valid-test-id',
        questionId: 'valid-question-id',
      };

      validationMiddleware.validateCreateUnflagQuestion(req, res, next);

      expect(next.calledOnce).to.be.true;
    });

    it('should return 400 for missing required fields', () => {
      req.body = {};

      validationMiddleware.validateCreateUnflagQuestion(req, res, next);

      expect(res.status.calledWith(400)).to.be.true;
      expect(
        res.json.calledWithMatch({
          success: false,
          message: sinon.match.string,
        })
      ).to.be.true;
    });
  });

  describe('sampleTestValidation', () => {
    it('should pass validation for valid sample test', () => {
      req.body = {
        test: {
          questions: [
            {
              questionType: 'Multiple_choice',
              score: 10,
              answer: ['A'],
              testTakerAnswer: ['B'],
            },
          ],
        },
      };

      validationMiddleware.sampleTestValidation(req, res, next);

      expect(next.calledOnce).to.be.true;
    });

    it('should return 400 for invalid question type', () => {
      req.body = {
        test: {
          questions: [
            {
              questionType: 'Invalid_type',
              score: 10,
              answer: ['A'],
              testTakerAnswer: ['B'],
            },
          ],
        },
      };

      validationMiddleware.sampleTestValidation(req, res, next);

      expect(res.status.calledWith(400)).to.be.true;
    });
  });

  describe('submitAssessmentValidation', () => {
    it('should pass validation for valid assessment submission', () => {
      req.body = {
        testId: 'valid-test-id',
        answers: [
          {
            questionId: 'q1',
            answer: ['a'],
            questionType: 'Multiple_choice',
            score: 1,
          },
        ],
      };

      validationMiddleware.submitAssessmentValidation(req, res, next);

      expect(res.status.called).to.be.true; // Ensure no error response
    });
    it('should return 400 for invalid assessment submission', () => {
      req.body = {};

      validationMiddleware.submitAssessmentValidation(req, res, next);

      expect(res.status.calledWith(400)).to.be.true;
    });
  });

  describe('assessmentInProgressValidation', () => {
    beforeEach(() => {
      req = {
        ...req,
        assessmentTakerInfo: {
          proctorFeatures: [],
        },
      } as unknown as Request;
    });

    it('should pass validation when ID Capture is not required', () => {
      req.body = {};
      validationMiddleware.assessmentInProgressValidation(req, res, next);
      expect(next.calledOnce).to.be.true;
    });

    it('should require identity when ID Capture is enabled', () => {
      req.assessmentTakerInfo.proctorFeatures = ['ID Capture'];
      validationMiddleware.assessmentInProgressValidation(req, res, next);
      expect(res.status.calledWith(400)).to.be.true;
    });

    it('should pass with identity when ID Capture is enabled', () => {
      req.assessmentTakerInfo.proctorFeatures = ['ID Capture'];
      req.body = {
        identity: {
          type: 'Passport',
          number: '123456',
          expiryDate: '2025-12-31',
        },
      };
      validationMiddleware.assessmentInProgressValidation(req, res, next);
      expect(res.status.calledOnce).to.be.true;
    });
  });

  describe('fingerprintValidation', () => {
    it('should pass when fingerprints match', async () => {
      req.params.assessmentTakerId = 'test-taker-id';
      req.headers = { 'x-device-fingerprint': 'test-fingerprint' };
      redisStub.getCachedData.resolves('test-fingerprint');

      await validationMiddleware.fingerprintValidation(req, res, next);
      expect(next.calledOnce).to.be.true;
    });
  });

  describe('getPresignedURLValidation', () => {
    it('should pass validation for valid presigned URL request', () => {
      req.body = {
        mimeType: 'image/jpeg',
        keyType: 'head-shot',
        contentMD5: 'test-md5',
      };

      validationMiddleware.getPresignedURLValidation(req, res, next);
      expect(next.calledOnce).to.be.true;
    });

    it('should return 400 for invalid mimeType', () => {
      req.body = {
        mimeType: 'invalid-type',
        keyType: 'head-shot',
        contentMD5: 'test-md5',
      };

      validationMiddleware.getPresignedURLValidation(req, res, next);
      expect(res.status.calledWith(400)).to.be.true;
    });

    it('should return 400 for invalid keyType', () => {
      req.body = {
        mimeType: 'image/jpeg',
        keyType: 'invalid-type',
        contentMD5: 'test-md5',
      };

      validationMiddleware.getPresignedURLValidation(req, res, next);
      expect(res.status.calledWith(400)).to.be.true;
    });
  });
});
