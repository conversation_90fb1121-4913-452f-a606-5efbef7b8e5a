import { expect } from 'chai';
import sinon from 'sinon';
import proxyquire from 'proxyquire';
import { NextFunction, Response } from 'express';
import { AppError, HttpCode } from '../../src/middlewares/errorHandler';
import { ErrorMessage, RequestExtension } from '../../src/helpers/types';

describe('Assessment Middleware', () => {
  let assessmentMiddleware: any;
  let prismaStub: any;
  let redisStub: any;
  let uuidValidatorStub: any;
  let req: RequestExtension;
  let res: Response;
  let next: NextFunction;

  beforeEach(() => {
    // Create stubs
    prismaStub = {
      assessmentTaker: {
        findFirst: sinon.stub(),
        findUnique: sinon.stub(),
      },
    };

    redisStub = {
      getCachedData: sinon.stub(),
      setCacheData: sinon.stub().resolves(),
    };

    uuidValidatorStub = sinon.stub();

    // Mock dependencies
    assessmentMiddleware = proxyquire(
      '../../src/middlewares/assessmentTakerAccessContol',
      {
        '../prisma': { default: prismaStub },
        '../helpers/redisCache': {
          getCachedData: redisStub.getCachedData,
          setCacheData: redisStub.setCacheData,
        },
        uuid: { validate: uuidValidatorStub },
      }
    );

    // Mock request, response, and next
    req = {
      params: {},
      headers: {},
      url: '',
      method: '',
    } as RequestExtension;

    res = {
      status: sinon.stub().returnsThis(),
      json: sinon.stub(),
    } as unknown as Response;

    next = sinon.stub();
  });

  afterEach(() => {
    sinon.restore();
  });

  describe('assessmentTakerAccessPermission', () => {
    it('should return 403 for invalid UUID', async () => {
      req.params.assessmentTakerId = 'invalid-uuid';
      uuidValidatorStub.returns(false);

      await assessmentMiddleware.assessmentTakerAccessPermission(
        req,
        res,
        next
      );

      expect(res.status.calledWith(HttpCode.FORBIDDEN)).to.be.true;
      expect(
        res.json.calledWith({
          success: false,
          message: ErrorMessage.CANDIDATE_NOT_FOUND,
        })
      ).to.be.true;
    });

    it('should return 403 when assessment taker not found', async () => {
      req.params.assessmentTakerId = 'valid-uuid';
      uuidValidatorStub.returns(true);
      prismaStub.assessmentTaker.findFirst.resolves(null);

      await assessmentMiddleware.assessmentTakerAccessPermission(
        req,
        res,
        next
      );

      expect(res.status.calledWith(HttpCode.FORBIDDEN)).to.be.true;
      expect(
        res.json.calledWith({
          success: false,
          message: ErrorMessage.CANDIDATE_NOT_FOUND,
        })
      ).to.be.true;
    });

    it('should allow access for GET assessment-result when phase is ASSESSMENT_COMPLETION', async () => {
      req.params.assessmentTakerId = 'valid-uuid';
      req.url = '/assessment-result';
      req.method = 'GET';
      uuidValidatorStub.returns(true);
      prismaStub.assessmentTaker.findFirst.resolves({
        id: 'valid-uuid',
        phase: 'ASSESSMENT_COMPLETION',
      });

      await assessmentMiddleware.assessmentTakerAccessPermission(
        req,
        res,
        next
      );

      expect(next.calledOnce).to.be.true;
      expect(req.assessmentTakerInfo).to.exist;
    });

    it('should throw error for GET assessment-result when phase is not ASSESSMENT_COMPLETION', async () => {
      req.params.assessmentTakerId = 'valid-uuid';
      req.url = '/assessment-result';
      req.method = 'GET';
      uuidValidatorStub.returns(true);
      prismaStub.assessmentTaker.findFirst.resolves({
        id: 'valid-uuid',
        phase: 'TEST_TAKING',
      });

      await assessmentMiddleware.assessmentTakerAccessPermission(
        req,
        res,
        next
      );

      expect(next.calledWith(sinon.match.instanceOf(AppError))).to.be.true;
      const error = next.firstCall.args[0];
      expect(error.httpCode).to.equal(HttpCode.BAD_REQUEST);
      expect(error.message).to.equal(
        ErrorMessage.ASSESSMENT_RESULTS_UNAVAILABLE
      );
    });

    it('should allow POST survey submission', async () => {
      req.params.assessmentTakerId = 'valid-uuid';
      req.url = '/survey';
      req.method = 'POST';
      uuidValidatorStub.returns(true);
      prismaStub.assessmentTaker.findFirst.resolves({
        id: 'valid-uuid',
        phase: 'TEST_TAKING',
      });

      await assessmentMiddleware.assessmentTakerAccessPermission(
        req,
        res,
        next
      );

      expect(next.calledOnce).to.be.true;
      expect(req.assessmentTakerInfo).to.exist;
    });

    it('should throw error when assessment is already completed', async () => {
      req.params.assessmentTakerId = 'valid-uuid';
      req.url = '/other-endpoint';
      req.method = 'GET';
      uuidValidatorStub.returns(true);
      prismaStub.assessmentTaker.findFirst.resolves({
        id: 'valid-uuid',
        phase: 'ASSESSMENT_COMPLETION',
      });

      await assessmentMiddleware.assessmentTakerAccessPermission(
        req,
        res,
        next
      );

      expect(next.calledWith(sinon.match.instanceOf(AppError))).to.be.true;
      const error = next.firstCall.args[0];
      expect(error.httpCode).to.equal(HttpCode.UNAUTHORIZED);
      expect(error.message).to.equal(ErrorMessage.ASSESSMENT_COMPLETED);
    });
  });

  describe('fingerprintValidation', () => {
    it('should throw error when fingerprint header is missing', async () => {
      req.params.assessmentTakerId = 'valid-uuid';
      req.headers = {};

      await assessmentMiddleware.fingerprintValidation(req, res, next);

      expect(next.calledWith(sinon.match.instanceOf(AppError))).to.be.true;
      const error = next.firstCall.args[0];
      expect(error.httpCode).to.equal(HttpCode.FORBIDDEN);
      expect(error.message).to.equal(ErrorMessage.ACCESS_DENIED);
    });

    it('should throw error when assessmentTakerId is missing', async () => {
      req.params = {};
      req.headers = { 'x-device-fingerprint': 'test-fingerprint' };

      await assessmentMiddleware.fingerprintValidation(req, res, next);

      expect(next.calledWith(sinon.match.instanceOf(AppError))).to.be.true;
      const error = next.firstCall.args[0];
      expect(error.httpCode).to.equal(HttpCode.FORBIDDEN);
      expect(error.message).to.equal(ErrorMessage.INVALID_ACCESS);
    });

    it('should throw error for invalid device during TEST_TAKING phase', async () => {
      req.params.assessmentTakerId = 'valid-uuid';
      req.headers = { 'x-device-fingerprint': 'new-fingerprint' };
      redisStub.getCachedData.resolves('stored-fingerprint');
      prismaStub.assessmentTaker.findUnique.resolves({
        phase: 'TEST_TAKING',
      });

      await assessmentMiddleware.fingerprintValidation(req, res, next);

      expect(next.calledWith(sinon.match.instanceOf(AppError))).to.be.true;
      const error = next.firstCall.args[0];
      expect(error.httpCode).to.equal(HttpCode.FORBIDDEN);
      expect(error.message).to.equal(ErrorMessage.INVALID_DEVICE);
    });

    it('should set fingerprint in cache when not in TEST_TAKING phase', async () => {
      req.params.assessmentTakerId = 'valid-uuid';
      const fingerprint = 'test-fingerprint';
      req.headers = { 'x-device-fingerprint': fingerprint };
      redisStub.getCachedData.resolves(null);
      prismaStub.assessmentTaker.findUnique.resolves({
        phase: 'REGISTRATION',
      });

      await assessmentMiddleware.fingerprintValidation(req, res, next);

      expect(redisStub.setCacheData.calledOnce).to.be.true;
      expect(next.calledOnce).to.be.true;
    });

    it('should set fingerprint in cache when assessment taker not found', async () => {
      req.params.assessmentTakerId = 'valid-uuid';
      const fingerprint = 'test-fingerprint';
      req.headers = { 'x-device-fingerprint': fingerprint };
      redisStub.getCachedData.resolves(null);
      prismaStub.assessmentTaker.findUnique.resolves(null);

      await assessmentMiddleware.fingerprintValidation(req, res, next);

      expect(redisStub.setCacheData.calledOnce).to.be.true;
      expect(next.calledOnce).to.be.true;
    });

    it('should allow matching fingerprint during TEST_TAKING phase', async () => {
      req.params.assessmentTakerId = 'valid-uuid';
      const fingerprint = 'test-fingerprint';
      req.headers = { 'x-device-fingerprint': fingerprint };
      redisStub.getCachedData.resolves(fingerprint);
      prismaStub.assessmentTaker.findUnique.resolves({
        phase: 'TEST_TAKING',
      });

      await assessmentMiddleware.fingerprintValidation(req, res, next);

      expect(next.calledOnce).to.be.true;
      expect(redisStub.setCacheData.called).to.be.false;
    });
  });
});
