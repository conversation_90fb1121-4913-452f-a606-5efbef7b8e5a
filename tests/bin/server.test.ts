import sinon from 'sinon';
import { expect } from 'chai';
import request from 'supertest';
import app from '../../src/app';
import {
  setupTestEnv,
  setupTestStubs,
  resetStubs,
  restoreStubs,
} from '../helpers/testSetup';

describe('Server', () => {
  before(() => {
    setupTestEnv();
    setupTestStubs();
  });

  afterEach(() => {
    resetStubs();
    sinon.restore();
  });

  after(() => {
    restoreStubs();
  });

  it('should return 404 for unknown routes', async () => {
    const response = await request(app).get('/unknown-route');
    expect(response.status).to.equal(404);
    expect(response.body).to.deep.equal({
      success: false,
      message: 'Route not found',
    });
  });
});
