import { expect } from 'chai';
import sinon from 'sinon';
import proxyquire from 'proxyquire';

describe('QuestionFlagService', () => {
  let sandbox: sinon.SinonSandbox;
  let prismaStub: any;
  let kafkaStub: any;
  let loggerStub: any;
  let QuestionFlagService: any;

  beforeEach(() => {
    sandbox = sinon.createSandbox();

    // Create stubs
    prismaStub = {
      questionFlag: {
        create: sandbox.stub(),
        findFirst: sandbox.stub(),
        delete: sandbox.stub(),
      },
    };

    kafkaStub = {
      questionFlaggingPublish: sandbox.stub(),
      questionUnflaggingPublish: sandbox.stub(),
    };

    loggerStub = {
      error: sandbox.stub(),
      info: sandbox.stub(),
    };

    // Load service with mocked dependencies
    QuestionFlagService = proxyquire('../../src/services/questionFlagService', {
      '../prisma': { default: prismaStub },
      '../helpers/kafkaPublishMessage': kafkaStub,
      '../helpers/logger': { default: loggerStub },
    });
  });

  afterEach(() => {
    sandbox.restore();
  });

  describe('createFlag', () => {
    const mockFlagData = {
      testId: 'test-101',
      questionId: 'question-202',
      questionText: 'What is 2+2?',
      reasonOfFlagging: ['Question is unclear'],
    };

    const mockAssessmentTaker = {
      id: 'taker-789',
      assessmentId: 'assessment-456',
      email: '<EMAIL>',
      organizationId: 'org-123',
    };

    it('Should successfully flag a question', async () => {
      const mockCreatedFlag = {
        id: 'flag-123',
        testId: mockFlagData.testId,
        questionId: mockFlagData.questionId,
        questionText: mockFlagData.questionText,
        reasonOfFlagging: 'Question is unclear',
        testTakerEmail: mockAssessmentTaker.email,
        testTakerId: mockAssessmentTaker.id,
        assessmentId: mockAssessmentTaker.assessmentId,
        organizationId: mockAssessmentTaker.organizationId,
        createdAt: new Date(),
        updatedAt: new Date(),
      };

      prismaStub.questionFlag.create.resolves(mockCreatedFlag);
      kafkaStub.questionFlaggingPublish.resolves();

      const result = await QuestionFlagService.createFlag(
        mockFlagData,
        mockAssessmentTaker
      );

      expect(prismaStub.questionFlag.create.calledOnce).to.be.true;
      expect(kafkaStub.questionFlaggingPublish.calledOnce).to.be.true;

      expect(result).to.have.property('questionFlagged');
      expect(result.questionFlagged).to.deep.equal(mockCreatedFlag);
    });

    it('Should handle database errors during flag creation', async () => {
      const dbError = new Error('Database connection failed');
      prismaStub.questionFlag.create.rejects(dbError);

      try {
        await QuestionFlagService.createFlag(mockFlagData, mockAssessmentTaker);
        expect.fail('Should have thrown an error');
      } catch (error) {
        expect(error.message).to.equal('Database connection failed');
      }
    });

    it('Should handle multiple reasons correctly', async () => {
      const mockFlagDataMultiple = {
        ...mockFlagData,
        reasonOfFlagging: ['Question is unclear', 'Too difficult'],
      };

      const mockCreatedFlag = {
        id: 'flag-123',
        testId: mockFlagDataMultiple.testId,
        questionId: mockFlagDataMultiple.questionId,
        questionText: mockFlagDataMultiple.questionText,
        reasonOfFlagging: 'Question is unclear and Too difficult',
        testTakerEmail: mockAssessmentTaker.email,
        testTakerId: mockAssessmentTaker.id,
        assessmentId: mockAssessmentTaker.assessmentId,
        organizationId: mockAssessmentTaker.organizationId,
        createdAt: new Date(),
        updatedAt: new Date(),
      };

      prismaStub.questionFlag.create.resolves(mockCreatedFlag);
      kafkaStub.questionFlaggingPublish.resolves();

      const result = await QuestionFlagService.createFlag(
        mockFlagDataMultiple,
        mockAssessmentTaker
      );

      expect(result.questionFlagged.reasonOfFlagging).to.equal(
        'Question is unclear and Too difficult'
      );
    });
  });

  describe('getFlagQuestionBy', () => {
    it('Should successfully get a flagged question by criteria', async () => {
      const whereClause = {
        testTakerId: 'taker-789',
        questionId: 'question-202',
      };

      const mockExistingFlag = {
        id: 'flag-123',
        testTakerId: 'taker-789',
        questionId: 'question-202',
        questionText: 'What is 2+2?',
        reasonOfFlagging: 'Question is unclear',
        createdAt: new Date(),
        updatedAt: new Date(),
      };

      prismaStub.questionFlag.findFirst.resolves(mockExistingFlag);

      const result = await QuestionFlagService.getFlagQuestionBy(whereClause);

      expect(prismaStub.questionFlag.findFirst.calledOnce).to.be.true;
      expect(
        prismaStub.questionFlag.findFirst.calledWith({
          where: whereClause,
        })
      ).to.be.true;

      expect(result).to.deep.equal(mockExistingFlag);
    });

    it('Should return null when no flag is found', async () => {
      const whereClause = {
        testTakerId: 'taker-789',
        questionId: 'question-202',
      };

      prismaStub.questionFlag.findFirst.resolves(null);

      const result = await QuestionFlagService.getFlagQuestionBy(whereClause);

      expect(prismaStub.questionFlag.findFirst.calledOnce).to.be.true;
      expect(result).to.be.null;
    });
  });

  describe('deleteFlagQuestion', () => {
    it('Should successfully delete a flagged question', async () => {
      const flagId = 'flag-123';

      prismaStub.questionFlag.delete.resolves();

      await QuestionFlagService.deleteFlagQuestion(flagId);

      expect(prismaStub.questionFlag.delete.calledOnce).to.be.true;
      expect(
        prismaStub.questionFlag.delete.calledWith({
          where: { id: flagId },
        })
      ).to.be.true;
    });

    it('Should handle database errors during deletion', async () => {
      const flagId = 'flag-123';
      const dbError = new Error('Database connection failed');

      prismaStub.questionFlag.delete.rejects(dbError);

      try {
        await QuestionFlagService.deleteFlagQuestion(flagId);
        expect.fail('Should have thrown an error');
      } catch (error) {
        expect(error.message).to.equal('Database connection failed');
      }
    });
  });
});
