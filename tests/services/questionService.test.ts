import { expect } from 'chai';
import sinon from 'sinon';
import proxyquire from 'proxyquire';
import { AppError, HttpCode } from '../../src/middlewares/errorHandler';

describe('QuestionService', () => {
  let sandbox: sinon.SinonSandbox;
  let axiosStub: any;
  let loggerStub: any;
  let QuestionService: any;

  beforeEach(() => {
    sandbox = sinon.createSandbox();

    // Create stubs
    axiosStub = {
      post: sandbox.stub(),
    };

    loggerStub = {
      error: sandbox.stub(),
    };

    // Load service with mocked dependencies
    const questionServiceModule = proxyquire(
      '../../src/services/questionService',
      {
        axios: axiosStub,
        '../helpers/logger': { default: loggerStub },
      }
    );

    QuestionService = questionServiceModule.QuestionService;

    // Set environment variable for testing
    process.env.QUESTION_SERVICE_URL = 'http://localhost:3000/api/questions';
  });

  afterEach(() => {
    sandbox.restore();
    delete process.env.QUESTION_SERVICE_URL;
  });

  describe('getQuestionTextsByIds', () => {
    it('Should successfully fetch question texts by IDs', async () => {
      const questionIds = ['q1', 'q2', 'q3'];
      const organizationId = 'org-123';

      const mockResponse = {
        data: {
          success: true,
          data: [
            {
              id: 'q1',
              questionText: 'What is 2+2?',
              questionType: 'SINGLE_CHOICE',
              multipleChoiceAnswer: ['2', '3', '4', '5'],
              score: 10,
            },
            {
              id: 'q2',
              questionText: 'What is the capital of France?',
              questionType: 'SINGLE_CHOICE',
              multipleChoiceAnswer: ['London', 'Berlin', 'Paris', 'Madrid'],
              score: 10,
            },
            {
              id: 'q3',
              questionText: 'Explain the concept of recursion.',
              questionType: 'ESSAY',
              score: 20,
            },
          ],
        },
      };

      axiosStub.post.resolves(mockResponse);

      const result = await QuestionService.getQuestionTextsByIds(
        questionIds,
        organizationId
      );

      expect(axiosStub.post.calledOnce).to.be.true;
      expect(
        axiosStub.post.calledWith(
          `${process.env.QUESTION_SERVICE_URL}/${organizationId}/bulk`,
          { questionIds }
        )
      ).to.be.true;

      expect(result).to.have.property('q1');
      expect(result).to.have.property('q2');
      expect(result).to.have.property('q3');
      expect(result.q1.questionText).to.equal('What is 2+2?');
      expect(result.q2.questionText).to.equal('What is the capital of France?');
      expect(result.q3.questionText).to.equal(
        'Explain the concept of recursion.'
      );
    });

    it('Should handle empty question IDs array', async () => {
      const questionIds: string[] = [];
      const organizationId = 'org-123';

      const mockResponse = {
        data: {
          success: true,
          data: [],
        },
      };

      axiosStub.post.resolves(mockResponse);

      const result = await QuestionService.getQuestionTextsByIds(
        questionIds,
        organizationId
      );

      expect(axiosStub.post.calledOnce).to.be.true;
      expect(result).to.deep.equal({});
    });

    it('Should handle API response with success: false', async () => {
      const questionIds = ['q1', 'q2'];
      const organizationId = 'org-123';

      const mockResponse = {
        data: {
          success: false,
          message: 'Questions not found',
        },
      };

      axiosStub.post.resolves(mockResponse);

      try {
        await QuestionService.getQuestionTextsByIds(
          questionIds,
          organizationId
        );
        expect.fail('Should have thrown an error');
      } catch (error) {
        expect(error).to.be.instanceOf(AppError);
        expect(error.httpCode).to.equal(HttpCode.INTERNAL_SERVER_ERROR);
        expect(error.message).to.include(
          'Failed to fetch question texts from the question service'
        );
      }
    });

    it('Should handle API response without success field', async () => {
      const questionIds = ['q1', 'q2'];
      const organizationId = 'org-123';

      const mockResponse = {
        data: {
          // Missing success field
          data: [],
        },
      };

      axiosStub.post.resolves(mockResponse);

      try {
        await QuestionService.getQuestionTextsByIds(
          questionIds,
          organizationId
        );
        expect.fail('Should have thrown an error');
      } catch (error) {
        expect(error).to.be.instanceOf(AppError);
        expect(error.httpCode).to.equal(HttpCode.INTERNAL_SERVER_ERROR);
        expect(error.message).to.include(
          'Failed to fetch question texts from the question service'
        );
      }
    });

    it('Should handle network errors', async () => {
      const questionIds = ['q1', 'q2'];
      const organizationId = 'org-123';

      const networkError = new Error('Network Error');
      axiosStub.post.rejects(networkError);

      try {
        await QuestionService.getQuestionTextsByIds(
          questionIds,
          organizationId
        );
        expect.fail('Should have thrown an error');
      } catch (error) {
        expect(error).to.be.instanceOf(AppError);
        expect(error.httpCode).to.equal(HttpCode.INTERNAL_SERVER_ERROR);
        expect(error.message).to.include(
          'Failed to fetch question texts from the question service'
        );
      }
    });

    it('Should handle HTTP error responses', async () => {
      const questionIds = ['q1', 'q2'];
      const organizationId = 'org-123';

      const httpError = {
        response: {
          status: 404,
          data: {
            message: 'Organization not found',
          },
        },
      };
      axiosStub.post.rejects(httpError);

      try {
        await QuestionService.getQuestionTextsByIds(
          questionIds,
          organizationId
        );
        expect.fail('Should have thrown an error');
      } catch (error) {
        expect(error).to.be.instanceOf(AppError);
        expect(error.httpCode).to.equal(HttpCode.INTERNAL_SERVER_ERROR);
        expect(error.message).to.include(
          'Failed to fetch question texts from the question service'
        );
      }
    });

    it('Should handle HTTP error responses without message', async () => {
      const questionIds = ['q1', 'q2'];
      const organizationId = 'org-123';

      const httpError = {
        response: {
          status: 500,
          data: {},
        },
      };
      axiosStub.post.rejects(httpError);

      try {
        await QuestionService.getQuestionTextsByIds(
          questionIds,
          organizationId
        );
        expect.fail('Should have thrown an error');
      } catch (error) {
        expect(error).to.be.instanceOf(AppError);
        expect(error.httpCode).to.equal(HttpCode.INTERNAL_SERVER_ERROR);
        expect(error.message).to.include(
          'Failed to fetch question texts from the question service'
        );
      }
    });

    it('Should handle timeout errors', async () => {
      const questionIds = ['q1', 'q2'];
      const organizationId = 'org-123';

      const timeoutError = {
        code: 'ECONNABORTED',
        message: 'timeout of 5000ms exceeded',
      };
      axiosStub.post.rejects(timeoutError);

      try {
        await QuestionService.getQuestionTextsByIds(
          questionIds,
          organizationId
        );
        expect.fail('Should have thrown an error');
      } catch (error) {
        expect(error).to.be.instanceOf(AppError);
        expect(error.httpCode).to.equal(HttpCode.INTERNAL_SERVER_ERROR);
        expect(error.message).to.include(
          'Failed to fetch question texts from the question service'
        );
      }
    });

    it('Should use correct URL format', async () => {
      const questionIds = ['q1'];
      const organizationId = 'test-org-456';

      const mockResponse = {
        data: {
          success: true,
          data: [],
        },
      };

      axiosStub.post.resolves(mockResponse);

      await QuestionService.getQuestionTextsByIds(questionIds, organizationId);

      expect(
        axiosStub.post.calledWith(
          `${process.env.QUESTION_SERVICE_URL}/${organizationId}/bulk`,
          { questionIds }
        )
      ).to.be.true;
    });

    it('Should handle large number of question IDs', async () => {
      const questionIds = Array.from({ length: 1000 }, (_, i) => `q${i}`);
      const organizationId = 'org-123';

      const mockResponse = {
        data: {
          success: true,
          data: [],
        },
      };

      axiosStub.post.resolves(mockResponse);

      const result = await QuestionService.getQuestionTextsByIds(
        questionIds,
        organizationId
      );

      expect(axiosStub.post.calledOnce).to.be.true;
      expect(axiosStub.post.firstCall.args[1].questionIds).to.have.length(1000);
      expect(result).to.deep.equal({});
    });
  });
});
