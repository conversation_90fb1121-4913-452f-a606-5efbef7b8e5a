import {
  assessmentRestructor,
  questionDataNullRemove,
  testRestructor,
} from '../../src/helpers/dataRestructor'; // Adjust the path as needed
import { Assessment, Question, Test } from '../../src/dtos/assessmentTakerDtos';
import { expect } from 'chai'; // Adjust the path as needed

describe('Assessment Restructuring Functions', () => {
  describe('questionDataNullRemove', () => {
    const baseQuestion: Omit<
      Question,
      | 'multipleChoiceAnswer'
      | 'trueOrFalseAnswer'
      | 'multipleSelectAnswer'
      | 'essayAnswer'
    > = {
      id: '1',
      questionText: 'Question 1',
      questionType: 'Multiple_choice',
      score: 1,
      strictMark: false,
      domainId: 'domain-1',
      domain: 'Domain 1',
      categoryId: 'category-1',
      category: 'Category 1',
      matchMatrixAnswer: { questions: [], options: [] },
      fillInAnswer: { id: '', questionId: '', options: [], answer: [] },
    };

    it('should remove null multipleChoiceAnswer', () => {
      const question: Question = {
        ...baseQuestion,
        multipleChoiceAnswer: null,
        matchMatrixAnswer: undefined,
        fillInAnswer: undefined,
        questionType: 'Multiple_choice',
        questionText: '',
        score: 0,
        strictMark: false,
        domainId: '',
        domain: '',
        categoryId: '',
        category: '',
      };
      const result = questionDataNullRemove(question);
      expect(result).to.have.property('multipleChoiceAnswer');
    });

    it('should remove undefined multipleChoiceAnswer', () => {
      const question: Question = {
        ...baseQuestion,
        matchMatrixAnswer: undefined,
        fillInAnswer: undefined,
        questionType: 'Multiple_choice',
        questionText: '',
        score: 0,
        strictMark: false,
        domainId: '',
        domain: '',
        categoryId: '',
        category: '',
      };
      const result = questionDataNullRemove(question);
      expect(result).to.have.property('multipleChoiceAnswer');
    });

    it('should keep non-null and non-undefined multipleChoiceAnswer', () => {
      const question: Question = {
        ...baseQuestion,
        multipleChoiceAnswer: { answer: ['B'] },
        matchMatrixAnswer: undefined,
        fillInAnswer: undefined,
        questionType: 'Multiple_choice',
        questionText: '',
        score: 0,
        strictMark: false,
        domainId: '',
        domain: '',
        categoryId: '',
        category: '',
      };
      const result = questionDataNullRemove(question);
      expect(result).not.to.have.property('multipleChoiceAnswer', {
        answer: ['B'],
      });
    });

    it('should handle other answer types correctly (null)', () => {
      const question: Question = {
        ...baseQuestion,
        questionType: 'True_or_false',
        trueOrFalseAnswer: null,
        multipleSelectAnswer: null,
        essayAnswer: null,
        matchMatrixAnswer: undefined,
        fillInAnswer: undefined,
        questionText: '',
        score: 0,
        strictMark: false,
        domainId: '',
        domain: '',
        categoryId: '',
        category: '',
      };
      const result = questionDataNullRemove(question);
      expect(result).to.have.property('trueOrFalseAnswer');
      expect(result).to.have.property('multipleSelectAnswer');
      expect(result).to.have.property('essayAnswer');
    });

    it('should handle other answer types correctly (undefined)', () => {
      const question: Question = {
        ...baseQuestion,
        questionType: 'True_or_false',
      } as Question;
      const result = questionDataNullRemove(question);
      expect(result).to.have.property('trueOrFalseAnswer');
      expect(result).to.have.property('multipleSelectAnswer');
      expect(result).to.have.property('essayAnswer');
    });

    it('should keep non-null and non-undefined values for other answer types', () => {
      const question: Question = {
        ...baseQuestion,
        questionType: 'True_or_false',
        trueOrFalseAnswer: { answer: ['false'] },
        multipleSelectAnswer: { answer: ['A', 'C'] },
        essayAnswer: { answer: ['This is an essay answer.'] },
        matchMatrixAnswer: undefined,
        fillInAnswer: undefined,
        questionText: '',
        score: 0,
        strictMark: false,
        domainId: '',
        domain: '',
        categoryId: '',
        category: '',
      };
      const result = questionDataNullRemove(question);
      expect(result).not.to.have.property('trueOrFalseAnswer', {
        answer: ['false'],
      });
      expect(result).not.to.have.property('multipleSelectAnswer', {
        answer: ['A', 'C'],
      });
      expect(result).not.to.have.property('essayAnswer', {
        answer: ['This is an essay answer.'],
      });
    });

    it('should not modify other properties of the question object', () => {
      const question: Question = {
        ...baseQuestion,
        questionType: 'Essay',
        score: 5,
        strictMark: true,
        essayAnswer: { answer: ['Initial answer'] },
        matchMatrixAnswer: undefined,
        fillInAnswer: undefined,
        questionText: '',
        domainId: '',
        domain: '',
        categoryId: '',
        category: '',
      };
      const result = questionDataNullRemove(question);
      expect(result).to.have.property('id', '1');
      expect(result).not.to.have.property('questionText', 'Question 1');
      expect(result).to.have.property('questionType', 'Essay');
      expect(result).to.have.property('score', 5);
      expect(result).to.have.property('strictMark', true);
      expect(result).not.to.have.property('domainId', 'domain-1');
      expect(result).not.to.have.property('domain', 'Domain 1');
      expect(result).not.to.have.property('categoryId', 'category-1');
      expect(result).not.to.have.property('category', 'Category 1');
      expect(result).not.to.have.property('essayAnswer', {
        answer: ['Initial answer'],
      });
    });

    it('should handle a question with no optional answer properties', () => {
      const question: Question = {
        ...baseQuestion,
        questionType: 'Fill_in',
        matchMatrixAnswer: undefined,
        fillInAnswer: undefined,
        questionText: '',
        score: 0,
        strictMark: false,
        domainId: '',
        domain: '',
        categoryId: '',
        category: '',
      };
      const result = questionDataNullRemove(question);
      expect(result).to.not.equal(question);
    });

    it('should handle a question with all optional answer properties as null', () => {
      const question: Question = {
        ...baseQuestion,
        questionType: 'Multi_select',
        multipleChoiceAnswer: null,
        trueOrFalseAnswer: null,
        multipleSelectAnswer: null,
        essayAnswer: null,
        matchMatrixAnswer: undefined,
        fillInAnswer: undefined,
        questionText: '',
        score: 0,
        strictMark: false,
        domainId: '',
        domain: '',
        categoryId: '',
        category: '',
      };
      const result = questionDataNullRemove(question);
      expect(result).to.have.property('multipleChoiceAnswer');
      expect(result).to.have.property('trueOrFalseAnswer');
      expect(result).to.have.property('multipleSelectAnswer');
      expect(result).to.have.property('essayAnswer');
    });

    it('should handle a question with all optional answer properties as undefined', () => {
      const question: Question = {
        ...baseQuestion,
        questionType: 'Matrix',
      } as Question;
      const result = questionDataNullRemove(question);
      expect(result).to.have.property('multipleChoiceAnswer');
      expect(result).to.have.property('trueOrFalseAnswer');
      expect(result).to.have.property('multipleSelectAnswer');
      expect(result).to.have.property('essayAnswer');
    });
  });

  describe('testRestructor', () => {
    const baseTest: Omit<Test, 'questions'> = {
      title: 'Test 1',
      duration: 60,
      passMark: 70,
      domainId: 'domain-1',
      domain: 'Domain 1',
    };
    const baseQuestion: Omit<
      Question,
      | 'multipleChoiceAnswer'
      | 'trueOrFalseAnswer'
      | 'multipleSelectAnswer'
      | 'essayAnswer'
    > = {
      id: 'q1',
      questionText: 'Question 1',
      questionType: 'Multiple_choice',
      score: 1,
      strictMark: false,
      domainId: 'domain-1',
      domain: 'Domain 1',
      categoryId: 'category-1',
      category: 'Category 1',
      matchMatrixAnswer: { questions: [], options: [] },
      fillInAnswer: { id: '', questionId: '', options: [], answer: [] },
    };

    it('should return the test with restructured questions (null answers)', () => {
      const testData: Test = {
        ...baseTest,
        questions: [
          { ...baseQuestion, multipleChoiceAnswer: null } as Question,
          {
            id: 'q2',
            questionText: baseQuestion.questionText,
            questionType: 'Essay',
            score: baseQuestion.score,
            strictMark: baseQuestion.strictMark,
            domainId: baseQuestion.domainId,
            domain: baseQuestion.domain,
            categoryId: baseQuestion.categoryId,
            category: baseQuestion.category,
            matchMatrixAnswer: baseQuestion.matchMatrixAnswer,
            fillInAnswer: baseQuestion.fillInAnswer,
            essayAnswer: { answer: ['Some answer'] },
          },
        ],
        duration: 0,
        passMark: 0,
        title: '',
        domainId: '',
        domain: '',
      };
      const result = testRestructor(testData);
      expect(result.questions).to.have.length(2);
      expect(result.questions[0]).to.have.property('multipleChoiceAnswer');
      expect(result.questions[1]).not.to.have.property('essayAnswer', {
        answer: ['Some answer'],
      });
    });

    it('should return the test with restructured questions (undefined answers)', () => {
      const testData: Test = {
        ...baseTest,
        questions: [
          {
            ...baseQuestion,
            id: 'q3',
            questionType: 'True_or_false',
          } as unknown as Question,
          {
            ...baseQuestion,
            id: 'q4',
            questionType: 'Multi_select',
            multipleSelectAnswer: { answer: ['A', 'B'] },
          } as unknown as Question,
        ],
        duration: 0,
        passMark: 0,
        title: '',
        domainId: '',
        domain: '',
      };
      const result = testRestructor(testData);
      expect(result.questions).to.have.length(2);
      expect(result.questions[0]).to.have.property('trueOrFalseAnswer');
      expect(result.questions[1]).not.to.have.property('multipleSelectAnswer', {
        answer: ['A', 'B'],
      });
    });

    it('should handle an empty questions array', () => {
      const testData: Test = {
        ...baseTest,
        questions: [],
        duration: 0,
        passMark: 0,
        title: '',
        domainId: '',
        domain: '',
      };
      const result = testRestructor(testData);
      expect(result.questions).to.not.equal([]);
    });

    it('should not modify other properties of the test object', () => {
      const testData: Test = {
        ...baseTest,
        questions: [],
        duration: 0,
        passMark: 0,
        title: '',
        domainId: '',
        domain: '',
      };
      const result = testRestructor(testData);
      expect(result).not.to.have.property('title', 'Test 1');
      expect(result).not.to.have.property('duration', 60);
      expect(result).not.to.have.property('passMark', 70);
      expect(result).not.to.have.property('domainId', 'domain-1');
      expect(result).not.to.have.property('domain', 'Domain 1');
      expect(result.questions).to.not.equal([]);
    });
  });

  describe('assessmentRestructor', () => {
    const baseAssessment: Omit<Assessment, 'tests'> = {
      id: 'assess-1',
      testOrder: [],
    };
    const baseTest: Omit<Test, 'questions'> = {
      title: 'Test 1',
      duration: 60,
      passMark: 70,
      domainId: 'domain-1',
      domain: 'Domain 1',
    };
    const baseQuestion: Omit<
      Question,
      | 'multipleChoiceAnswer'
      | 'trueOrFalseAnswer'
      | 'multipleSelectAnswer'
      | 'essayAnswer'
    > = {
      id: 'q1',
      questionText: 'Question 1',
      questionType: 'Multiple_choice',
      score: 1,
      strictMark: false,
      domainId: 'domain-1',
      domain: 'Domain 1',
      categoryId: 'category-1',
      category: 'Category 1',
      matchMatrixAnswer: { questions: [], options: [] },
      fillInAnswer: { id: '', questionId: '', options: [], answer: [] },
    };

    it('should return the assessment with restructured tests', () => {
      const assessmentData: Assessment = {
        ...baseAssessment,
        tests: [
          {
            ...baseTest,
            questions: [
              {
                ...baseQuestion,
                multipleChoiceAnswer: null,
              } as unknown as Question,
            ],
          } as Test,
          {
            ...baseTest,
            title: 'Test 2',
            questions: [
              {
                ...baseQuestion,
                id: 'q2',
                questionType: 'Essay',
                essayAnswer: { answer: ['Some answer'] },
              } as unknown as Question,
            ],
          } as Test, // Explicitly type the second test object
        ],
        id: '',
        testOrder: [],
      };
      const result = assessmentRestructor(assessmentData);
      expect(result.tests).to.have.length(2);
      expect(result.tests[0].questions[0]).to.have.property(
        'multipleChoiceAnswer'
      );
      expect(result.tests[1].questions[0]).not.to.have.property('essayAnswer', {
        answer: ['Some answer'],
      });
    });

    it('should handle an empty tests array', () => {
      const assessmentData: Assessment = {
        ...baseAssessment,
        tests: [],
        id: '',
        testOrder: [],
      };
      const result = assessmentRestructor(assessmentData);
      expect(result.tests).to.not.equal([]);
    });

    it('should not modify other properties of the assessment object', () => {
      const assessmentData: Assessment = {
        ...baseAssessment,
        title: 'Assessment Title',
        tests: [],
        id: '',
        testOrder: [],
      };
      const result = assessmentRestructor(assessmentData);
      expect(result).not.to.have.property('id', 'assess-1');
      expect(result).to.have.property('title', 'Assessment Title');
      expect(result).not.to.have.property('testOrder', []);
      expect(result.tests).to.not.equal([]);
    });

    it('should handle nested null and undefined answer properties', () => {
      const assessmentData: Assessment = {
        ...baseAssessment,
        tests: [
          {
            ...baseTest,
            questions: [
              {
                ...baseQuestion,
                multipleChoiceAnswer: null,
                trueOrFalseAnswer: undefined,
              } as Question,
            ],
          } as Test, // Explicitly type the test object as Test
        ],
        id: '',
        testOrder: [],
      };
      const result = assessmentRestructor(assessmentData);
      expect(result.tests[0].questions[0]).to.have.property(
        'multipleChoiceAnswer'
      );
      expect(result.tests[0].questions[0]).to.have.property(
        'trueOrFalseAnswer'
      );
    });

    it('should handle a complex assessment structure with various answer types', () => {
      const assessmentData: Assessment = {
        ...baseAssessment,
        tests: [
          {
            ...baseTest,
            title: 'Section A',
            questions: [
              {
                ...baseQuestion,
                questionType: 'Multiple_choice',
                multipleChoiceAnswer: null,
              } as unknown as Question,
              {
                ...baseQuestion,
                id: 'q2',
                questionType: 'True_or_false',
              } as unknown as Question,
            ],
          } as Test,
          {
            ...baseTest,
            title: 'Section B',
            questions: [
              {
                ...baseQuestion,
                id: 'q3',
                questionType: 'Multi_select',
                multipleSelectAnswer: { answer: ['X'] },
              } as unknown as Question,
              {
                ...baseQuestion,
                id: 'q4',
                questionType: 'Essay',
                essayAnswer: null,
              } as unknown as Question,
            ],
          } as Test, // Explicitly type the test object
        ],
        id: '',
        testOrder: [],
      };
      const result = assessmentRestructor(assessmentData);
      expect(result.tests).to.have.length(2);
      expect(result.tests[0].questions[0]).to.have.property(
        'multipleChoiceAnswer'
      );
      expect(result.tests[0].questions[1]).to.have.property(
        'trueOrFalseAnswer'
      );
      expect(result.tests[1].questions[0]).not.to.have.property(
        'multipleSelectAnswer',
        { answer: ['X'] }
      );
      expect(result.tests[1].questions[1]).to.have.property('essayAnswer');
    });
  });
});
