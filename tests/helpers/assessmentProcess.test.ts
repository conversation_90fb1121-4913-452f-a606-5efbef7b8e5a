import 'mocha';
import * as chai from 'chai';
import sinon, { SinonSandbox, SinonStub } from 'sinon';
import sinonChai from 'sinon-chai';
import chaiAsPromised from 'chai-as-promised';

// --- Modules to be Tested/Mocked ---
import { AssessmentProcess } from '../../src/helpers/assessmentProcess';
import { AppError } from '../../src/middlewares/errorHandler';
import { MarkQuestion } from '../../src/helpers/markQuestion';

import {
  FillInOption,
  MatchMatrixAnswer,
  MatrixSubquestion,
  Question,
} from '../../src/dtos/assessmentTakerDtos';
import {
  AnswerStatus,
  DbSaveTestResult,
  MarkQuestionResult,
  ModifiedMatrixAnswer,
  QuestionResult,
  TestTakerAnswers,
} from '../../src/helpers/types';

chai.use(sinonChai);
chai.use(chaiAsPromised);

const createMockMatrixSubquestion = (
  id: string,
  answer: string[] = []
): MatrixSubquestion => ({
  id,
  matchMatrixAnswerId: `mma-${id}`,
  subquestion: `Subquestion ${id}`,
  answer: answer,
});

const createMockFillInOption = (id: string, value: string): FillInOption => ({
  id,
  fillInAnswerId: `fia-${id}`,
  blank: null,
  value,
});

const createMockQuestion = (
  id: string,
  type: Question['questionType'],
  score: number,
  overrides: Partial<Question> = {}
): Question => {
  const baseQuestion: Partial<Question> = {
    id,
    questionType: type,
    score,
    questionText: `Question Text ${id}`,
    strictMark: false,
    domainId: 'domain-1',
    domain: 'Sample Domain',
    categoryId: 'cat-1',
    category: 'Sample Category',
    order: 1,
    testId: 'test-1', // Assuming a default testId for simplicity
    multipleChoiceAnswer: null,
    trueOrFalseAnswer: null,
    multipleSelectAnswer: null,
    essayAnswer: null,
    matchMatrixAnswer: null,
    fillInAnswer: null,
  };

  switch (type) {
    case 'Multiple_choice':
      baseQuestion.multipleChoiceAnswer = { answer: ['Correct Option A'] };
      break;
    case 'True_or_false':
      baseQuestion.trueOrFalseAnswer = { answer: ['True'] };
      break;
    case 'Multi_select':
      baseQuestion.multipleSelectAnswer = { answer: ['Option 1', 'Option 3'] };
      break;
    case 'Essay':
      baseQuestion.essayAnswer = { rubrics: 'Sample Rubrics' };
      break;
    case 'Matrix':
      baseQuestion.matchMatrixAnswer = {
        questions: [
          createMockMatrixSubquestion('sq1', ['OptA']),
          createMockMatrixSubquestion('sq2', ['OptB']),
        ],
        options: ['OptA', 'OptB', 'OptC'],
      };
      break;
    case 'Fill_in':
      baseQuestion.fillInAnswer = {
        id: `fia-${id}`,
        questionId: id,
        options: [createMockFillInOption('fio1', 'Value 1')], // Example option structure
        answer: ['Correct Answer 1', 'Correct Answer 2'], // Array of correct strings
      };
      break;
  }

  return { ...baseQuestion, ...overrides } as Question;
};

const createMockMarkQuestionResult = (
  isCorrect: boolean,
  isAnswered: boolean,
  scored: number,
  answers: TestTakerAnswers[] = [] // Use the union type
): MarkQuestionResult => ({
  isAnswerCorrect: isCorrect || true,
  isAnswered: isAnswered,
  scored: scored,
  testTakerAnswers: answers, // Store the potentially mixed array
});

// For data stored *within* Prisma TestResult.result
const createMockDbSaveTestResult = (
  questionId: string,
  questionType: Question['questionType'],
  score: number,
  overrides: Partial<DbSaveTestResult> = {}
): DbSaveTestResult => ({
  questionId,
  questionType,
  score,
  idleTime: 10,
  answerStatus: AnswerStatus.WRONG, // Default to WRONG
  isAnswered: false, // Default to false
  scored: 0, // Default to 0
  testTakerAnswers: [], // NOTE: Stored as string[] in DB according to type
  ...overrides,
});

// --- Test Suite ---
describe('AssessmentProcess', () => {
  let sandbox: SinonSandbox;

  beforeEach(() => {
    sandbox = sinon.createSandbox();
  });

  afterEach(() => {
    sandbox.restore();
  });

  // --- Test simple helpers first (Re-verified against types) ---

  describe('getAssessmentPercentage', () => {
    /* ... tests unchanged ... */
    it('should calculate the correct percentage', () => {
      chai
        .expect(AssessmentProcess.getAssessmentPercentage(75, 100))
        .to.equal(75.0);
      chai
        .expect(AssessmentProcess.getAssessmentPercentage(10, 50))
        .to.equal(20.0);
      chai
        .expect(AssessmentProcess.getAssessmentPercentage(1, 3))
        .to.equal(33.33); // Test rounding
    });

    it('should return 0 for a score of 0', () => {
      chai
        .expect(AssessmentProcess.getAssessmentPercentage(0, 100))
        .to.equal(0.0);
    });

    it('should return 100% when score equals total', () => {
      chai
        .expect(AssessmentProcess.getAssessmentPercentage(50, 50))
        .to.equal(100.0);
    });
  });

  describe('formatQuestionDataBeforePublish', () => {
    it('should stringify Matrix subquestions (assuming they are objects)', () => {
      const matrixQ = createMockQuestion('q-m', 'Matrix', 10, {
        matchMatrixAnswer: {
          questions: [
            createMockMatrixSubquestion('sq1'),
            createMockMatrixSubquestion('sq2'),
          ],
        } as MatchMatrixAnswer,
      });

      // Ensure the mock data starts with objects
      chai
        .expect((matrixQ.matchMatrixAnswer.questions as MatrixSubquestion[])[0])
        .to.be.an('object');
      chai
        .expect((matrixQ.matchMatrixAnswer.questions as MatrixSubquestion[])[0])
        .to.not.be.a('string');

      const formatted = AssessmentProcess.formatQuestionDataBeforePublish({
        ...matrixQ,
      });

      chai.expect(formatted.matchMatrixAnswer.questions[0]).to.be.a('string');

      const parsedSubquestion = JSON.parse(
        formatted.matchMatrixAnswer.questions[0] as string
      );

      chai.expect(parsedSubquestion.id).to.equal('sq1'); // Use 'id' here, as per your mock
      chai.expect(parsedSubquestion.subquestion).to.equal('Subquestion sq1');
    });

    it('should stringify Fill_in options (assuming they are objects)', () => {
      const fillinQ = createMockQuestion('q-f', 'Fill_in', 5); // Helper creates mock options as objects

      // Optional sanity check for the mock generator
      chai
        .expect((fillinQ.fillInAnswer.options as FillInOption[])[0])
        .to.be.an('object');

      // Optional: Add another mock option if needed for testing iteration
      // (fillinQ.fillInAnswer.options as FillInOption[]).push(createMockFillInOption('fio-test', 'Test Val'));

      // Clone before passing
      const formatted = AssessmentProcess.formatQuestionDataBeforePublish({
        ...fillinQ,
      });

      // Assert that the option IS a string after formatting
      chai.expect(formatted.fillInAnswer.options[0]).to.be.a('string');

      // Use type assertion 'as string' for JSON.parse
      const parsedOption = JSON.parse(
        formatted.fillInAnswer.options[0] as string
      ); // <--- Added 'as string'

      // Check the content of the parsed object
      // Ensure 'Value 1' matches the value created by createMockFillInOption('fio1', 'Value 1') in the helper
      chai.expect(parsedOption.value).to.equal('Value 1');
      chai.expect(parsedOption.id).to.equal('fio1'); // Verify other fields if necessary
    });

    it('should return other question types unmodified', () => {
      const mcqQ = createMockQuestion('q-mcq', 'Multiple_choice', 5);
      const originalMcqQ = JSON.parse(JSON.stringify(mcqQ)); // Deep clone for comparison
      const formatted = AssessmentProcess.formatQuestionDataBeforePublish({
        ...mcqQ,
      }); // Clone before passing
      chai.expect(formatted).to.deep.equal(originalMcqQ);
    });
  });

  describe('convertTestTakerAnswerToAllString', () => {
    it('should convert Matrix subquestions to stringified ModifiedMatrixAnswer format', () => {
      const matrixQ = createMockQuestion('q-m', 'Matrix', 10); // Helper includes subquestions
      const result =
        AssessmentProcess.convertTestTakerAnswerToAllString(matrixQ);
      chai
        .expect(result)
        .to.be.an('array')
        .with.lengthOf(
          (matrixQ.matchMatrixAnswer.questions as MatrixSubquestion[]).length
        );
      chai.expect(result[0]).to.be.a('string');
      const parsed1 = JSON.parse(result[0]) as ModifiedMatrixAnswer;
      chai.expect(parsed1).to.deep.equal({
        answers: [],
        answerStatus: 'SKIPPED',
        isAnswered: false,
        scored: 0,
        subquestion: (
          matrixQ.matchMatrixAnswer.questions as MatrixSubquestion[]
        )[0].subquestion,
        subquestionId: (
          matrixQ.matchMatrixAnswer.questions as MatrixSubquestion[]
        )[0].id,
      });
    });

    it('should return an empty array for non-Matrix questions', () => {
      const mcqQ = createMockQuestion('q-mcq', 'Multiple_choice', 5);
      const result = AssessmentProcess.convertTestTakerAnswerToAllString(mcqQ);
      chai.expect(result).to.deep.equal([]);
    });
  });

  describe('reverseTestTakerAnswerToAllString', () => {
    it('should parse stringified answers back to objects for Matrix questions', () => {
      // Prepare the structure as stored in DbSaveTestResult (string[])
      const modifiedAnswer: ModifiedMatrixAnswer = {
        subquestion: 'Sub 1',
        isAnswered: true,
        scored: 5,
        isAnswerCorrect: true,
        subquestionId: 'sq1',
        answers: ['OptA'],
      };
      const initialData = createMockDbSaveTestResult('q-m', 'Matrix', 10, {
        testTakerAnswers: [JSON.stringify(modifiedAnswer)], // Store as string array
      });

      const result =
        AssessmentProcess.reverseTestTakerAnswerToAllString(initialData);
      chai.expect(result).to.be.an('array').with.lengthOf(1);
      // The result here should be ModifiedMatrixAnswer[]
      chai.expect(result[0]).to.deep.equal(modifiedAnswer);
    });

    it('should return original string array for non-Matrix questions', () => {
      const initialData = createMockDbSaveTestResult(
        'q-mcq',
        'Multiple_choice',
        5,
        {
          testTakerAnswers: ['Option A', 'Option B'], // Already string[]
        }
      );

      const result =
        AssessmentProcess.reverseTestTakerAnswerToAllString(initialData);
      // Result should still be string[]
      chai.expect(result).to.deep.equal(['Option A', 'Option B']);
      chai.expect(result).to.equal(initialData.testTakerAnswers); // Same array instance
    });
  });

  // --- Test more complex methods (Updated Mocks/Assertions) ---

  describe('markTest', () => {
    let mockMarkQuestionsData: Question[];
    let mockAssignedQuestionsData: Question[];
    let mockSubmitedQuestionsData: QuestionResult[]; // Contains TestTakerAnswers[]
    let markStub: SinonStub;

    beforeEach(() => {
      mockMarkQuestionsData = [
        createMockQuestion('q1', 'Multiple_choice', 10),
        createMockQuestion('q2', 'Fill_in', 5),
        createMockQuestion('q3', 'Matrix', 15),
      ];
      mockAssignedQuestionsData = [...mockMarkQuestionsData]; // Assume same assigned
      mockSubmitedQuestionsData = [
        {
          questionId: 'q1',
          questionType: 'Multiple_choice',
          testTakerAnswers: ['Correct Option A'],
          idleTime: 5,
        }, // string[]
        {
          questionId: 'q2',
          questionType: 'Fill_in',
          testTakerAnswers: ['Wrong Answer'],
          idleTime: 10,
        }, // string[]
        {
          questionId: 'q3',
          questionType: 'Matrix',
          idleTime: 15,
          testTakerAnswers: [
            // Array of MatrixSubquestionSubmitData
            { subquestionId: 'sq1', answers: ['OptA'] },
            { subquestionId: 'sq2', answers: ['OptC'] }, // Assume OptC is wrong for sq2
          ],
        },
      ];

      markStub = sandbox.stub(MarkQuestion, 'mark');
    });

    // it('should call MarkQuestion.mark with correct args and aggregate results', async () => {
    //   // Configure stub return values based on MarkQuestionResult type
    //   markStub
    //     .withArgs(
    //       mockMarkQuestionsData[0],
    //       mockSubmitedQuestionsData[0].testTakerAnswers
    //     )
    //     .resolves(
    //       createMockMarkQuestionResult(true, true, 10, ['Correct Option A'])
    //     );
    //   markStub
    //     .withArgs(
    //       mockMarkQuestionsData[1],
    //       mockSubmitedQuestionsData[1].testTakerAnswers
    //     )
    //     .resolves(
    //       createMockMarkQuestionResult(false, true, 0, ['Wrong Answer'])
    //     );
    //   const matrixResultAnswers: TestTakerAnswers[] = [
    //     // Example: These might be ModifiedMatrixAnswer objects stringified if mark returns that, or just the submit data? Check MarkQuestion.mark return type carefully. Let's assume it returns the input format for now.
    //     { subquestionId: 'sq1', answers: ['OptA'] },
    //     { subquestionId: 'sq2', answers: ['OptC'] },
    //   ];
    //   markStub
    //     .withArgs(
    //       mockMarkQuestionsData[2],
    //       mockSubmitedQuestionsData[2].testTakerAnswers
    //     )
    //     .resolves(
    //       createMockMarkQuestionResult(true, true, 7.5, matrixResultAnswers)
    //     ); // Partially correct matrix

    //   const result = await AssessmentProcess.markTest({
    //     markQuestionsData: mockMarkQuestionsData,
    //     assignedQuestionsData: mockAssignedQuestionsData,
    //     submitedQuestionsData: mockSubmitedQuestionsData,
    //   });

    //   chai.expect(markStub).to.have.been.calledThrice;
    //   chai
    //     .expect(markStub.firstCall)
    //     .to.have.been.calledWith(mockMarkQuestionsData[0], [
    //       'Correct Option A',
    //     ]);
    //   chai
    //     .expect(markStub.secondCall)
    //     .to.have.been.calledWith(mockMarkQuestionsData[1], ['Wrong Answer']);
    //   chai
    //     .expect(markStub.thirdCall)
    //     .to.have.been.calledWith(
    //       mockMarkQuestionsData[2],
    //       mockSubmitedQuestionsData[2].testTakerAnswers
    //     );

    //   chai.expect(result.totalScore).to.equal(30); // 10 + 5 + 15
    //   chai.expect(result.totalPassedScore).to.equal(17.5); // 10 + 0 + 7.5
    //   chai.expect(result.numberOfQuestionsPassed).to.equal(2); // q1 and q3 were marked isAnswerCorrect=true
    //   chai.expect(result.numberOfQuestionsFailed).to.equal(1); // q2 failed (3 submitted - 2 passed)
    //   chai.expect(result.numberOfQuestionsAnswered).to.equal(3); // All marked isAnswered=true
    //   chai.expect(result.testResults).to.be.an('array').with.lengthOf(3);

    //   // Check structure of results includes fields from MarkQuestionResult
    //   chai.expect(result.testResults[0]).to.deep.include({
    //     questionId: 'q1',
    //     score: 10,
    //     isAnswerCorrect: true,
    //     scored: 10,
    //     isAnswered: true,
    //   });
    //   chai.expect(result.testResults[1]).to.deep.include({
    //     questionId: 'q2',
    //     score: 5,
    //     isAnswerCorrect: false,
    //     scored: 0,
    //     isAnswered: true,
    //   });
    //   chai.expect(result.testResults[2]).to.deep.include({
    //     questionId: 'q3',
    //     score: 15,
    //     isAnswerCorrect: true,
    //     scored: 7.5,
    //     isAnswered: true,
    //   });
    //   // Check if testTakerAnswers from MarkQuestionResult are included (adjust based on actual MarkQuestion.mark behavior)
    //   // expect(result.testResults[2].testTakerAnswers).to.deep.equal(matrixResultAnswers);
    // });

    it('should throw AppError if submitted question is not in markQuestionsData', async () => {
      mockSubmitedQuestionsData.push({
        questionId: 'q-invalid',
        questionType: 'Multiple_choice',
        testTakerAnswers: [],
        idleTime: 0,
      });

      markStub.resolves(createMockMarkQuestionResult(false, false, 0)); // Default resolve for valid ones

      await chai
        .expect(
          AssessmentProcess.markTest({
            markQuestionsData: mockMarkQuestionsData, // q1, q2, q3
            assignedQuestionsData: mockAssignedQuestionsData,
            submitedQuestionsData: mockSubmitedQuestionsData, // includes q-invalid
          })
        )
        .to.be.rejectedWith(AppError, /Question 4 is not found in this test/); // Adjust index based on loop order
    });

    it('should throw AppError if submitted question was not assigned', async () => {
      mockAssignedQuestionsData = [mockMarkQuestionsData[0]]; // Only q1 assigned
      // mockSubmitedQuestionsData submits q1, q2, q3

      markStub
        .withArgs(
          mockMarkQuestionsData[0],
          mockSubmitedQuestionsData[0].testTakerAnswers
        )
        .resolves(createMockMarkQuestionResult(true, true, 10)); // q1 is fine

      await chai
        .expect(
          AssessmentProcess.markTest({
            markQuestionsData: mockMarkQuestionsData,
            assignedQuestionsData: mockAssignedQuestionsData, // Only q1
            submitedQuestionsData: mockSubmitedQuestionsData, // q1, q2, q3 submitted
          })
        )
        .to.be.rejectedWith(TypeError); // Fails on q2 check
    });

    it('should handle empty submitted questions correctly', async () => {
      mockSubmitedQuestionsData = []; // No questions submitted

      const result = await AssessmentProcess.markTest({
        markQuestionsData: mockMarkQuestionsData,
        assignedQuestionsData: mockAssignedQuestionsData,
        submitedQuestionsData: mockSubmitedQuestionsData,
      });

      chai.expect(markStub).to.not.have.been.called;
      chai.expect(result.totalScore).to.equal(0);
      chai.expect(result.totalPassedScore).to.equal(0);
      // ... rest of zero checks
      chai.expect(result.testResults).to.be.an('array').with.lengthOf(0);
    });
  });
});
