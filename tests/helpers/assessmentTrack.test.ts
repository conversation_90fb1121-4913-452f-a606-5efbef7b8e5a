import { expect } from 'chai';
import sinon from 'sinon';
import proxyquire from 'proxyquire';
import { AppError } from '../../src/middlewares/errorHandler';
import {
  assessmentTakerDependencyMockData,
  fakeAssessmentTaker,
  fakeTestResult,
} from '../fakeData/mockresponse';
import { createPrismaStub } from '../fakeData/mockPayload';
import { AssessmentLinkStatus } from '@prisma/client';

describe('AssessmentTrackStatus', () => {
  let sandbox;
  let AssessmentTrackStatus;
  let stubs;

  beforeEach(() => {
    sandbox = sinon.createSandbox();
    stubs = {
      fetchCandidateDetails: sandbox
        .stub()
        .resolves(assessmentTakerDependencyMockData),
      viewAssessmentLinkPublish: sandbox.stub().resolves(),
      setCacheData: sandbox.stub().resolves(),
      getAssessmentTakerDependencyData: sandbox
        .stub()
        .resolves(assessmentTakerDependencyMockData),
      autoSendAssessmentReportSubmission: sandbox.stub().resolves(),
      prisma: {
        assessmentTaker: {
          create: sandbox.stub().resolves(fakeAssessmentTaker),
          update: sandbox.stub().resolves(),
        },
        testResult: {
          findMany: sandbox.stub().resolves(fakeTestResult),
        },
      },
    };

    AssessmentTrackStatus = proxyquire('../../src/helpers/assessmentTrack', {
      '../helpers/fetchCandidateDetails': {
        fetchCandidateDetails: stubs.fetchCandidateDetails,
      },
      '../helpers/kafkaPublishMessage': {
        viewAssessmentLinkPublish: stubs.viewAssessmentLinkPublish,
      },
      './redisCache': {
        setCacheData: stubs.setCacheData,
        getAssessmentTakerDependencyData:
          stubs.getAssessmentTakerDependencyData,
      },
      './assessmentReport': {
        autoSendAssessmentReportSubmission:
          stubs.autoSendAssessmentReportSubmission,
      },
      '../prisma': stubs.prisma,
    }).AssessmentTrackStatus;
  });

  afterEach(() => {
    sandbox.restore();
  });

  describe('registerNewCandidate', () => {
    it('throws error when assessment has expired', async () => {
      const originalExpireDate =
        assessmentTakerDependencyMockData.assessmentTaker.expireDate;
      assessmentTakerDependencyMockData.assessmentTaker.expireDate = new Date(
        Date.now() - 100000
      );

      try {
        await AssessmentTrackStatus.registerNewCandidate(fakeAssessmentTaker);
        expect.fail('Expected an error to be thrown');
      } catch (err) {
        expect(err).to.be.instanceOf(AppError);
        expect(err.message).to.equal('expired');
      } finally {
        assessmentTakerDependencyMockData.assessmentTaker.expireDate =
          originalExpireDate;
      }
    });

    it('throws error when assessment is not due', async () => {
      const originalCommenceDate =
        assessmentTakerDependencyMockData.assessmentTaker.commenceDate;
      const futureDate = new Date(Date.now() + 5 * 24 * 60 * 60 * 1000);
      assessmentTakerDependencyMockData.assessmentTaker.commenceDate =
        futureDate;
      assessmentTakerDependencyMockData.assessmentTaker.expireDate = futureDate;

      try {
        await AssessmentTrackStatus.registerNewCandidate(
          fakeAssessmentTaker.id
        );
        expect.fail('Expected an error to be thrown');
      } catch (err) {
        expect(err).to.be.instanceOf(AppError);
        expect(err.message).to.equal('undue');
      } finally {
        assessmentTakerDependencyMockData.assessmentTaker.commenceDate =
          originalCommenceDate;
        assessmentTakerDependencyMockData.assessmentTaker.expireDate;
      }
    });

    it('registers a new candidate successfully', async () => {
      const breakdownSpy = sandbox.spy(
        AssessmentTrackStatus,
        'assessmentDataBreakdown'
      );

      await AssessmentTrackStatus.registerNewCandidate(fakeAssessmentTaker.id)
        .done;

      expect(stubs.fetchCandidateDetails.calledWith(fakeAssessmentTaker.id)).to
        .be.true;
      expect(breakdownSpy.called).to.be.true;
    });
  });

  describe('getCandidateCurrentPhase', () => {
    it('throws error when link status is invalid', async () => {
      try {
        await AssessmentTrackStatus.getCandidateCurrentPhase({
          ...fakeAssessmentTaker,
          linkStatus: AssessmentLinkStatus.INVALID,
        });
        expect.fail('Expected an error to be thrown');
      } catch (err) {
        expect(err).to.be.instanceOf(AppError);
        expect(err.message).to.equal('invalid');
      }
    });

    it('throws error when status is completed', async () => {
      const testTaker = {
        ...fakeAssessmentTaker,
        status: 'COMPLETED',
        phase: 'COMPLETED',
        linkStatus: 'VALID',
      };

      const prismaStub = createPrismaStub();
      prismaStub.assessmentTaker.update.resolves({
        ...testTaker,
        linkStatus: 'INVALID',
      });

      try {
        await AssessmentTrackStatus.getCandidateCurrentPhase(
          testTaker,
          prismaStub
        );
        expect.fail('Expected an error to be thrown');
      } catch (error) {
        expect(error).to.be.instanceOf(AppError);
        expect(error.httpCode).to.equal(400);
        expect(error.message).to.include('completed');
      }
    });

    // it('throws error when estimated end time has passed', async function () {
    //   this.timeout(10000);

    //   const pastDate = new Date(Date.now() - 24 * 60 * 60 * 1000);
    //   const testTaker = {
    //     ...fakeAssessmentTaker,
    //     status: 'IN_PROGRESS',
    //     phase: 'CODE_CONDUCT_SIGNING',
    //     estimatedEndTime: pastDate,
    //     linkStatus: 'VALID',
    //   };

    //   const prismaStub = {
    //     assessmentTaker: {
    //       update: sinon.stub().resolves({
    //         ...testTaker,
    //         linkStatus: AssessmentLinkStatus.INVALID,
    //       }),
    //     },
    //   };

    //   try {
    //     await AssessmentTrackStatus.getCandidateCurrentPhase(
    //       testTaker,
    //       prismaStub
    //     );
    //     expect.fail('Expected an error to be thrown');
    //   } catch (error) {
    //     expect(error).to.be.instanceOf(AppError);
    //     expect(error.message).to.equal('invalid');
    //   }
    // });

    it('throws error when assessment has expired', async () => {
      const pastDate = new Date(Date.now() - 5 * 24 * 60 * 60 * 1000);
      const testTaker = {
        ...fakeAssessmentTaker,
        linkStatus: 'VALID',
        status: 'IN_PROGRESS',
        estimatedEndTime: null,
        expireDate: pastDate,
      };

      const prismaStub = createPrismaStub();
      prismaStub.assessmentTaker.update.resolves({
        ...testTaker,
        linkStatus: 'INVALID',
      });

      try {
        await AssessmentTrackStatus.getCandidateCurrentPhase(
          testTaker,
          prismaStub
        );
        expect.fail('Expected an error to be thrown');
      } catch (error) {
        expect(error).to.be.instanceOf(AppError);
        expect(error.httpCode).to.equal(400);
        expect(error.message).to.equal('expired');
      }
    });

    it('returns data for candidate not in progress', async () => {
      const futureDate = new Date(Date.now() + 2 * 60 * 60 * 1000);

      const result = await AssessmentTrackStatus.getCandidateCurrentPhase({
        ...fakeAssessmentTaker,
        linkStatus: 'VALID',
        status: 'IN_PROGRESS',
        estimatedEndTime: futureDate,
        expireDate: futureDate,
        phase: 'CODE_CONDUCT_SIGNING',
      });

      expect(stubs.getAssessmentTakerDependencyData.called).to.be.true;
      expect(result).to.not.be.undefined; // Add an assertion to check the result
    });
  });
});
