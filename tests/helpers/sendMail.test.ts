import { expect } from 'chai';
import sinon from 'sinon';
import proxyquire from 'proxyquire';
import { AppError } from '../../src/middlewares/errorHandler';

describe('Email Service', () => {
  let sendMail: any;
  let nodemailerStub: any;
  let createTransportStub: any;
  let transporterMock: any;

  beforeEach(() => {
    // Mock the transporter and its methods
    transporterMock = {
      sendMail: sinon.stub().resolves({
        rejected: [],
        response: '250 OK',
      }),
    };

    // Mock nodemailer.createTransport
    createTransportStub = sinon.stub().returns(transporterMock);

    // Mock the entire nodemailer module
    nodemailerStub = {
      createTransport: createTransportStub,
    };

    // Use proxyquire to mock the nodemailer dependency
    sendMail = proxyquire('../../src/helpers/sendmail', {
      nodemailer: nodemailerStub,
    }).sendMail;
  });

  afterEach(() => {
    sinon.restore();
  });

  describe('sendMail', () => {
    it('should create transporter with correct configuration', async () => {
      process.env.EMAIL_HOST = 'smtp.example.com';
      process.env.EMAIL_PORT = '587';
      process.env.EMAIL_HOST_USER = '<EMAIL>';
      process.env.EMAIL_HOST_PASSWORD = 'password123';

      await sendMail(['<EMAIL>'], 'Test Subject', '<p>Test HTML</p>');

      expect(createTransportStub.calledOnce).to.be.true;
      expect(createTransportStub.firstCall.args[0]).to.deep.equal({
        host: 'smtp.example.com',
        port: 587,
        auth: {
          user: '<EMAIL>',
          pass: 'password123',
        },
      });
    });

    it('should send email with correct options to single recipient', async () => {
      await sendMail(['<EMAIL>'], 'Test Subject', '<p>Test HTML</p>');

      expect(transporterMock.sendMail.calledOnce).to.be.true;
      const mailOptions = transporterMock.sendMail.firstCall.args[0];
      expect(mailOptions).to.deep.equal({
        from: '"Dodokpo"<<EMAIL>>',
        to: '<EMAIL>',
        subject: 'Test Subject',
        html: '<p>Test HTML</p>',
        attachments: undefined,
      });
    });

    it('should send email to multiple recipients', async () => {
      await sendMail(
        ['<EMAIL>', '<EMAIL>'],
        'Test Subject',
        '<p>Test HTML</p>'
      );

      expect(transporterMock.sendMail.callCount).to.equal(2);
      const firstCallOptions = transporterMock.sendMail.firstCall.args[0];
      const secondCallOptions = transporterMock.sendMail.secondCall.args[0];
      expect(firstCallOptions.to).to.equal('<EMAIL>');
      expect(secondCallOptions.to).to.equal('<EMAIL>');
    });

    it('should include attachments when provided', async () => {
      const attachments = [
        { filename: 'file1.txt', path: '/path/to/file1.txt' },
        { filename: 'file2.pdf', path: '/path/to/file2.pdf' },
      ];

      await sendMail(
        ['<EMAIL>'],
        'Test Subject',
        '<p>Test HTML</p>',
        attachments
      );

      const mailOptions = transporterMock.sendMail.firstCall.args[0];
      expect(mailOptions.attachments).to.deep.equal(attachments);
    });

    it('should throw AppError when email is rejected', async () => {
      transporterMock.sendMail.resolves({
        rejected: ['<EMAIL>'],
        response: '550 Rejected',
      });

      try {
        await sendMail(
          ['<EMAIL>'],
          'Test Subject',
          '<p>Test HTML</p>'
        );
        expect.fail('Should have thrown an error');
      } catch (error) {
        expect(error).to.be.instanceOf(AppError);
        expect((error as AppError).httpCode).to.equal(500);
        expect((error as AppError).message).to.equal(
          'Error sending <NAME_EMAIL>'
        );
      }
    });

    it('should handle SMTP connection errors', async () => {
      const connectionError = new Error('SMTP connection failed');
      transporterMock.sendMail.rejects(connectionError);

      try {
        await sendMail(
          ['<EMAIL>'],
          'Test Subject',
          '<p>Test HTML</p>'
        );
        expect.fail('Should have thrown an error');
      } catch (error) {
        expect(error).to.equal(connectionError);
      }
    });

    it('should use the correct sender email from env', async () => {
      process.env.EMAIL_HOST_USER = '<EMAIL>';

      await sendMail(['<EMAIL>'], 'Test Subject', '<p>Test HTML</p>');

      const mailOptions = transporterMock.sendMail.firstCall.args[0];
      expect(mailOptions.from).to.equal('"Dodokpo"<<EMAIL>>');
    });

    it('should handle empty recipient list gracefully', async () => {
      await sendMail([], 'Test Subject', '<p>Test HTML</p>');
      expect(transporterMock.sendMail.callCount).to.equal(0);
    });

    it('should handle invalid port number gracefully', async () => {
      process.env.EMAIL_PORT = 'invalid';

      try {
        await sendMail(
          ['<EMAIL>'],
          'Test Subject',
          '<p>Test HTML</p>'
        );
        expect.fail('Should have thrown an error');
      } catch (error) {
        expect(error).to.be.instanceOf(Error);
      }
    });
  });
});
