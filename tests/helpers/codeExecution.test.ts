import { expect } from 'chai';
import sinon from 'sinon';
import proxyquire from 'proxyquire';
import { AppError, HttpCode } from '../../src/middlewares/errorHandler';

describe('Code Execution Helper', () => {
  let sandbox: sinon.SinonSandbox;
  let axiosStub: any;
  let loggerStub: any;
  let codeExecutionHelper: any;

  beforeEach(() => {
    sandbox = sinon.createSandbox();

    // Create stubs
    axiosStub = {
      post: sandbox.stub(),
      get: sandbox.stub(),
    };

    loggerStub = {
      error: sandbox.stub(),
      info: sandbox.stub(),
      warn: sandbox.stub(),
    };

    // Load helper with mocked dependencies
    codeExecutionHelper = proxyquire('../../src/helpers/codeExecution', {
      axios: axiosStub,
      './logger': { default: loggerStub },
    });

    // Set environment variables
    process.env.JUDGE0_API_URL = 'http://localhost:2358';
    process.env.JUDGE0_API_KEY = 'test-api-key';
  });

  afterEach(() => {
    sandbox.restore();
    delete process.env.JUDGE0_API_URL;
    delete process.env.JUDGE0_API_KEY;
  });

  describe('generateRunnerCode', () => {
    it('Should generate JavaScript runner code correctly', () => {
      const languageId = 63;
      const userCode = 'function add(a, b) { return a + b; }';
      const input = '2,3';

      const result = codeExecutionHelper.generateRunnerCode(
        languageId,
        userCode,
        input
      );

      expect(result).to.include('const result = add(2,3);');
      expect(result).to.include('console.log');
    });

    it('Should generate TypeScript runner code correctly', () => {
      const languageId = 74;
      const userCode =
        'function add(a: number, b: number): number { return a + b; }';
      const input = '2,3';

      const result = codeExecutionHelper.generateRunnerCode(
        languageId,
        userCode,
        input
      );

      expect(result).to.include('const result = add(2,3);');
      expect(result).to.include('console.log');
    });

    it('Should generate JavaScript runner code with class method correctly', () => {
      const languageId = 63;
      const userCode = 'class Solution { add(a, b) { return a + b; } }';
      const input = '2,3';

      const result = codeExecutionHelper.generateRunnerCode(
        languageId,
        userCode,
        input
      );

      expect(result).to.include('const result = new Solution().add(2,3);');
      expect(result).to.include('console.log');
    });

    it('Should generate JavaScript runner code with arrow function correctly', () => {
      const languageId = 63;
      const userCode = 'const add = (a, b) => a + b;';
      const input = '2,3';

      const result = codeExecutionHelper.generateRunnerCode(
        languageId,
        userCode,
        input
      );

      expect(result).to.include('const result = add(2,3);');
      expect(result).to.include('console.log');
    });

    it('Should handle unsupported language', () => {
      const languageId = 999; // Unsupported
      const userCode = 'some code';
      const input = 'some input';

      try {
        codeExecutionHelper.generateRunnerCode(languageId, userCode, input);
        expect.fail('Should have thrown an error');
      } catch (error) {
        expect(error).to.be.instanceOf(AppError);
        expect(error.httpCode).to.equal(HttpCode.BAD_REQUEST);
        expect(error.message).to.include(
          'Unsupported language_id: 999. Supported: 63, 74'
        );
      }
    });

    it('Should handle empty user code', () => {
      const languageId = 63;
      const userCode = '';
      const input = 'test';

      try {
        codeExecutionHelper.generateRunnerCode(languageId, userCode, input);
        expect.fail('Should have thrown an error');
      } catch (error) {
        expect(error).to.be.instanceOf(AppError);
        expect(error.httpCode).to.equal(HttpCode.BAD_REQUEST);
        expect(error.message).to.equal('Code must be a non-empty string');
      }
    });
  });

  describe('createSourceCode', () => {
    it('Should create source code by combining user code and runner', () => {
      const userCode = 'function add(a, b) { return a + b; }';
      const runnerCode = 'console.log(add(2, 3));';
      const languageId = 63;

      const result = codeExecutionHelper.createSourceCode(
        userCode,
        runnerCode,
        languageId
      );

      expect(result).to.include(userCode);
      expect(result).to.include(runnerCode);
    });

    it('Should create source code correctly for JavaScript', () => {
      const code = 'function add(a, b) { return a + b; }';
      const runnerCode = 'console.log(add(2, 3));';
      const languageId = 63;

      const result = codeExecutionHelper.createSourceCode(
        code,
        runnerCode,
        languageId
      );

      expect(result).to.include(code);
      expect(result).to.include(runnerCode);
      expect(result).to.be.a('string');
    });

    it('Should create source code correctly for TypeScript', () => {
      const code =
        'function add(a: number, b: number): number { return a + b; }';
      const runnerCode = 'console.log(add(2, 3));';
      const languageId = 74;

      const result = codeExecutionHelper.createSourceCode(
        code,
        runnerCode,
        languageId
      );

      expect(result).to.include(code);
      expect(result).to.include(runnerCode);
      expect(result).to.include('/// <reference lib="es2015" />');
      expect(result).to.be.a('string');
    });
  });

  describe('executeCode', () => {
    it('Should successfully execute code and return output', async () => {
      const sourceCode = 'console.log("Hello World");';
      const languageId = 63;
      const input = '';

      // Mock direct response since wait=true is used
      const mockResponse = {
        data: {
          status: { id: 3, description: 'Accepted' },
          stdout: 'Hello World\n',
          stderr: null,
          compile_output: null,
          time: '0.001',
          memory: 1024,
        },
      };

      axiosStub.post.resolves(mockResponse);

      const result = await codeExecutionHelper.executeCode(
        sourceCode,
        languageId,
        input
      );

      expect(axiosStub.post.calledOnce).to.be.true;
      expect(result).to.equal('Hello World');
    });

    it('Should handle compilation errors', async () => {
      const sourceCode = 'invalid syntax code';
      const languageId = 63;
      const input = '';

      const mockResponse = {
        data: {
          status: { id: 6, description: 'Compilation Error' },
          stdout: null,
          stderr: null,
          compile_output: 'SyntaxError: Unexpected token',
          time: null,
          memory: null,
        },
      };

      axiosStub.post.resolves(mockResponse);

      try {
        await codeExecutionHelper.executeCode(sourceCode, languageId, input);
        expect.fail('Should have thrown an error');
      } catch (error) {
        expect(error).to.be.instanceOf(AppError);
        expect(error.httpCode).to.equal(HttpCode.BAD_REQUEST);
        expect(error.message).to.include('Compilation Error');
      }
    });

    it('Should handle runtime errors', async () => {
      const sourceCode = 'console.log(undefinedVariable);';
      const languageId = 63;
      const input = '';

      const mockResponse = {
        data: {
          status: { id: 5, description: 'Time Limit Exceeded' },
          stdout: null,
          stderr: 'ReferenceError: undefinedVariable is not defined',
          compile_output: null,
          time: '1.000',
          memory: 2048,
        },
      };

      axiosStub.post.resolves(mockResponse);

      try {
        await codeExecutionHelper.executeCode(sourceCode, languageId, input);
        expect.fail('Should have thrown an error');
      } catch (error) {
        expect(error).to.be.instanceOf(AppError);
        expect(error.httpCode).to.equal(HttpCode.BAD_REQUEST);
        expect(error.message).to.include('Runtime Error');
      }
    });

    it('Should handle Judge0 API errors', async () => {
      const sourceCode = 'console.log("test");';
      const languageId = 63;
      const input = '';

      const apiError = new Error('Judge0 API unavailable');
      axiosStub.post.rejects(apiError);

      try {
        await codeExecutionHelper.executeCode(sourceCode, languageId, input);
        expect.fail('Should have thrown an error');
      } catch (error) {
        expect(error).to.be.instanceOf(AppError);
        expect(error.httpCode).to.equal(HttpCode.INTERNAL_SERVER_ERROR);
      }
    });

    it('Should handle execution timeout', async () => {
      const sourceCode = 'while(true) {}'; // Infinite loop
      const languageId = 63;
      const input = '';

      const mockSubmissionResponse = {
        data: { token: 'test-token-123' },
      };

      // Simulate multiple polling attempts
      axiosStub.post.resolves(mockSubmissionResponse);
      axiosStub.get.onFirstCall().resolves({
        data: { status: { id: 1, description: 'In Queue' } },
      });
      axiosStub.get.onSecondCall().resolves({
        data: { status: { id: 2, description: 'Processing' } },
      });
      axiosStub.get.resolves({
        data: {
          status: { id: 5, description: 'Time Limit Exceeded' },
          stdout: null,
          stderr: null,
          compile_output: null,
          time: '2.000',
          memory: 4096,
        },
      });

      try {
        await codeExecutionHelper.executeCode(sourceCode, languageId, input);
        expect.fail('Should have thrown an error');
      } catch (error) {
        expect(error).to.be.instanceOf(AppError);
        expect(error.httpCode).to.equal(HttpCode.BAD_REQUEST);
      }
    });
  });

  describe('prepareAndProcessTestCases', () => {
    const mockTestCases = [
      {
        test_case_id: '1',
        input: '2,3',
        output: '5',
        hidden: false,
      },
      {
        test_case_id: '2',
        input: '10,15',
        output: '25',
        hidden: true,
      },
    ];

    it.skip('Should process multiple test cases successfully', async () => {
      const assessmentTakerId = 'taker-123';
      const questionId = 'question-456';
      const code = 'function add(a, b) { return a + b; }';
      const languageId = 63;
      const codeConstraints = { timeLimit: 2, memoryLimit: 128 };

      // Mock successful executions
      const mockSubmissionResponse = { data: { token: 'test-token' } };
      const mockResultResponse = {
        data: {
          status: { id: 3, description: 'Accepted' },
          stdout: '5\n',
          stderr: null,
          time: '0.001',
          memory: 1024,
        },
      };

      axiosStub.post.resolves(mockSubmissionResponse);
      axiosStub.get.resolves(mockResultResponse);

      const result = await codeExecutionHelper.prepareAndProcessTestCases(
        assessmentTakerId,
        questionId,
        code,
        languageId,
        mockTestCases,
        codeConstraints
      );

      expect(result.results).to.have.length(2);
      expect(result.summary.totalTestCases).to.equal(2);
      expect(result.summary.passedTestCases).to.equal(2);
      expect(result.summary.failedTestCases).to.equal(0);
    });

    it.skip('Should handle mixed test case results', async () => {
      const assessmentTakerId = 'taker-123';
      const questionId = 'question-456';
      const code = 'function add(a, b) { return a + b; }';
      const languageId = 63;

      // Mock first test case success, second failure
      const mockSubmissionResponse = { data: { token: 'test-token' } };

      axiosStub.post.resolves(mockSubmissionResponse);
      axiosStub.get.onFirstCall().resolves({
        data: {
          status: { id: 3, description: 'Accepted' },
          stdout: '5\n',
          stderr: null,
          time: '0.001',
          memory: 1024,
        },
      });
      axiosStub.get.onSecondCall().resolves({
        data: {
          status: { id: 3, description: 'Accepted' },
          stdout: '20\n', // Wrong output
          stderr: null,
          time: '0.001',
          memory: 1024,
        },
      });

      const result = await codeExecutionHelper.prepareAndProcessTestCases(
        assessmentTakerId,
        questionId,
        code,
        languageId,
        mockTestCases,
        {}
      );

      expect(result.summary.passedTestCases).to.equal(1);
      expect(result.summary.failedTestCases).to.equal(1);
      expect(result.results[0].passed).to.be.true;
      expect(result.results[1].passed).to.be.false;
    });

    it('Should handle code constraints validation', async () => {
      const assessmentTakerId = 'taker-123';
      const questionId = 'question-456';
      const languageId = 63;
      const codeConstraints = {
        timeLimit: 1,
        memoryLimit: 64,
        forbiddenKeywords: ['eval', 'setTimeout'],
      };

      try {
        await codeExecutionHelper.prepareAndProcessTestCases(
          assessmentTakerId,
          questionId,
          'eval("malicious code")',
          languageId,
          mockTestCases,
          codeConstraints
        );
        expect.fail('Should have thrown an error');
      } catch (error) {
        expect(error).to.be.instanceOf(AppError);
      }
    });
  });
});
