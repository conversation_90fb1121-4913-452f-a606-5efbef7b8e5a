import { expect } from 'chai';
import Sinon from 'sinon';
import { AssessmentProcess } from '../../src/helpers/assessmentProcess';
import { MarkQuestion } from '../../src/helpers/markQuestion';
import { Question } from '../../src/dtos/assessmentTakerDtos';
import { AnswerStatus, QuestionResult } from '../../src/helpers/types';

describe('Question Count Validation Tests', () => {
  let markStub: Sinon.SinonStub;

  beforeEach(() => {
    markStub = Sinon.stub(MarkQuestion, 'mark');
  });

  afterEach(() => {
    Sinon.restore();
  });

  const createMockQuestion = (id: string, score: number): Question =>
    ({
      id,
      score,
      questionType: 'Multiple_choice',
      questionText: `Question ${id}`,
      multipleChoiceAnswer: { answer: ['Option A'] },
    } as Question);

  const createMockSubmittedQuestion = (
    questionId: string,
    answers: string[] = ['Answer']
  ): QuestionResult => ({
    questionId,
    questionType: 'Multiple_choice',
    testTakerAnswers: answers,
    idleTime: 0,
  });

  describe('Total Count Validation', () => {
    it('Should ensure passed + failed + skipped = total questions in mixed scenario', async () => {
      const assignedQuestions = [
        createMockQuestion('q1', 10), // Will be correct
        createMockQuestion('q2', 5), // Will be wrong
        createMockQuestion('q3', 15), // Will be skipped (not submitted) - won't be processed
        createMockQuestion('q4', 8), // Will be skipped (empty answer)
        createMockQuestion('q5', 12), // Will be correct
      ];

      const submittedQuestions = [
        createMockSubmittedQuestion('q1', ['Correct']),
        createMockSubmittedQuestion('q2', ['Wrong']),
        // q3 not submitted - won't be processed by markTest
        createMockSubmittedQuestion('q4', []), // Empty answer
        createMockSubmittedQuestion('q5', ['Correct']),
      ];

      // Mock responses
      markStub
        .withArgs(Sinon.match.any, 'q1', Sinon.match.any, ['Correct'])
        .resolves({
          answerStatus: AnswerStatus.CORRECT,
          scored: 10,
          isAnswered: true,
          testTakerAnswers: ['Correct'],
        });

      markStub
        .withArgs(Sinon.match.any, 'q2', Sinon.match.any, ['Wrong'])
        .resolves({
          answerStatus: AnswerStatus.WRONG,
          scored: 0,
          isAnswered: true,
          testTakerAnswers: ['Wrong'],
        });

      markStub.withArgs(Sinon.match.any, 'q4', Sinon.match.any, []).resolves({
        answerStatus: AnswerStatus.SKIPPED,
        scored: 0,
        isAnswered: false,
        testTakerAnswers: [],
      });

      markStub
        .withArgs(Sinon.match.any, 'q5', Sinon.match.any, ['Correct'])
        .resolves({
          answerStatus: AnswerStatus.CORRECT,
          scored: 12,
          isAnswered: true,
          testTakerAnswers: ['Correct'],
        });

      const result = await AssessmentProcess.markTest({
        assessmentTakerId: 'test-taker-1',
        assignedQuestionsData: assignedQuestions,
        markQuestionsData: assignedQuestions,
        submitedQuestionsData: submittedQuestions,
      });

      // Validate individual counts - only submitted questions are processed
      expect(result.numberOfQuestionsPassed).to.equal(2); // q1, q5
      expect(result.numberOfQuestionsFailed).to.equal(1); // q2
      expect(result.numberOfQuestionsSkipped).to.equal(1); // q4 (submitted but empty)
      expect(result.numberOfQuestionsAnswered).to.equal(3); // q1, q2, q5
      expect(result.totalQuestions).to.equal(4); // only submitted questions

      // Critical validation: totals must add up
      const totalCounted =
        result.numberOfQuestionsPassed +
        result.numberOfQuestionsFailed +
        result.numberOfQuestionsSkipped;
      expect(totalCounted).to.equal(
        result.totalQuestions,
        `Count mismatch: ${result.numberOfQuestionsPassed} passed + ${result.numberOfQuestionsFailed} failed + ${result.numberOfQuestionsSkipped} skipped = ${totalCounted}, but total questions = ${result.totalQuestions}`
      );
    });

    it('Should validate counts with all questions passed', async () => {
      const assignedQuestions = [
        createMockQuestion('q1', 10),
        createMockQuestion('q2', 5),
        createMockQuestion('q3', 15),
      ];

      const submittedQuestions = [
        createMockSubmittedQuestion('q1', ['Correct']),
        createMockSubmittedQuestion('q2', ['Correct']),
        createMockSubmittedQuestion('q3', ['Correct']),
      ];

      // All correct
      markStub.resolves({
        answerStatus: AnswerStatus.CORRECT,
        scored: 10,
        isAnswered: true,
        testTakerAnswers: ['Correct'],
      });

      const result = await AssessmentProcess.markTest({
        assessmentTakerId: 'test-taker-1',
        assignedQuestionsData: assignedQuestions,
        markQuestionsData: assignedQuestions,
        submitedQuestionsData: submittedQuestions,
      });

      expect(result.numberOfQuestionsPassed).to.equal(3);
      expect(result.numberOfQuestionsFailed).to.equal(0);
      expect(result.numberOfQuestionsSkipped).to.equal(0);
      expect(result.numberOfQuestionsAnswered).to.equal(3);

      const totalCounted =
        result.numberOfQuestionsPassed +
        result.numberOfQuestionsFailed +
        result.numberOfQuestionsSkipped;
      expect(totalCounted).to.equal(result.totalQuestions);
    });

    it('Should validate counts with all questions failed', async () => {
      const assignedQuestions = [
        createMockQuestion('q1', 10),
        createMockQuestion('q2', 5),
        createMockQuestion('q3', 15),
      ];

      const submittedQuestions = [
        createMockSubmittedQuestion('q1', ['Wrong']),
        createMockSubmittedQuestion('q2', ['Wrong']),
        createMockSubmittedQuestion('q3', ['Wrong']),
      ];

      // All wrong
      markStub.resolves({
        answerStatus: AnswerStatus.WRONG,
        scored: 0,
        isAnswered: true,
        testTakerAnswers: ['Wrong'],
      });

      const result = await AssessmentProcess.markTest({
        assessmentTakerId: 'test-taker-1',
        assignedQuestionsData: assignedQuestions,
        markQuestionsData: assignedQuestions,
        submitedQuestionsData: submittedQuestions,
      });

      expect(result.numberOfQuestionsPassed).to.equal(0);
      expect(result.numberOfQuestionsFailed).to.equal(3);
      expect(result.numberOfQuestionsSkipped).to.equal(0);
      expect(result.numberOfQuestionsAnswered).to.equal(3);

      const totalCounted =
        result.numberOfQuestionsPassed +
        result.numberOfQuestionsFailed +
        result.numberOfQuestionsSkipped;
      expect(totalCounted).to.equal(result.totalQuestions);
    });

    it('Should validate counts with all questions skipped', async () => {
      const assignedQuestions = [
        createMockQuestion('q1', 10),
        createMockQuestion('q2', 5),
        createMockQuestion('q3', 15),
      ];

      // No questions submitted
      const submittedQuestions = [];

      const result = await AssessmentProcess.markTest({
        assessmentTakerId: 'test-taker-1',
        assignedQuestionsData: assignedQuestions,
        markQuestionsData: assignedQuestions,
        submitedQuestionsData: submittedQuestions,
      });

      // When no questions are submitted, markTest processes an empty array
      expect(result.numberOfQuestionsPassed).to.equal(0);
      expect(result.numberOfQuestionsFailed).to.equal(0);
      expect(result.numberOfQuestionsSkipped).to.equal(0); // No submitted questions to be skipped
      expect(result.numberOfQuestionsAnswered).to.equal(0);
      expect(result.totalQuestions).to.equal(0); // No submitted questions

      const totalCounted =
        result.numberOfQuestionsPassed +
        result.numberOfQuestionsFailed +
        result.numberOfQuestionsSkipped;
      expect(totalCounted).to.equal(result.totalQuestions);
    });

    it('Should validate counts in large test scenario', async () => {
      // Create 20 questions with mixed outcomes
      const assignedQuestions = Array.from({ length: 20 }, (_, i) =>
        createMockQuestion(`q${i + 1}`, 5)
      );

      // Submit 15 questions, leave 5 unsubmitted (unsubmitted won't be processed)
      const submittedQuestions = Array.from({ length: 15 }, (_, i) =>
        createMockSubmittedQuestion(`q${i + 1}`, i % 3 === 0 ? [] : ['Answer'])
      );

      // Mock responses: every 3rd submitted question is skipped (empty), others alternate correct/wrong
      markStub.callsFake(
        (assessmentTakerId, questionId, dbQuestion, testTakerAnswers) => {
          const questionNum = parseInt(questionId.replace('q', ''));
          if (testTakerAnswers.length === 0) {
            return Promise.resolve({
              answerStatus: AnswerStatus.SKIPPED,
              scored: 0,
              isAnswered: false,
              testTakerAnswers,
            });
          }
          const isCorrect = questionNum % 2 === 1;
          return Promise.resolve({
            answerStatus: isCorrect ? AnswerStatus.CORRECT : AnswerStatus.WRONG,
            scored: isCorrect ? 5 : 0,
            isAnswered: true,
            testTakerAnswers,
          });
        }
      );

      const result = await AssessmentProcess.markTest({
        assessmentTakerId: 'test-taker-1',
        assignedQuestionsData: assignedQuestions,
        markQuestionsData: assignedQuestions,
        submitedQuestionsData: submittedQuestions,
      });

      // Now only submitted questions are counted
      expect(result.totalQuestions).to.equal(15); // Only submitted questions

      // Critical validation
      const totalCounted =
        result.numberOfQuestionsPassed +
        result.numberOfQuestionsFailed +
        result.numberOfQuestionsSkipped;
      expect(totalCounted).to.equal(
        result.totalQuestions,
        `Large test count mismatch: ${result.numberOfQuestionsPassed} + ${result.numberOfQuestionsFailed} + ${result.numberOfQuestionsSkipped} = ${totalCounted}, expected ${result.totalQuestions}`
      );

      // Additional validations
      expect(result.numberOfQuestionsPassed).to.be.at.least(0);
      expect(result.numberOfQuestionsFailed).to.be.at.least(0);
      expect(result.numberOfQuestionsSkipped).to.be.at.least(0); // Skipped questions from submitted ones
      expect(result.numberOfQuestionsAnswered).to.be.at.most(15); // At most 15 submitted
    });
  });
});
