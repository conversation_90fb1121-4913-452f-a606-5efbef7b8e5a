import Sinon from 'sinon';
import proxyquire from 'proxyquire';
import {
  assessmentTakerDependencyMockData,
  fakeAssessmentTaker,
} from '../fakeData/mockresponse';
import { expect } from 'chai';
import { AssessmentCache } from '../../src/dtos/assessmentTakerDtos';

describe('redisCache', () => {
  let sandbox: Sinon.SinonSandbox;
  let redisGetStub: Sinon.SinonStub;
  let redisSetStub: Sinon.SinonStub;
  let redisDelStub: Sinon.SinonStub;
  let redisKeysStub: Sinon.SinonStub;
  let fetchCandidateDetailsStub: Sinon.SinonStub;
  let loggerStub: Sinon.SinonStub;
  let redisCache;

  beforeEach(() => {
    sandbox = Sinon.createSandbox();

    // Create Redis method stubs
    redisGetStub = sandbox.stub();
    redisSetStub = sandbox.stub();
    redisDelStub = sandbox.stub();
    redisKeysStub = sandbox.stub();
    fetchCandidateDetailsStub = sandbox.stub();
    loggerStub = sandbox.stub();

    // Use proxyquire to replace the original module with our stubs
    redisCache = proxyquire('../../src/helpers/redisCache', {
      ioredis: {
        Redis: function () {
          return {
            get: redisGetStub,
            set: redisSetStub,
            del: redisDelStub,
            keys: redisKeysStub,
          };
        },
      },
      './fetchCandidateDetails': {
        fetchCandidateDetails: fetchCandidateDetailsStub,
      },
      './logger': {
        default: {
          warn: loggerStub,
        },
      },
    });
  });

  afterEach(() => {
    sandbox.restore();
  });

  describe('getCachedData', () => {
    it('Should return parsed data when cache exists', async () => {
      const testData = { test: 'data' };
      redisGetStub.resolves(JSON.stringify(testData));

      const result = await redisCache.getCachedData('test-key');

      expect(redisGetStub.calledWith('test-key')).to.be.true;
      expect(result).to.deep.equal(testData);
    });

    it('Should return false when cache does not exist', async () => {
      redisGetStub.resolves(null);

      const result = await redisCache.getCachedData('test-key');

      expect(redisGetStub.calledWith('test-key')).to.be.true;
      expect(result).to.be.false;
    });

    it('Should return false when Redis throws an error', async () => {
      redisGetStub.rejects(new Error('Redis connection error'));

      const result = await redisCache.getCachedData('test-key');

      expect(redisGetStub.calledWith('test-key')).to.be.true;
      expect(result).to.be.false;
    });

    it('Should return false when JSON parsing fails', async () => {
      redisGetStub.resolves('invalid json');

      const result = await redisCache.getCachedData('test-key');

      expect(redisGetStub.calledWith('test-key')).to.be.true;
      expect(result).to.be.false;
    });
  });

  describe('getAssessmentTakerDependencyData', () => {
    it('Should return candidate assessment data from memory', async () => {
      const cacheData: AssessmentCache = {
        reportCallbackURL:
          assessmentTakerDependencyMockData.assessmentTaker.reportCallbackURL,
        assessment:
          assessmentTakerDependencyMockData.assessmentTaker.assessment,
      };

      redisGetStub.resolves(JSON.stringify(cacheData));

      const response = await redisCache.getAssessmentTakerDependencyData(
        fakeAssessmentTaker
      );

      expect(response).to.deep.equal({
        assessmentTaker: {
          ...fakeAssessmentTaker,
          ...cacheData,
        },
      });

      expect(
        redisGetStub.calledWith(
          `assessmentTaker-${fakeAssessmentTaker.id}-client`
        )
      ).to.be.true;
    });

    it('Should return candidate assessment from service call when cache miss', async () => {
      redisGetStub.resolves(null);
      fetchCandidateDetailsStub.resolves(assessmentTakerDependencyMockData);

      const response = await redisCache.getAssessmentTakerDependencyData(
        fakeAssessmentTaker
      );

      expect(response).to.deep.equal({
        assessmentTaker: {
          ...fakeAssessmentTaker,
          reportCallbackURL:
            assessmentTakerDependencyMockData.assessmentTaker.reportCallbackURL,
          assessment:
            assessmentTakerDependencyMockData.assessmentTaker.assessment,
        },
      });

      expect(
        redisGetStub.calledWith(
          `assessmentTaker-${fakeAssessmentTaker.id}-client`
        )
      ).to.be.true;
      expect(fetchCandidateDetailsStub.calledWith(fakeAssessmentTaker.id)).to.be
        .true;
    });
  });

  describe('getAssessmentTakerDependencyDataMark', () => {
    it('Should return candidate assessment mark data from service call when cache miss', async () => {
      const testAssessmentTaker = {
        ...fakeAssessmentTaker,
        estimatedEndTime: new Date(Date.now() + 3600000), // 1 hour from now
      };

      redisGetStub.resolves(null);
      fetchCandidateDetailsStub.resolves(assessmentTakerDependencyMockData);
      redisSetStub.resolves('OK');

      const response = await redisCache.getAssessmentTakerDependencyDataMark(
        testAssessmentTaker
      );

      expect(response).to.deep.include({
        assessmentTaker: {
          ...testAssessmentTaker,
          reportCallbackURL:
            assessmentTakerDependencyMockData.assessmentTaker.reportCallbackURL,
          assessment:
            assessmentTakerDependencyMockData.assessmentTaker.assessment,
        },
      });

      expect(
        redisGetStub.calledWith(
          `assessment-${testAssessmentTaker.assessmentId}-mark`
        )
      ).to.be.true;

      expect(
        fetchCandidateDetailsStub.calledWithExactly(
          testAssessmentTaker.id,
          'marker'
        )
      ).to.be.true;

      expect(redisSetStub.called).to.be.true;
    });

    it('Should return candidate assessment mark data from memory when cached', async () => {
      const cachedData = {
        reportCallbackURL:
          assessmentTakerDependencyMockData.assessmentTaker.reportCallbackURL,
        assessment:
          assessmentTakerDependencyMockData.assessmentTaker.assessment,
      };

      redisGetStub.resolves(JSON.stringify(cachedData));

      const response = await redisCache.getAssessmentTakerDependencyDataMark(
        fakeAssessmentTaker
      );

      expect(response).to.deep.equal({
        assessmentTaker: {
          ...fakeAssessmentTaker,
          ...cachedData,
        },
      });

      expect(
        redisGetStub.calledWith(
          `assessment-${fakeAssessmentTaker.assessmentId}-mark`
        )
      ).to.be.true;

      expect(fetchCandidateDetailsStub.called).to.be.false;
    });
  });

  describe('setCacheData', () => {
    it('Should set cache and return true on success', async () => {
      redisSetStub.resolves('OK');

      const key = 'some-key';
      const data = { value: 'some-value' };
      const timeout = 100;

      const response = await redisCache.setCacheData(key, data, timeout);

      expect(redisSetStub.calledWith(key, JSON.stringify(data), 'EX', timeout))
        .to.be.true;
      expect(response).to.be.true;
    });

    it('Should return false on Redis error', async () => {
      redisSetStub.rejects(new Error('Redis connection error'));

      const key = 'some-key';
      const data = { value: 'some-value' };
      const timeout = 100;

      const response = await redisCache.setCacheData(key, data, timeout);

      expect(redisSetStub.calledWith(key, JSON.stringify(data), 'EX', timeout))
        .to.be.true;
      expect(response).to.be.false;
    });
  });

  describe('deleteCacheData', () => {
    it('Should delete cache and return true on success', async () => {
      redisKeysStub.resolves(['key-1', 'key-2']);
      redisDelStub.resolves(2);

      const key = 'some-key';
      const response = await redisCache.deleteCacheData(key);

      expect(redisKeysStub.calledWith(key)).to.be.true;
      expect(redisDelStub.calledWith(['key-1', 'key-2'])).to.be.true;
      expect(response).to.be.true;
    });

    it('Should return false on failure to delete cached data', async () => {
      redisKeysStub.resolves(['key-1', 'key-2']);
      redisDelStub.rejects(new Error('Redis error'));

      const key = 'some-key';
      const response = await redisCache.deleteCacheData(key);

      expect(redisKeysStub.calledWith(key)).to.be.true;
      expect(redisDelStub.calledWith(['key-1', 'key-2'])).to.be.true;
      expect(response).to.be.false;
    });

    it('Should handle empty keys array', async () => {
      redisKeysStub.resolves([]);
      redisDelStub.resolves(0);

      const key = 'some-key';
      const response = await redisCache.deleteCacheData(key);

      expect(redisKeysStub.calledWith(key)).to.be.true;
      expect(redisDelStub.calledWith([])).to.be.true;
      expect(response).to.be.true;
    });

    it('Should handle null keys response', async () => {
      redisKeysStub.resolves(null);

      const key = 'some-key';
      const response = await redisCache.deleteCacheData(key);

      expect(redisKeysStub.calledWith(key)).to.be.true;
      expect(redisDelStub.called).to.be.false;
      expect(response).to.be.true;
    });
  });

  describe('getKeysByPattern', () => {
    it('Should return keys matching pattern', async () => {
      const keys = ['key-1', 'key-2', 'key-3'];
      redisKeysStub.resolves(keys);

      const result = await redisCache.getKeysByPattern('key-*');

      expect(redisKeysStub.calledWith('key-*')).to.be.true;
      expect(result).to.deep.equal(keys);
    });

    it('Should return empty array on Redis error', async () => {
      redisKeysStub.rejects(new Error('Redis connection error'));

      const result = await redisCache.getKeysByPattern('key-*');

      expect(redisKeysStub.calledWith('key-*')).to.be.true;
      expect(result).to.deep.equal([]);
    });
  });

  describe('setOrExtendMarkCachedData', () => {
    it('Should set assessment mark data when not found', async () => {
      redisGetStub.resolves(null);
      fetchCandidateDetailsStub.resolves({
        assessmentTaker: assessmentTakerDependencyMockData.assessmentTaker,
      });
      redisSetStub.resolves('OK');

      const result = await redisCache.setOrExtendMarkCachedData(
        fakeAssessmentTaker
      );

      expect(
        redisGetStub.calledWith(
          `assessment-${fakeAssessmentTaker.assessmentId}-mark`
        )
      ).to.be.true;

      expect(
        fetchCandidateDetailsStub.calledWithExactly(
          fakeAssessmentTaker.id,
          'marker'
        )
      ).to.be.true;

      expect(redisSetStub.called).to.be.true;
      expect(result).to.be.null;
    });

    it('Should extend assessment mark data when already cached', async () => {
      const cachedData = {
        assessment:
          assessmentTakerDependencyMockData.assessmentTaker.assessment,
      };

      redisGetStub.resolves(JSON.stringify(cachedData));
      redisSetStub.resolves('OK');

      await redisCache.setOrExtendMarkCachedData(fakeAssessmentTaker);

      expect(
        redisGetStub.calledWith(
          `assessment-${fakeAssessmentTaker.assessmentId}-mark`
        )
      ).to.be.true;

      expect(fetchCandidateDetailsStub.called).to.be.false;
      expect(redisSetStub.called).to.be.true;
    });

    it('Should handle errors gracefully', async () => {
      redisGetStub.rejects(new Error('Redis error'));

      const result = await redisCache.setOrExtendMarkCachedData(
        fakeAssessmentTaker
      );

      expect(loggerStub.called).to.be.true;
      expect(result).to.be.undefined;
    });
  });
});
