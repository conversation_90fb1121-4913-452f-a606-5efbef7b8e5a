import { expect, use } from 'chai';
import * as sinon from 'sinon';
import sinonChai from 'sinon-chai';
import proxyquire from 'proxyquire';
import {
  SubmitAssessmentTestDTO,
  ImageCapture,
  ViolationCapture,
} from '../../src/helpers/types';

use(sinonChai);

describe('Proctoring Based Submission', () => {
  let protoringSubmission: any;
  let prismaStub: any;
  let sandbox: sinon.SinonSandbox;

  beforeEach(() => {
    sandbox = sinon.createSandbox();

    // Mock Prisma
    prismaStub = {
      assessmentTakerCapture: {
        createMany: sandbox.stub(),
      },
      screenShot: {
        createMany: sandbox.stub(),
      },
      idletime: {
        create: sandbox.stub(),
      },
      windowViolation: {
        create: sandbox.stub(),
      },
    };

    // Load the module with mocked dependencies using proxyquire
    protoringSubmission = proxyquire(
      '../../src/helpers/protoringBasedSubmission',
      {
        '../prisma': { default: prismaStub },
        '@noCallThru': true,
      }
    );
  });

  afterEach(() => {
    sandbox.restore();
  });

  describe('saveAssessmentMonitoringData', () => {
    it('should save monitoring data with no violations', async () => {
      const submissionData: SubmitAssessmentTestDTO = {
        candidateMonitoring: [
          {
            imageURL: 'https://example.com/image1.jpg',
            isIntegrityShot: false,
            timeStamp: '2023-01-01T10:00:00Z',
            violation: false,
          },
        ],
        screenMonitoring: [
          {
            imageURL: 'https://example.com/screen1.jpg',
            isIntegrityShot: false,
            timeStamp: '2023-01-01T10:00:00Z',
            violation: false,
          },
        ],
        startTime: '2023-01-01T10:00:00Z',
        finishTime: '2023-01-01T11:00:00Z',
        testResults: [],
      };

      prismaStub.assessmentTakerCapture.createMany.resolves();
      prismaStub.screenShot.createMany.resolves();

      const result = await protoringSubmission.saveAssessmentMonitoringData(
        submissionData,
        'test-taker-123'
      );

      expect(result).to.deep.equal({
        assessmentTakerShotSummary: {
          assessmentTakerTotalShots: 1,
          assessmentTakerTotalViolationShots: 0,
        },
        windowShotSummary: {
          windowTotalShots: 1,
          windowViolationTotalShots: 0,
        },
        windowViolationDuration: 0,
        windowViolationCount: 0,
      });

      expect(prismaStub.assessmentTakerCapture.createMany).to.have.been
        .calledOnce;
      expect(prismaStub.screenShot.createMany).to.have.been.calledOnce;
    });

    it('should handle empty monitoring arrays', async () => {
      const submissionData: SubmitAssessmentTestDTO = {
        candidateMonitoring: [],
        screenMonitoring: [],
        startTime: '2023-01-01T10:00:00Z',
        finishTime: '2023-01-01T11:00:00Z',
        testResults: [],
      };

      const result = await protoringSubmission.saveAssessmentMonitoringData(
        submissionData,
        'test-taker-123'
      );

      expect(result).to.deep.equal({
        assessmentTakerShotSummary: {
          assessmentTakerTotalShots: 0,
          assessmentTakerTotalViolationShots: 0,
        },
        windowShotSummary: {
          windowTotalShots: 0,
          windowViolationTotalShots: 0,
        },
        windowViolationDuration: 0,
        windowViolationCount: 0,
      });

      expect(prismaStub.assessmentTakerCapture.createMany).to.not.have.been
        .called;
      expect(prismaStub.screenShot.createMany).to.not.have.been.called;
    });

    it('should handle violation captures', async () => {
      const violationCapture: ViolationCapture = {
        shots: [
          {
            imageURL: 'https://example.com/violation1.jpg',
            timeStamp: '2023-01-01T10:05:00Z',
          },
        ],
        startTime: '2023-01-01T10:05:00Z',
        endTime: '2023-01-01T10:06:00Z',
        questNumber: 1,
        questionId: 'q1',
        testId: 't1',
        testNumber: 1,
        violationNumber: 1,
        violation: false,
      };

      const submissionData: SubmitAssessmentTestDTO = {
        candidateMonitoring: [violationCapture],
        screenMonitoring: [],
        startTime: '2023-01-01T10:00:00Z',
        finishTime: '2023-01-01T11:00:00Z',
        testResults: [],
      };

      prismaStub.idletime.create.resolves();
      prismaStub.assessmentTakerCapture.createMany.resolves();

      const result = await protoringSubmission.saveAssessmentMonitoringData(
        submissionData,
        'test-taker-123'
      );

      expect(
        result.assessmentTakerShotSummary.assessmentTakerTotalViolationShots
      ).to.equal(0);
      //expect(prismaStub.idletime.create).to.have.been.calledOnce;
    });
  });

  describe('getNonViolationAssessmentTakerShotData', () => {
    it('should process non-violation image captures', async () => {
      const imageCaptures: ImageCapture[] = [
        {
          imageURL: 'https://example.com/image1.jpg',
          isIntegrityShot: false,
          timeStamp: '2023-01-01T10:00:00Z',
          violation: false,
        },
        {
          imageURL: 'https://example.com/image2.jpg',
          isIntegrityShot: true,
          timeStamp: '2023-01-01T10:01:00Z',
          violation: false,
        },
      ];

      const result =
        await protoringSubmission.getNonViolationAssessmentTakerShotData(
          'test-taker-123',
          imageCaptures
        );

      expect(result.nonViolationAssesmentTakerShots).to.have.length(2);
      expect(result.testTakerShotCount).to.equal(2);
      expect(result.testTakerViolationShotCount).to.equal(0);

      expect(result.nonViolationAssesmentTakerShots[0]).to.deep.include({
        isViolationShot: false,
        assessmentTakerId: 'test-taker-123',
        imageURL: 'https://example.com/image1.jpg',
      });
    });

    it('should handle empty shots array', async () => {
      const result =
        await protoringSubmission.getNonViolationAssessmentTakerShotData(
          'test-taker-123',
          []
        );

      expect(result.nonViolationAssesmentTakerShots).to.have.length(0);
      expect(result.testTakerShotCount).to.equal(0);
      expect(result.testTakerViolationShotCount).to.equal(0);
    });
  });
});
