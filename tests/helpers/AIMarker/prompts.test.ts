import { expect } from 'chai';
import { preamble, TPreambleInput } from '../../../src/helpers/AImarker/prompt';

describe('preamble function', () => {
  const baseInput: TPreambleInput = {
    question: 'Test question',
    essay: 'Test essay content',
  };

  describe('basic functionality', () => {
    it('should generate preamble with default score when none provided', () => {
      const result = preamble(baseInput, 'Test rubrics');
      expect(result).to.include(
        'Score: " followed by a number between 0 and 10'
      );
    });

    it('should use provided score in the preamble', () => {
      const result = preamble(baseInput, 'Test rubrics', 15);
      expect(result).to.include(
        'Score: " followed by a number between 0 and 15'
      );
    });
  });

  describe('rubrics handling', () => {
    it('should include provided rubrics when available', () => {
      const rubrics = 'Specific test rubrics';
      const result = preamble(baseInput, rubrics);
      expect(result).to.include(`Base your score on these rubrics: ${rubrics}`);
    });

    it('should use default criteria when no rubrics provided', () => {
      const result = preamble(baseInput, '');
      expect(result).to.include('Evaluate based on:');
      expect(result).to.include('- Logical coherence');
      expect(result).to.include('- Depth of understanding');
      expect(result).to.include('- Clarity of expression');
      expect(result).to.include('- Relevance to topic');
    });

    it('should handle empty string rubrics', () => {
      const result = preamble(baseInput, '');
      expect(result).to.include('Evaluate based on:');
      expect(result).not.to.include('Base your score on these rubrics');
    });

    it('should handle undefined rubrics', () => {
      const result = preamble(baseInput, undefined as any);
      expect(result).to.include('Evaluate based on:');
      expect(result).not.to.include('Base your score on these rubrics');
    });
  });

  describe('content inclusion', () => {
    it('should include the question in the output', () => {
      const question = 'Special test question';
      const result = preamble({ ...baseInput, question }, 'Test rubrics');
      expect(result).to.include(`Question: ${question}`);
    });

    it('should include the essay in the output', () => {
      const essay = 'Special test essay content';
      const result = preamble({ ...baseInput, essay }, 'Test rubrics');
      expect(result).to.include(`Response: ${essay}`);
    });
  });

  describe('format requirements', () => {
    it('should include the score format instruction', () => {
      const result = preamble(baseInput, 'Test rubrics');
      expect(result).to.include(
        'IMPORTANT: Your response MUST start with "Score: "'
      );
      expect(result).to.include('Example response format:');
      expect(result).to.include('Score: 7');
    });

    it('should maintain consistent formatting', () => {
      const result = preamble(baseInput, 'Test rubrics');
      // Check for double newlines which might indicate formatting issues
      expect(result).not.to.include('\n\n\n');
    });
  });

  describe('edge cases', () => {
    it('should handle empty question', () => {
      const result = preamble({ ...baseInput, question: '' }, 'Test rubrics');
      expect(result).to.include('Question: ');
    });

    it('should handle empty essay', () => {
      const result = preamble({ ...baseInput, essay: '' }, 'Test rubrics');
      expect(result).to.include('Response: ');
    });

    it('should handle very long rubrics', () => {
      const longRubrics = 'a'.repeat(1000);
      const result = preamble(baseInput, longRubrics);
      expect(result).to.include(longRubrics);
    });

    it('should handle special characters in input', () => {
      const specialInput = {
        question: 'Question with "quotes" & special chars',
        essay: 'Essay with \nnewlines and\ttabs',
      };
      const result = preamble(specialInput, 'Rubrics with "quotes"');
      expect(result).to.include(specialInput.question);
      expect(result).to.include(specialInput.essay);
    });
  });
});
