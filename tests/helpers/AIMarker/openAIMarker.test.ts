import { expect } from 'chai';
import sinon from 'sinon';
import proxyquire from 'proxyquire';
import { AppError, HttpCode } from '../../../src/middlewares/errorHandler';

describe('OpenaiMarker', () => {
  let OpenaiMarker;
  let OpenAIStub;
  let openaiInstanceMock;
  let chatCompletionsMock;
  let processEnvBackup: NodeJS.ProcessEnv;

  beforeEach(() => {
    // Backup original process.env
    processEnvBackup = process.env;

    // Mock OpenAI instance and methods
    chatCompletionsMock = {
      create: sinon.stub(),
    };

    openaiInstanceMock = {
      chat: {
        completions: chatCompletionsMock,
      },
    };

    OpenAIStub = sinon.stub().returns(openaiInstanceMock);

    // Use proxyquire to mock the OpenAI dependency
    OpenaiMarker = proxyquire('../../../src/helpers/AImarker/openAIMarker', {
      openai: OpenAIStub,
      './prompt': {
        preamble: sinon.stub().returns('test prompt'),
        TUserInput: {},
      },
    }).OpenaiMarker;

    // Reset process.env for each test
    process.env = {
      ...processEnvBackup,
      AI_PROVIDER: 'GPT',
      OPENAI_API_KEY: 'test-openai-key',
      GEMINI_API_KEY: 'test-gemini-key',
      GEMINI_BASE_URL: 'https://gemini.test/api',
    };
  });

  afterEach(() => {
    // Restore original process.env
    process.env = processEnvBackup;
    sinon.restore();
  });

  describe('Constructor', () => {
    it('should initialize with OpenAI provider by default', () => {
      process.env.AI_PROVIDER = 'GPT';
      const marker = new OpenaiMarker();

      expect(marker.provider).to.equal('GPT');
      expect(marker.isGemini).to.be.false;
      expect(OpenAIStub.calledOnce).to.be.true;
      expect(OpenAIStub.firstCall.args[0]).to.deep.equal({
        apiKey: 'test-openai-key',
        baseURL: undefined,
      });
    });

    it('should initialize with Gemini provider when configured', () => {
      process.env.AI_PROVIDER = 'GEMINI';
      const marker = new OpenaiMarker();

      expect(marker.provider).to.equal('GEMINI');
      expect(marker.isGemini).to.be.true;
      expect(OpenAIStub.calledOnce).to.be.true;
      expect(OpenAIStub.firstCall.args[0]).to.deep.equal({
        apiKey: 'test-gemini-key',
        baseURL: 'https://gemini.test/api',
      });
    });

    it('should throw error when API key is missing for GPT', () => {
      delete process.env.OPENAI_API_KEY;
      process.env.AI_PROVIDER = 'GPT';

      expect(() => new OpenaiMarker())
        .to.throw(AppError)
        .with.property('message', 'Missing API key for GPT provider');
    });

    it('should throw error when API key is missing for Gemini', () => {
      delete process.env.GEMINI_API_KEY;
      process.env.AI_PROVIDER = 'GEMINI';

      expect(() => new OpenaiMarker())
        .to.throw(AppError)
        .with.property('message', 'Missing API key for GEMINI provider');
    });
  });

  describe('marker method', () => {
    const testInput = {
      question: 'Test question',
      essay: 'Test essay content',
      score: 10,
      rubrics: ['Rubric 1', 'Rubric 2'],
    };

    it('should call GPT model with correct parameters', async () => {
      process.env.AI_PROVIDER = 'GPT';
      const marker = new OpenaiMarker();

      const mockResponse = {
        choices: [
          {
            message: {
              content: 'Test response content',
            },
          },
        ],
      };
      chatCompletionsMock.create.resolves(mockResponse);

      const result = await marker.marker(testInput);

      expect(chatCompletionsMock.create.calledOnce).to.be.true;
      const callArgs = chatCompletionsMock.create.firstCall.args[0];
      expect(callArgs.model).to.equal('gpt-3.5-turbo-1106');
      expect(callArgs.messages).to.deep.equal([
        {
          role: 'user',
          content: 'test prompt',
        },
      ]);
      expect(result).to.equal('Test response content');
    });

    it('should call Gemini model with correct parameters', async () => {
      process.env.AI_PROVIDER = 'GEMINI';
      const marker = new OpenaiMarker();

      const mockResponse = {
        choices: [
          {
            message: {
              content: 'Test response content',
            },
          },
        ],
      };
      chatCompletionsMock.create.resolves(mockResponse);

      const result = await marker.marker(testInput);

      expect(chatCompletionsMock.create.calledOnce).to.be.true;
      const callArgs = chatCompletionsMock.create.firstCall.args[0];
      expect(callArgs.model).to.equal('gemini-2.0-flash');
      expect(result).to.equal('Test response content');
    });

    it('should handle rate limit error (429)', async () => {
      const marker = new OpenaiMarker();

      const error = new Error('Rate limit exceeded') as unknown as AppError;
      error.httpCode = 429;
      chatCompletionsMock.create.rejects(error);

      try {
        await marker.marker(testInput);
        expect.fail('Should have thrown an error');
      } catch (error) {
        expect(error).to.be.instanceOf(AppError);
        expect((error as AppError).httpCode).to.equal(
          HttpCode.INTERNAL_SERVER_ERROR
        );
        expect((error as AppError).message).to.equal(
          'GPT API error: Rate limit exceeded'
        );
      }
    });

    it('should handle invalid API key error (401)', async () => {
      const marker = new OpenaiMarker();

      const error = new Error('Invalid API key') as unknown as AppError;
      error.httpCode = 401;
      chatCompletionsMock.create.rejects(error);

      try {
        await marker.marker(testInput);
        expect.fail('Should have thrown an error');
      } catch (error) {
        expect(error).to.be.instanceOf(AppError);
        expect((error as AppError).httpCode).to.equal(
          HttpCode.INTERNAL_SERVER_ERROR
        );
        expect((error as AppError).message).to.equal(
          'GPT API error: Invalid API key'
        );
        expect((error as AppError).isOperational).to.be.false;
      }
    });

    it('should handle generic API errors', async () => {
      const marker = new OpenaiMarker();

      const error = new Error('Some API error') as unknown as AppError;
      error.data = { data: { detail: 'Internal server error' } };
      chatCompletionsMock.create.rejects(error);

      try {
        await marker.marker(testInput);
        expect.fail('Should have thrown an error');
      } catch (error) {
        expect(error).to.be.instanceOf(AppError);
        expect((error as AppError).httpCode).to.equal(
          HttpCode.INTERNAL_SERVER_ERROR
        );
        expect((error as AppError).message).to.equal(
          'GPT API error: Some API error'
        );
        expect((error as AppError).isOperational).to.be.false;
        expect((error as AppError).message).to.deep.equal(
          'GPT API error: Some API error'
        );
      }
    });

    it('should handle unexpected errors without response', async () => {
      const marker = new OpenaiMarker();

      const error = new Error('Network error');
      chatCompletionsMock.create.rejects(error);

      try {
        await marker.marker(testInput);
        expect.fail('Should have thrown an error');
      } catch (error) {
        expect(error).to.be.instanceOf(AppError);
        expect((error as AppError).message).to.equal(
          'GPT API error: Network error'
        );
      }
    });
  });
});
