import { expect, use } from 'chai';
import * as sinon from 'sinon';
import sinon<PERSON>hai from 'sinon-chai';
import proxyquire from 'proxyquire';
import { AssessmentTaker, QuestionFlag } from '@prisma/client';
import { TCandidateData } from '../../src/dtos/assessmentTakerDtos';

use(sinonChai);

describe('Kafka Publish Message', () => {
  let kafkaPublishMessage: any;
  let kafkaPublisherStub: any;
  let sandbox: sinon.SinonSandbox;

  beforeEach(() => {
    sandbox = sinon.createSandbox();

    // Mock KafkaPublisher instance
    kafkaPublisherStub = {
      connect: sandbox.stub().resolves(),
      disconnect: sandbox.stub().resolves(),
      publish: sandbox.stub().resolves(),
    };

    // Mock the constructor to return our stub
    const KafkaPublisherStub = sandbox.stub().returns(kafkaPublisherStub);

    // Load the module with mocked dependencies using proxyquire
    kafkaPublishMessage = proxyquire('../../src/helpers/kafkaPublishMessage', {
      './kafkaPublisher': { default: KafkaPublisherStub },
      '@noCallThru': true,
    });
  });

  afterEach(() => {
    sandbox.restore();
  });

  describe('assessmentInProgressPublish', () => {
    it('should publish assessment in progress message', async () => {
      const assessmentTakerData: TCandidateData = {
        id: 'taker-123',
        email: '<EMAIL>',
        organizationId: 'org-456',
        assessment: {
          id: 'assessment-789',
          title: 'JavaScript Test',
          assessmentDuration: 60,
          tests: [],
        },
        dispatcher: 'test-dispatcher',
        assessmentDuration: 3600,
      } as unknown as TCandidateData;

      const duration = 3600;

      await kafkaPublishMessage.assessmentInProgressPublish(
        assessmentTakerData,
        duration
      );

      expect(kafkaPublisherStub.connect).to.have.been.calledOnce;
      expect(kafkaPublisherStub.publish).to.have.been.calledThrice;
      expect(kafkaPublisherStub.disconnect).to.have.been.calledOnce;

      // Verify the first publish call (assessment-progress)
      const firstPublishCall = kafkaPublisherStub.publish.getCall(0);
      expect(firstPublishCall.args[0]).to.equal('assessment-progress');
      // expect(firstPublishCall.args[1]).to.include({
      //   assessmentTaker: {
      //     assessmentId: 'assessment-789',
      //     organizationId: 'org-456',
      //     status: 'In progress',
      //   },
      //   seconds: 3600,
      // });

      // Verify the second publish call (progress-link)
      const secondPublishCall = kafkaPublisherStub.publish.getCall(1);
      expect(secondPublishCall.args[0]).to.equal('progress-link');
      expect(secondPublishCall.args[1]).to.deep.equal({
        id: 'taker-123',
        seconds: 3600,
      });

      // Verify the third publish call (notification-progress)
      const thirdPublishCall = kafkaPublisherStub.publish.getCall(2);
      expect(thirdPublishCall.args[0]).to.equal('notification-progress');
      expect(thirdPublishCall.args[1]).to.deep.include({
        notice: {
          testTakerId: 'taker-123',
          dispatcher: 'test-dispatcher',
          organizationId: 'org-456',
          testTakerEmail: '<EMAIL>',
          assessmentId: 'assessment-789',
          assessmentName: 'JavaScript Test',
        },
      });
    });
  });

  it('should handle kafka connection errors', async () => {
    const assessmentTakerData: TCandidateData = {
      id: 'taker-123',
      email: '<EMAIL>',
      organizationId: 'org-456',
      assessment: {
        id: 'assessment-789',
        title: 'JavaScript Test',
        assessmentDuration: 60, // minutes
        tests: [],
      },
      dispatcher: 'test-dispatcher',
      assessmentDuration: 3600,
    } as unknown as TCandidateData;

    kafkaPublisherStub.connect.rejects(new Error('Connection failed'));

    try {
      await kafkaPublishMessage.assessmentInProgressPublish(
        assessmentTakerData,
        3600
      );
      expect.fail('Should have thrown an error');
    } catch (error) {
      expect(error.message).to.equal('Connection failed');
    }
  });

  describe('assessmentSubmitPublish', () => {
    it('should publish assessment submission message', async () => {
      const assessmentTakerData: TCandidateData = {
        id: 'taker-123',
        email: '<EMAIL>',
        organizationId: 'org-456',
        assessment: {
          id: 'assessment-789',
          title: 'JavaScript Test',
          assessmentDuration: 60, // minutes
          tests: [],
        },
        dispatcher: 'test-dispatcher',
      } as unknown as TCandidateData;

      const dataResult = {
        assessmentEndTime: '2023-01-01T11:00:00Z',
        testTakerDurationSeconds: 3600,
        overallAssessmentPercentage: 85,
        overallAssessmentPassScore: 70,
        status: 'COMPLETED',
        submissionType: 'MANUAL',
        testResults: [],
      };

      await kafkaPublishMessage.assessmentSubmitPublish(
        assessmentTakerData,
        dataResult
      );

      expect(kafkaPublisherStub.connect).to.have.been.calledOnce;
      expect(kafkaPublisherStub.publish).to.have.been.calledThrice;
      expect(kafkaPublisherStub.disconnect).to.have.been.calledOnce;

      // Verify submit-assessment-results publish
      const firstPublishCall = kafkaPublisherStub.publish.getCall(0);
      expect(firstPublishCall.args[0]).to.equal('submit-assessment-results');
      expect(firstPublishCall.args[1]).to.deep.include({
        ...dataResult,
        completionTime: '2023-01-01T11:00:00Z',
      });

      // Verify complete-link publish
      const secondPublishCall = kafkaPublisherStub.publish.getCall(1);
      expect(secondPublishCall.args[0]).to.equal('complete-link');
      expect(secondPublishCall.args[1]).to.deep.include({
        testTakerId: 'taker-123',
        completionTime: '2023-01-01T11:00:00Z',
        testTakerDurationSeconds: 3600,
        overallAssessmentPercentage: 85,
        overallAssessmentPassScore: 70,
        status: 'COMPLETED',
        submissionType: 'MANUAL',
      });

      // Verify notification-completed publish
      const thirdPublishCall = kafkaPublisherStub.publish.getCall(2);
      expect(thirdPublishCall.args[0]).to.equal('notification-completed');
      expect(thirdPublishCall.args[1]).to.deep.include({
        notice: {
          testTakerId: 'taker-123',
          dispatcher: 'test-dispatcher',
          organizationId: 'org-456',
          testTakerEmail: '<EMAIL>',
          assessmentId: 'assessment-789',
          assessmentName: 'JavaScript Test',
        },
      });
    });
  });

  describe('questionFlaggingPublish', () => {
    it('should publish question flagging message', async () => {
      const assessmentTakerInfo: AssessmentTaker = {
        id: 'taker-123',
        email: '<EMAIL>',
        dispatcher: 'test-dispatcher',
        organizationId: 'org-456',
        assessmentId: 'assessment-789',
        status: 'IN_PROGRESS',
        startTime: new Date(),
        createdAt: new Date(),
        updatedAt: new Date(),
      } as AssessmentTaker;

      const questionFlaggedSavedData: QuestionFlag = {
        id: 'flag-123',
        questionId: 'question-456',
        assessmentTakerId: 'taker-123',
        flagReasons: ['Unclear question'],
        createdAt: new Date(),
        updatedAt: new Date(),
      } as unknown as QuestionFlag;

      await kafkaPublishMessage.questionFlaggingPublish(
        assessmentTakerInfo,
        questionFlaggedSavedData
      );

      expect(kafkaPublisherStub.connect).to.have.been.calledOnce;
      expect(kafkaPublisherStub.publish).to.have.been.calledTwice;
      expect(kafkaPublisherStub.disconnect).to.have.been.calledOnce;

      // Verify report-question-flagged publish
      const firstPublishCall = kafkaPublisherStub.publish.getCall(0);
      expect(firstPublishCall.args[0]).to.equal('report-question-flagged');
      expect(firstPublishCall.args[1]).to.deep.equal(questionFlaggedSavedData);

      // Verify notification-question-flagged publish
      const secondPublishCall = kafkaPublisherStub.publish.getCall(1);
      expect(secondPublishCall.args[0]).to.equal(
        'notification-question-flagged'
      );
      expect(secondPublishCall.args[1]).to.deep.include({
        notice: {
          ...questionFlaggedSavedData,
          dispatcher: 'test-dispatcher',
          organizationId: 'org-456',
        },
      });
    });
  });

  describe('questionUnflaggingPublish', () => {
    it('should publish question unflagging message', async () => {
      const questionUnflaggedData = {
        id: 'flag-123',
        questionId: 'question-456',
        assessmentTakerId: 'taker-123',
      };

      await kafkaPublishMessage.questionUnflaggingPublish(
        questionUnflaggedData
      );

      expect(kafkaPublisherStub.connect).to.have.been.calledOnce;
      expect(kafkaPublisherStub.publish).to.have.been.called;
      expect(kafkaPublisherStub.disconnect).to.have.been.calledOnce;

      const publishCall = kafkaPublisherStub.publish.getCall(0);
      expect(publishCall.args[0]).to.equal('report-question-unflagged');
      //expect(publishCall.args[1]).to.deep.equal(questionUnflaggedData);
    });
  });

  describe('viewAssessmentLinkPublish', () => {
    it('should publish view assessment link message', async () => {
      const assessmentTakerData: TCandidateData = {
        id: 'taker-123',
        email: '<EMAIL>',
        organizationId: 'org-456',
        assessment: {
          id: 'assessment-789',
          title: 'JavaScript Test',
        },
        dispatcher: 'test-dispatcher',
      } as unknown as TCandidateData;

      const duration = 3600;

      await kafkaPublishMessage.viewAssessmentLinkPublish(
        assessmentTakerData,
        duration
      );

      expect(kafkaPublisherStub.connect).to.have.been.calledOnce;
      expect(kafkaPublisherStub.publish).to.have.been.calledOnce;
      expect(kafkaPublisherStub.disconnect).to.have.been.calledOnce;

      const publishCall = kafkaPublisherStub.publish.getCall(0);
      expect(publishCall.args[0]).to.equal('view-link');
      // expect(publishCall.args[1]).to.deep.include({
      //   testTakerId: 'taker-123',
      //   assessmentId: 'assessment-789',
      //   assessmentName: 'JavaScript Test',
      //   testTakerEmail: '<EMAIL>',
      //   organizationId: 'org-456',
      //   dispatcher: 'test-dispatcher',
      //   duration: 3600,
      // });
    });
  });
});
