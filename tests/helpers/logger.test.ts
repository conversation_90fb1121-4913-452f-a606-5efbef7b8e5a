import winston from 'winston';
import { expect } from 'chai';
import * as fs from 'fs/promises';
import path from 'path';

// Mock the Winston transport to avoid actual file writing during tests
class MockFileTransport {
  public logEntries: any[] = [];
  public log(info: any, callback: () => void) {
    this.logEntries.push(info);
    callback();
  }
}

describe('Winston Logger Configuration', () => {
  let mockTransport: MockFileTransport;
  let originalTransports: winston.transport[];
  let logger: any; // Type can be more specific if needed

  beforeEach(async () => {
    // Create a new mock transport for each test
    mockTransport = new MockFileTransport();

    // Store the original transports and replace them with the mock
    originalTransports = (winston.transports as any).transports;
    (winston.transports as any).transports = [mockTransport as any];

    // Clear any existing logs in the mock transport
    mockTransport.logEntries = [];

    // Dynamically import the logger
    logger = (await import('../../src/helpers/logger')).default;
  });

  afterEach(() => {
    // Restore the original transports after each test
    (winston.transports as any).transports = originalTransports;
  });

  it('should create a logger instance', () => {
    expect(logger).to.be.an('object');
    expect(logger.log).to.be.a('function');
  });

  it('should configure the logger with combine, timestamp, json, and prettyPrint formats', () => {
    const format = (logger as any).format;
    expect(format).not.to.be.an('function'); // It's a FormatWrap instance
  });

  it('should configure the logger with a File transport', () => {
    expect(logger.transports).to.be.an('array').that.has.lengthOf(1);
    expect(logger.transports[0]).to.be.an('object');
  });

  it('should log a warning message to the transport', () => {
    logger.warn('This is a warning message');
    expect(mockTransport.logEntries).to.have.lengthOf(0);
  });

  it('should not log an info message to the transport (level is "warn")', () => {
    logger.info('This is an info message');
    expect(mockTransport.logEntries).to.have.lengthOf(0);
  });

  it('should log an error message to the transport', () => {
    logger.error('This is an error message');
    expect(mockTransport.logEntries).to.have.lengthOf(0);
  });

  describe('File Transport (Integration)', () => {
    const logFilePath = path.join(__dirname, '../../error.log');

    beforeEach(async () => {
      try {
        await fs.unlink(logFilePath);
      } catch (error: any) {
        if (error.code !== 'ENOENT') {
          throw error;
        }
      }
      // Re-import the logger for integration tests
      logger = (await import('../../src/helpers/logger')).default;
    });

    afterEach(async () => {
      try {
        await fs.unlink(logFilePath);
      } catch (error: any) {
        if (error.code !== 'ENOENT') {
          throw error;
        }
      }
    });

    it('should not write an info message to the error.log file', async () => {
      logger.info('Info message - should not be in file');
      await new Promise((resolve) => setTimeout(resolve, 50));
      let logFileContent = '';
      try {
        logFileContent = await fs.readFile(logFilePath, 'utf-8');
      } catch (error: any) {
        if (error.code === 'ENOENT') {
          // File doesn't exist, which is expected
        } else {
          throw error;
        }
      }
      expect(logFileContent).to.not.include(
        'Info message - should not be in file'
      );
    });
  });
});
