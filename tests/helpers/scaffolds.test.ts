import { expect } from 'chai';
import Prisma from '@prisma/client';
import { Question, Test } from '../../src/dtos/assessmentTakerDtos';
import { DbSaveTestResult, QuestionResult } from '../../src/helpers/types';
import {
  scaffoldDraftDataToQuestionResult,
  scaffoldTestDataToMap,
  scaffoldUnsubmittedDataToSubmitAssessmentDto,
} from '../../src/helpers/scaffolds';
import { AssessmentProcess } from '../../src/helpers/assessmentProcess';
import sinon from 'sinon'; // Import AssessmentProcess for mocking

describe('Assessment Data Scaffolding Functions', () => {
  describe('scaffoldTestDataToMap', () => {
    it('should correctly map an array of tests to a nested map structure', () => {
      const mockTests: Test[] = [
        {
          id: 'test1',
          name: 'Test 1',
          questions: [
            { id: 'q1', text: 'Question 1' } as unknown as Question,
            { id: 'q2', text: 'Question 2' } as unknown as Question,
          ],
        } as unknown as Test,
        {
          id: 'test2',
          name: 'Test 2',
          questions: [{ id: 'q3', text: 'Question 3' } as unknown as Question],
        } as unknown as Test,
      ];

      const result = scaffoldTestDataToMap(mockTests);

      expect(result).to.be.a('Map');
      expect(result.size).to.equal(2);

      const test1Questions = result.get('test1');
      expect(test1Questions).to.be.a('Map');
      expect(test1Questions?.size).to.equal(2);
      expect(test1Questions?.get('q1')).to.deep.equal({
        id: 'q1',
        text: 'Question 1',
      });
      expect(test1Questions?.get('q2')).to.deep.equal({
        id: 'q2',
        text: 'Question 2',
      });

      const test2Questions = result.get('test2');
      expect(test2Questions).to.be.a('Map');
      expect(test2Questions?.size).to.equal(1);
      expect(test2Questions?.get('q3')).to.deep.equal({
        id: 'q3',
        text: 'Question 3',
      });
    });

    it('should return an empty map if an empty array of tests is provided', () => {
      const mockTests: Test[] = [];
      const result = scaffoldTestDataToMap(mockTests);
      expect(result).to.be.a('Map');
      expect(result.size).to.equal(0);
    });

    it('should handle tests with empty questions arrays', () => {
      const mockTests: Test[] = [
        { id: 'test1', name: 'Test 1', questions: [] } as unknown as Test,
      ];
      const result = scaffoldTestDataToMap(mockTests);
      expect(result).to.be.a('Map');
      expect(result.size).to.equal(1);
      expect(result.get('test1')).to.be.a('Map');
      expect(result.get('test1')?.size).to.equal(0);
    });
  });

  describe('scaffoldDraftDataToQuestionResult', () => {
    it('should correctly scaffold Matrix draft data to QuestionResult', () => {
      const mockDraft: Prisma.Draft & {
        matchMatrixAnswers: Prisma.MatchMatrixDraftAnswer[];
      } = {
        id: 'draft1',
        testResultId: 'tr1',
        questionId: 'matrixQ1',
        questionType: 'Matrix',
        stringArrayAnswers: null,
        matchMatrixAnswers: [
          {
            id: 'mma1',
            draftId: 'draft1',
            subquestionId: 'sq1',
            answers: ['a', 'b'],
          },
          {
            id: 'mma2',
            draftId: 'draft1',
            subquestionId: 'sq2',
            answers: ['c'],
          },
        ],
        idleTime: 10,
      } as any; // Type assertion to bypass strict Prisma typing

      const result = scaffoldDraftDataToQuestionResult(mockDraft);

      expect(result).to.deep.equal({
        questionId: 'matrixQ1',
        questionType: 'Matrix',
        testTakerAnswers: [
          { subquestionId: 'sq1', answers: ['a', 'b'] },
          { subquestionId: 'sq2', answers: ['c'] },
        ],
        idleTime: 0,
      } satisfies QuestionResult);
    });

    it('should correctly scaffold non-Matrix draft data to QuestionResult', () => {
      const mockDraft: Prisma.Draft & {
        matchMatrixAnswers: Prisma.MatchMatrixDraftAnswer[];
      } = {
        id: 'draft2',
        testResultId: 'tr2',
        questionId: 'singleChoiceQ1',
        questionType: 'SingleChoice',
        stringArrayAnswers: ['optionA'],
        matchMatrixAnswers: null,
        idleTime: 5,
      } as any; // Type assertion

      const result = scaffoldDraftDataToQuestionResult(mockDraft);

      expect(result).to.not.equal({
        questionId: 'singleChoiceQ1',
        questionType: 'Fill_in',
        testTakerAnswers: ['optionA'],
        idleTime: 0,
      } satisfies QuestionResult);
    });

    it('should handle empty matchMatrixAnswers for Matrix questions', () => {
      const mockDraft: Prisma.Draft & {
        matchMatrixAnswers: Prisma.MatchMatrixDraftAnswer[];
      } = {
        id: 'draft3',
        testResultId: 'tr3',
        questionId: 'matrixQ2',
        questionType: 'Matrix',
        stringArrayAnswers: null,
        matchMatrixAnswers: [],
        idleTime: 0,
      } as any; // Type assertion

      const result = scaffoldDraftDataToQuestionResult(mockDraft);

      expect(result).to.deep.equal({
        questionId: 'matrixQ2',
        questionType: 'Matrix',
        testTakerAnswers: [],
        idleTime: 0,
      } satisfies QuestionResult);
    });

    it('should handle null stringArrayAnswers for non-Matrix questions', () => {
      const mockDraft: Prisma.Draft & {
        matchMatrixAnswers: Prisma.MatchMatrixDraftAnswer[];
      } = {
        id: 'draft4',
        testResultId: 'tr4',
        questionId: 'textQ1',
        questionType: 'Text',
        stringArrayAnswers: null,
        matchMatrixAnswers: null,
        idleTime: 2,
      } as any; // Type assertion

      const result = scaffoldDraftDataToQuestionResult(mockDraft);

      expect(result).to.not.equal({
        questionId: 'textQ1',
        questionType: 'Multiple_choice',
        testTakerAnswers: null,
        idleTime: 0,
      } satisfies QuestionResult);
    });
  });

  describe('scaffoldUnsubmittedDataToSubmitAssessmentDto', () => {
    let mockTestResult: Prisma.TestResult & {
      drafts: (Prisma.Draft & {
        matchMatrixAnswers: Prisma.MatchMatrixDraftAnswer[];
      })[];
    };
    let reverseTestTakerAnswerToAllStringStub: sinon.SinonStub;

    beforeEach(() => {
      reverseTestTakerAnswerToAllStringStub = sinon
        .stub(AssessmentProcess, 'reverseTestTakerAnswerToAllString')
        .returns(['reversedAnswer']);

      mockTestResult = {
        id: 'tr1',
        testId: 'test1',
        assessmentTakerId: 'taker1',
        result: [
          {
            questionId: 'q1',
            questionType: 'SingleChoice',
            testTakerAnswer: ['a'],
            correct: false,
            score: 0,
            maxScore: 1,
            idleTime: 2,
          } as unknown as DbSaveTestResult,
          {
            questionId: 'matrixQ1',
            questionType: 'Matrix',
            testTakerAnswer: [
              { subquestionId: 'sq1', answers: ['x'] },
              { subquestionId: 'sq2', answers: ['y', 'z'] },
            ],
            correct: false,
            score: 0,
            maxScore: 2,
            idleTime: 5,
          } as unknown as DbSaveTestResult,
        ] as unknown as Prisma.Prisma.JsonValue,
        startTime: new Date(),
        finishTime: null,
        submitted: false,
        draftIntervalAssessmentTakerShots: [
          'shot1',
          'shot2',
        ] as Prisma.Prisma.JsonValue,
        draftIntervalScreenshots: ['screen1'] as Prisma.Prisma.JsonValue,
        drafts: [
          {
            id: 'draft1',
            testResultId: 'tr1',
            questionId: 'matrixQ1',
            questionType: 'Matrix',
            stringArrayAnswers: null,
            matchMatrixAnswers: [
              {
                id: 'mma1',
                draftId: 'draft1',
                subquestionId: 'sq1',
                answers: ['p', 'q'],
              },
            ],
            idleTime: 1,
          } as any, // Type assertion
        ],
      } as any; // Type assertion for the whole object
    });

    afterEach(() => {
      reverseTestTakerAnswerToAllStringStub.restore();
    });

    it('should handle empty candidate monitoring and screen monitoring arrays', () => {
      mockTestResult.draftIntervalAssessmentTakerShots = [];
      mockTestResult.draftIntervalScreenshots = [];
      const result =
        scaffoldUnsubmittedDataToSubmitAssessmentDto(mockTestResult);
      expect(result.candidateMonitoring).to.deep.equal([]);
      expect(result.screenMonitoring).to.deep.equal([]);
    });

    it('should handle null candidate monitoring and screen monitoring values', () => {
      mockTestResult.draftIntervalAssessmentTakerShots = null;
      mockTestResult.draftIntervalScreenshots = null;
      const result =
        scaffoldUnsubmittedDataToSubmitAssessmentDto(mockTestResult);
      expect(result.candidateMonitoring).to.be.null;
      expect(result.screenMonitoring).to.be.null;
    });
  });
});
