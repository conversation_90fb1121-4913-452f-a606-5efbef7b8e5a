import { expect } from 'chai';
import { AppPermissions } from '../../src/helpers/defaultPermission'; // Adjust the path as needed

describe('AppPermissions Enum', () => {
  it('should have the correct value for admin', () => {
    expect(AppPermissions.admin).to.equal('admin');
  });

  it('should have the correct value for organizationAdmin', () => {
    expect(AppPermissions.organizationAdmin).to.equal('organization admin');
  });

  it('should have the correct value for manageAssessments', () => {
    expect(AppPermissions.manageAssessments).to.equal('manage assessments');
  });

  it('should have the correct value for viewAssessments', () => {
    expect(AppPermissions.viewAssessments).to.equal('view assessments');
  });

  it('should have the correct value for viewReports', () => {
    expect(AppPermissions.viewReports).to.equal('view reports');
  });

  it('should have the correct value for manageReports', () => {
    expect(AppPermissions.manageReports).to.equal('manage reports');
  });
});
