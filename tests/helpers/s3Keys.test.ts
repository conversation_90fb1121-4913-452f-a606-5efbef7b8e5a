import { expect } from 'chai';
import sinon from 'sinon';
import { KeyParams, PresignedURLDto } from '../../src/helpers/types';
import S3KeysGenerator from '../../src/helpers/s3Keys';
interface S3KeysGeneratorMock {
  getKey: (params: KeyParams, query: PresignedURLDto) => string;
  candidateShotKey: (params: KeyParams & { filename: string }) => string;
  idShotKey: (params: KeyParams & { filename: string }) => string;
  headShotKey: (params: KeyParams & { filename: string }) => string;
  screenShotKey: (params: KeyParams & { filename: string }) => string;
}
describe('S3KeysGenerator', () => {
  const s3KeysGeneratorMock: S3KeysGeneratorMock =
    S3KeysGenerator as unknown as S3KeysGeneratorMock;
  describe('getKey', () => {
    let params: KeyParams;
    let query: PresignedURLDto;

    beforeEach(() => {
      params = {
        organizationId: 'org123',
        assessmentName: 'Sample Assessment',
        assessmentTakerId: 'taker456',
        email: '<EMAIL>',
        expiryDate: '2025-01-01',
      };

      query = {
        mimeType: 'image/png',
        keyType: 'id-shot',
        contentMD5: 'mock-content-md5',
      };
    });

    afterEach(() => {
      sinon.restore();
    });

    it('should generate an ID shot key when keyType is "id-shot"', () => {
      const idShotKeyStub = sinon
        .stub(s3KeysGeneratorMock, 'idShotKey')
        .returns('id-shot-key');
      const key = s3KeysGeneratorMock.getKey(params, query);

      expect(idShotKeyStub.calledOnce).to.be.true;
      expect(key).to.equal('id-shot-key');
    });

    it('should generate a head shot key when keyType is "head-shot"', () => {
      query.keyType = 'head-shot';
      const headShotKeyStub = sinon
        .stub(s3KeysGeneratorMock, 'headShotKey')
        .returns('head-shot-key');

      const key = s3KeysGeneratorMock.getKey(params, query);

      expect(headShotKeyStub.calledOnce).to.be.true;
      expect(key).to.equal('head-shot-key');
    });

    it('should generate a candidate monitoring shot key when keyType is "candidate-monitoring"', () => {
      query.keyType = 'candidate-monitoring';
      const candidateShotKeyStub = sinon
        .stub(s3KeysGeneratorMock, 'candidateShotKey')
        .returns('candidate-shot-key');

      const key = s3KeysGeneratorMock.getKey(params, query);

      expect(candidateShotKeyStub.calledOnce).to.be.true;
      expect(key).to.equal('candidate-shot-key');
    });

    it('should generate a screenshot key when keyType is "screen-monitoring"', () => {
      query.keyType = 'screen-monitoring';
      const screenShotKeyStub = sinon
        .stub(s3KeysGeneratorMock, 'screenShotKey')
        .returns('screenshot-key');

      const key = s3KeysGeneratorMock.getKey(params, query);

      expect(screenShotKeyStub.calledOnce).to.be.true;
      expect(key).to.equal('screenshot-key');
    });

    it('should replace spaces with hyphens in assessmentName', () => {
      sinon
        .stub(s3KeysGeneratorMock, 'idShotKey')
        .callsFake(({ assessmentName }) => assessmentName);
      params.assessmentName = 'Assessment with Spaces';

      const key = s3KeysGeneratorMock.getKey(params, query);

      expect(key).to.equal('Assessment-with-Spaces');
    });
  });
});
