// src/lib/JwtPayloadEncryption.test.ts

import * as crypto from 'crypto';
import { expect } from 'chai'; // Using Chai for assertions
import 'mocha'; // Importing mocha types for test structure (describe, it, etc.)
import { JsonWebTokenError } from 'jsonwebtoken';

// Assuming JwtPayloadEncryption and types are in the same directory or adjust path
import JwtPayloadEncryption from '../../src/helpers/jwtPayloadEncryption';
import { UnEncryptedJwtPayload, TokenPayload } from '../../src/helpers/types';

// --- Helper Functions (Identical to the Jest version) ---

// Helper function to encrypt data for testing purposes (mirrors the logic)
const encryptData = (
  payload: TokenPayload,
  key: string,
  algo: string
): string => {
  const iv = crypto.randomBytes(16); // AES block size is 16 bytes (128 bits)
  const cipher = crypto.createCipheriv(algo, Buffer.from(key, 'base64'), iv);
  const encryptedPayload =
    cipher.update(JSON.stringify(payload), 'utf8', 'base64') +
    cipher.final('base64');
  return `${iv.toString('base64')}:${encryptedPayload}`;
};

// --- Test Suite ---

describe('JwtPayloadEncryption', () => {
  let originalEnv: NodeJS.ProcessEnv; // To store original environment variables

  // Define valid test data
  const validAlgo = 'aes-256-cbc'; // Common algorithm, requires 32-byte key
  const validKey = crypto.randomBytes(32).toString('base64'); // Generate a valid 32-byte key
  const wrongKey = crypto.randomBytes(32).toString('base64'); // Different key for testing mismatches
  const samplePayload: TokenPayload = {
    userId: 'user-123', // Existing field - OK
    email: '<EMAIL>', // ADDED: Provide a valid example email
    role: 'admin', // Existing field - OK
    system: false, // ADDED: Provide a boolean value (true or false)
    organizationId: 'org-456', // ADDED: Provide a valid example Org ID
    permissions: ['read', 'write'], // Existing field - OK
    TOKEN_TYPE: 'access', // ADDED: Provide a valid example token type
  };
  const extraProps = {
    iat: 1678886400,
    exp: 1678887000,
    iss: 'test-issuer',
  };

  // Function to create the input object for decryptPayload
  const createTestInput = (
    data: string | undefined | null | number, // Added number for invalid type test
    otherProps: object = extraProps
  ): UnEncryptedJwtPayload | any => {
    // Return any for invalid input tests
    const input: any = { ...otherProps };
    if (data !== undefined && data !== null) {
      input.data = data;
    } else if (data === null) {
      input.data = null; // Explicitly test null
    }
    // If data is undefined, the property won't be added, testing missing property
    return input as UnEncryptedJwtPayload;
  };

  // --- Mocha Hooks for Environment Variable Management ---
  before(() => {
    // Runs once before all tests in this block
    originalEnv = { ...process.env }; // Store original env
  });

  after(() => {
    // Runs once after all tests in this block
    process.env = originalEnv; // Restore original env
  });

  beforeEach(() => {
    // Runs before each test
    // Reset environment variables to a clean state for each test
    process.env = { ...originalEnv }; // Start with original env
    // Set default valid environment variables for most tests
    process.env.JWT_ENCRYPTION_KEY = validKey;
    process.env.JWT_ENCRYPTION_ALGO = validAlgo;
  });

  // --- Happy Path ---
  describe('Happy Path', () => {
    it('should correctly decrypt a valid payload and merge properties', () => {
      const encryptedData = encryptData(samplePayload, validKey, validAlgo);
      const input = createTestInput(encryptedData, extraProps);

      const result = JwtPayloadEncryption.decryptPayload(input);

      // Check if decrypted payload matches original (using Chai's deep include)
      expect(result).to.deep.include(samplePayload);
      // Check if extra properties are merged
      expect(result).to.deep.include(extraProps);
      // Ensure the 'data' property is removed
      expect(result).to.not.have.property('data');
      // Check specific properties for correctness
      expect(result.userId).to.equal(samplePayload.userId);
      expect(result.exp).to.equal(extraProps.exp);
    });

    it('should work correctly when the input has only the "data" property', () => {
      const encryptedData = encryptData(samplePayload, validKey, validAlgo);
      const input = createTestInput(encryptedData, {}); // No extra props

      const result = JwtPayloadEncryption.decryptPayload(input);

      expect(result).to.deep.include(samplePayload);
      expect(result).to.not.have.property('data');
      expect(result).to.not.have.property('iat'); // Ensure no extra props were added
    });

    it('should correctly decrypt and preserve data types from the TokenPayload', () => {
      // This payload matches the strict TokenPayload interface
      const complexPayload: TokenPayload = {
        userId: 'user-123',
        email: '<EMAIL>',
        role: 'admin',
        system: false, // This is a boolean
        organizationId: 'org-456',
        permissions: ['read', 'write'], // This is an array of strings
        TOKEN_TYPE: 'access',
      };
      const encryptedData = encryptData(complexPayload, validKey, validAlgo);
      const input = createTestInput(encryptedData, {}); // Using empty object for extraProps is fine here

      const result = JwtPayloadEncryption.decryptPayload(input);

      expect(result).to.deep.include(complexPayload);

      // 2. Explicitly check types and values of fields actually in TokenPayload
      expect(result.userId).to.be.a('string').and.equal('user-123');
      expect(result.email).to.be.a('string').and.equal('<EMAIL>');
      expect(result.role).to.be.a('string').and.equal('admin');
      expect(result.system).to.be.a('boolean').and.equal(false); // Check boolean type and value
      expect(result.organizationId).to.be.a('string').and.equal('org-456');
      expect(result.permissions)
        .to.be.an('array')
        .and.deep.equal(['read', 'write']); // Check array type and deep equality
      expect(result.TOKEN_TYPE).to.be.a('string').and.equal('access');

      // 3. Ensure the 'data' property used for transport is removed
      expect(result).to.not.have.property('data');
    });

    // --- Environment Variable Edge Cases ---
    describe('Environment Variable Edge Cases', () => {
      it('should throw JsonWebTokenError if JWT_ENCRYPTION_KEY is missing', () => {
        delete process.env.JWT_ENCRYPTION_KEY;
        const encryptedData = encryptData(samplePayload, validKey, validAlgo); // Still need valid data
        const input = createTestInput(encryptedData);

        // Use a function wrapper for throw assertion
        const decryptFn = () => JwtPayloadEncryption.decryptPayload(input);
        expect(decryptFn).to.throw(JsonWebTokenError, 'Something went wrong');
      });
    });
    it('should throw JsonWebTokenError if JWT_ENCRYPTION_KEY is not valid base64', () => {
      process.env.JWT_ENCRYPTION_KEY = 'this-is-not-base64!';
      const encryptedData = encryptData(samplePayload, validKey, validAlgo);
      const input = createTestInput(encryptedData);

      const decryptFn = () => JwtPayloadEncryption.decryptPayload(input);
      expect(decryptFn).to.throw(JsonWebTokenError, 'Something went wrong');
    });

    it('should throw JsonWebTokenError if JWT_ENCRYPTION_KEY has incorrect length for the algorithm', () => {
      process.env.JWT_ENCRYPTION_KEY = crypto
        .randomBytes(16)
        .toString('base64'); // 16 bytes for aes-256-cbc is wrong
      const encryptedData = encryptData(samplePayload, validKey, validAlgo);
      const input = createTestInput(encryptedData);

      const decryptFn = () => JwtPayloadEncryption.decryptPayload(input);
      expect(decryptFn).to.throw(JsonWebTokenError, 'Something went wrong');
      // Note: The underlying crypto error might be 'Invalid key length'
    });

    it('should throw JsonWebTokenError if JWT_ENCRYPTION_ALGO is missing', () => {
      delete process.env.JWT_ENCRYPTION_ALGO;
      const encryptedData = encryptData(samplePayload, validKey, validAlgo); // Still need valid data
      const input = createTestInput(encryptedData);

      const decryptFn = () => JwtPayloadEncryption.decryptPayload(input);
      expect(decryptFn).to.throw(JsonWebTokenError, 'Something went wrong');
    });

    it('should throw JsonWebTokenError if JWT_ENCRYPTION_ALGO is invalid/unsupported', () => {
      process.env.JWT_ENCRYPTION_ALGO = 'invalid-algorithm-name';
      const encryptedData = encryptData(samplePayload, validKey, validAlgo); // Still need valid data
      const input = createTestInput(encryptedData);

      const decryptFn = () => JwtPayloadEncryption.decryptPayload(input);
      expect(decryptFn).to.throw(JsonWebTokenError, 'Something went wrong');
      // Note: The underlying crypto error might be 'Unknown cipher'
    });
  });

  // --- Input Data Edge Cases ---
  describe('Input Data Edge Cases', () => {
    it('should throw JsonWebTokenError if input data is missing', () => {
      const input = createTestInput(undefined); // data property is absent
      const decryptFn = () => JwtPayloadEncryption.decryptPayload(input);
      expect(decryptFn).to.throw(JsonWebTokenError, 'Something went wrong');
      // Underlying error: TypeError: Cannot read properties of undefined (reading 'split')
    });

    it('should throw JsonWebTokenError if input data is null', () => {
      const input = createTestInput(null); // data property is null
      const decryptFn = () => JwtPayloadEncryption.decryptPayload(input);
      expect(decryptFn).to.throw(JsonWebTokenError, 'Something went wrong');
      // Underlying error: TypeError: Cannot read properties of null (reading 'split')
    });

    it('should throw JsonWebTokenError if input data is not a string', () => {
      const input = createTestInput(12345); // data is a number
      const decryptFn = () => JwtPayloadEncryption.decryptPayload(input);
      expect(decryptFn).to.throw(JsonWebTokenError, 'Something went wrong');
      // Underlying error: TypeError: unencryptedPayload.data.split is not a function
    });

    it('should throw JsonWebTokenError if input data does not contain ":" separator', () => {
      const input = createTestInput('justsomedatawithoutseparator');
      const decryptFn = () => JwtPayloadEncryption.decryptPayload(input);
      expect(decryptFn).to.throw(JsonWebTokenError, 'Something went wrong');
      // Underlying error: RangeError from Buffer.from when mapping split result or index out of bounds
    });

    it('should throw JsonWebTokenError if input data has multiple ":" separators', () => {
      const input = createTestInput('iv:part1:part2');
      const decryptFn = () => JwtPayloadEncryption.decryptPayload(input);
      expect(decryptFn).to.throw(JsonWebTokenError, 'Something went wrong');
      // Underlying error: Destructuring assignment fails or Buffer.from error on third part
    });

    it('should throw JsonWebTokenError if IV part is not valid base64', () => {
      const encryptedPayload = encryptData(
        samplePayload,
        validKey,
        validAlgo
      ).split(':')[1];
      const input = createTestInput(`!!!invalid-iv!!!:${encryptedPayload}`);
      const decryptFn = () => JwtPayloadEncryption.decryptPayload(input);
      expect(decryptFn).to.throw(JsonWebTokenError, 'Something went wrong');
      // Underlying error: Error [ERR_INVALID_ARG_VALUE]: The argument 'input' is invalid. Received '!!!invalid-iv!!!' (from Buffer.from)
    });

    it('should throw JsonWebTokenError if encrypted data part is not valid base64', () => {
      const iv = encryptData(samplePayload, validKey, validAlgo).split(':')[0];
      const input = createTestInput(`${iv}:!!!invalid-encrypted-data!!!`);
      const decryptFn = () => JwtPayloadEncryption.decryptPayload(input);
      expect(decryptFn).to.throw(JsonWebTokenError, 'Something went wrong');
      // Underlying error: Error [ERR_INVALID_ARG_VALUE]: The argument 'input' is invalid. Received '!!!invalid-encrypted-data!!!' (from Buffer.from)
    });

    it('should throw JsonWebTokenError if IV length is incorrect for the algorithm (e.g., < 16 bytes for AES)', () => {
      const shortIv = crypto.randomBytes(8).toString('base64'); // Too short
      const encryptedData = encryptData(
        samplePayload,
        validKey,
        validAlgo
      ).split(':')[1];
      const input = createTestInput(`${shortIv}:${encryptedData}`);

      const decryptFn = () => JwtPayloadEncryption.decryptPayload(input);
      expect(decryptFn).to.throw(JsonWebTokenError, 'Something went wrong');
      // Note: The underlying crypto error might be 'Invalid IV length'
    });
  });

  // --- Decryption Edge Cases ---
  describe('Decryption Edge Cases', () => {
    it('should throw JsonWebTokenError if data was encrypted with a different key', () => {
      const encryptedData = encryptData(samplePayload, wrongKey, validAlgo); // Encrypt with wrong key
      const input = createTestInput(encryptedData); // Try decrypting with validKey (from process.env)

      const decryptFn = () => JwtPayloadEncryption.decryptPayload(input);
      expect(decryptFn).to.throw(JsonWebTokenError, 'Something went wrong');
      // Underlying error: Often 'error:06065064:digital envelope routines:EVP_DecryptFinal_ex:bad decrypt' from decipher.final()
    });

    it('should throw JsonWebTokenError if encrypted data is corrupted (tampered with)', () => {
      const encryptedData = encryptData(samplePayload, validKey, validAlgo);
      const parts = encryptedData.split(':');
      const corruptedEncrypted = parts[1].slice(0, -4) + 'xxxx'; // Change last few chars
      const input = createTestInput(`${parts[0]}:${corruptedEncrypted}`);

      const decryptFn = () => JwtPayloadEncryption.decryptPayload(input);
      expect(decryptFn).to.throw(JsonWebTokenError, 'Something went wrong');
      // Underlying error: Often 'error:06065064:digital envelope routines:EVP_DecryptFinal_ex:bad decrypt' from decipher.final()
    });

    it('should throw JsonWebTokenError if the decrypted data is not valid JSON', () => {
      const nonJsonString = 'This is just plain text, not JSON.';
      // Manually encrypt non-JSON string using the same method
      const iv = crypto.randomBytes(16);
      const cipher = crypto.createCipheriv(
        validAlgo,
        Buffer.from(validKey, 'base64'),
        iv
      );
      const encryptedNonJson =
        cipher.update(nonJsonString, 'utf8', 'base64') + cipher.final('base64');
      const encryptedData = `${iv.toString('base64')}:${encryptedNonJson}`;
      const input = createTestInput(encryptedData);

      const decryptFn = () => JwtPayloadEncryption.decryptPayload(input);
      expect(decryptFn).to.throw(JsonWebTokenError, 'Something went wrong');
      // Underlying error: SyntaxError: Unexpected token ... in JSON at position ... from JSON.parse()
    });
  });

  // --- Input Object Edge Cases ---
  describe('Input Object Edge Cases', () => {
    // Note: TypeScript should prevent these at compile time if types are used correctly,
    // but JavaScript callers might still pass invalid types.

    it('should throw an error (likely TypeError) if input is null', () => {
      // Use function wrapper for throw assertion
      const decryptFn = () => JwtPayloadEncryption.decryptPayload(null as any);
      // This will likely throw TypeError before specific JsonWebTokenError is caught
      expect(decryptFn).to.throw(JsonWebTokenError);
      // Example: Cannot read properties of null (reading 'data')
    });

    it('should throw an error (likely TypeError) if input is undefined', () => {
      const decryptFn = () =>
        JwtPayloadEncryption.decryptPayload(undefined as any);
      expect(decryptFn).to.throw(JsonWebTokenError);
      // Example: Cannot read properties of undefined (reading 'data')
    });

    it('should throw an error (likely TypeError) if input is not an object', () => {
      const decryptFn = () =>
        JwtPayloadEncryption.decryptPayload('not an object' as any);
      expect(decryptFn).to.throw(JsonWebTokenError);
      // Example: Cannot read properties of undefined (reading 'split') if data is missing
      // Or: unencryptedPayload.data.split is not a function if data exists but input isn't object
    });
  });
});
