import { expect } from 'chai';
import { sanitizeURL } from '../../src/helpers/urlUtils';

describe('urlUtils', () => {
  describe('sanitizeURL', () => {
    it('should return null for null input', () => {
      expect(sanitizeURL(null)).to.be.null;
    });

    it('should handle empty string', () => {
      expect(sanitizeURL('')).to.equal('');
    });

    it('should encode pipe characters', () => {
      const url = 'https://example.com/path|with|pipes';
      expect(sanitizeURL(url)).to.equal(
        'https://example.com/path%7Cwith%7Cpipes'
      );
    });

    it('should encode spaces', () => {
      const url = 'https://example.com/path with spaces';
      expect(sanitizeURL(url)).to.equal(
        'https://example.com/path%20with%20spaces'
      );
    });

    it('should encode special characters', () => {
      const url = 'https://example.com/path?name=John&title=Software Engineer';
      expect(sanitizeURL(url)).to.equal(
        'https://example.com/path?name=John&title=Software%20Engineer'
      );
    });

    it('should handle URLs with query parameters and fragments', () => {
      const url = 'https://example.com/path?param=value#fragment';
      expect(sanitizeURL(url)).to.equal(
        'https://example.com/path?param=value#fragment'
      );
    });

    it('should handle URLs with international characters', () => {
      const url = 'https://example.com/über/café';
      expect(sanitizeURL(url)).to.equal(
        'https://example.com/%C3%BCber/caf%C3%A9'
      );
    });

    it('should handle URLs with multiple special characters', () => {
      const url =
        'https://example.com/path|with|pipes and spaces+special@chars';
      const expected =
        'https://example.com/path%7Cwith%7Cpipes%20and%20spaces+special@chars';
      expect(sanitizeURL(url)).to.equal(expected);
    });

    it('should preserve valid URL characters', () => {
      const url = 'https://example.com/valid-path_with.normal~characters';
      expect(sanitizeURL(url)).to.equal(url);
    });

    it('should handle file paths', () => {
      const url = '/path/to/file|version1.jpg';
      expect(sanitizeURL(url)).to.equal('/path/to/file%7Cversion1.jpg');
    });

    it('should handle S3-style URLs', () => {
      const url = 'https://s3.amazonaws.com/bucket/file|v1.jpg';
      expect(sanitizeURL(url)).to.equal(
        'https://s3.amazonaws.com/bucket/file%7Cv1.jpg'
      );
    });

    it('should handle CloudFront URLs', () => {
      const url =
        'https://d111111abcdef8.cloudfront.net/images/file|latest.png';
      expect(sanitizeURL(url)).to.equal(
        'https://d111111abcdef8.cloudfront.net/images/file%7Clatest.png'
      );
    });

    it('should return null for invalid URLs with unmatched brackets', () => {
      const url = 'https://example.com/path[unmatched';
      expect(sanitizeURL(url)).to.equal('https://example.com/path%5Bunmatched');
    });
  });
});
