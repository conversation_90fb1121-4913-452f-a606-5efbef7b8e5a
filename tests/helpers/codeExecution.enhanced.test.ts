import { expect } from 'chai';

import {
  generateRunnerCode,
  prepareAndProcessTestCases,
  createSourceCode,
} from '../../src/helpers/codeExecution';
import { AppError } from '../../src/middlewares/errorHandler';
import { TestCase } from '../../src/helpers/types';

describe('Enhanced CodeExecution Security Tests', () => {
  describe('Input Validation', () => {
    it('should reject dangerous code patterns', async () => {
      const dangerousCodes = [
        'require("fs")',
        'import("fs")',
        'fetch("http://evil.com")',
        'document.createElement("script")',
        'window.location = "http://evil.com"',
        'global.process',
      ];

      for (const code of dangerousCodes) {
        try {
          generateRunnerCode(63, code, '[]');
          expect.fail(`Should have rejected dangerous code: ${code}`);
        } catch (error) {
          expect(error).to.be.instanceOf(AppError);
          expect(error.message).to.include('dangerous operations');
        }
      }
    });

    it('should reject oversized code', () => {
      const largeCode = 'a'.repeat(60000);

      try {
        generateRunnerCode(63, largeCode, '[]');
        expect.fail('Should have rejected oversized code');
      } catch (error) {
        expect(error).to.be.instanceOf(AppError);
        expect(error.message).to.include('maximum length');
      }
    });

    it('should reject unsupported languages', () => {
      try {
        generateRunnerCode(999, 'console.log("test")', '[]');
        expect.fail('Should have rejected unsupported language');
      } catch (error) {
        expect(error).to.be.instanceOf(AppError);
        expect(error.message).to.include('Unsupported language_id');
      }
    });

    it('should validate test case structure', async () => {
      const invalidTestCases = [
        { input: 'test' }, // missing output and test_case_id
        { output: 'result' }, // missing input and test_case_id
        { test_case_id: '1' }, // missing input and output
      ];

      try {
        await prepareAndProcessTestCases(
          'taker1',
          'q1',
          'function test() { return "ok"; }',
          63,
          invalidTestCases as unknown as TestCase[]
        );
        expect.fail('Should have rejected invalid test cases');
      } catch (error) {
        expect(error).to.be.instanceOf(AppError);
        expect(error.message).to.include('missing required fields');
      }
    });
  });

  describe('Enhanced Function Detection', () => {
    const testCases = [
      {
        name: 'function declaration',
        code: 'function add(a, b) { return a + b; }',
        input: '1, 2',
        expected: `
    const result = add(1, 2);
    if (typeof result === 'string') {
      console.log(result);
    } else {
      console.log(JSON.stringify(result));
    }
  `,
      },
      {
        name: 'arrow function',
        code: 'const multiply = (a, b) => a * b;',
        input: '3, 4',
        expected: `
    const result = multiply(3, 4);
    if (typeof result === 'string') {
      console.log(result);
    } else {
      console.log(JSON.stringify(result));
    }
  `,
      },
      {
        name: 'async function',
        code: 'async function fetchData() { return "data"; }',
        input: '',
        expected: `
    const result = fetchData();
    if (typeof result === 'string') {
      console.log(result);
    } else {
      console.log(JSON.stringify(result));
    }
  `,
      },

      {
        name: 'class instance method',
        code: 'class Calculator { add(a, b) { return a + b; } }',
        input: '7, 8',
        expected: `
        const result = new Calculator().add(7, 8);
        if (typeof result === 'string') {
          console.log(result);
        } else {
          console.log(JSON.stringify(result));
        }
      `,
      },
    ];

    testCases.forEach(({ name, code, input, expected }) => {
      it(`should detect ${name}`, () => {
        const result = generateRunnerCode(63, code, input);
        expect(result).to.equal(expected);
      });
    });
  });

  describe('createSourceCode', () => {
    it('should create TypeScript source code with references for language_id 74', () => {
      const code =
        'function add(a: number, b: number): number { return a + b; }';
      const runnerCode = 'console.log(add(1, 2));';
      const language_id = 74;

      const result = createSourceCode(code, runnerCode, language_id);

      expect(result).to.include('/// <reference lib="es2015" />');
      expect(result).to.include('/// <reference lib="es2016" />');
      expect(result).to.include('/// <reference lib="es2017" />');
      expect(result).to.include('/// <reference lib="dom" />');
      expect(result).to.include(code);
      expect(result).to.include(runnerCode);
    });

    it('should create JavaScript source code without references for language_id 63', () => {
      const code = 'function add(a, b) { return a + b; }';
      const runnerCode = 'console.log(add(1, 2));';
      const language_id = 63;

      const result = createSourceCode(code, runnerCode, language_id);

      expect(result).to.not.include('/// <reference');
      expect(result).to.include(code);
      expect(result).to.include(runnerCode);
      expect(result).to.equal(`${code}\n${runnerCode}`);
    });

    it('should handle empty code and runner code', () => {
      const code = '';
      const runnerCode = '';
      const language_id = 63;

      const result = createSourceCode(code, runnerCode, language_id);

      expect(result).to.equal('\n');
    });
  });

  // Note: Validation functions are internal and not exported, so we test them through the public API
});
