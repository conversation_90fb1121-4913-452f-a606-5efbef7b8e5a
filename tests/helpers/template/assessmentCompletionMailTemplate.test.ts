import { expect } from 'chai';
import sinon from 'sinon';
import proxyquire from 'proxyquire';

describe('Assessment Completion Templates', () => {
  let sessionTable, resultSectionTemplate, assessmentCompletionTemplate;
  let timeFormatStub;

  before(() => {
    // Create a stub for the timeFormat function
    timeFormatStub = sinon.stub();
    timeFormatStub.callsFake((seconds) => {
      const hours = Math.floor(seconds / 3600);
      const minutes = Math.floor((seconds % 3600) / 60);
      const remainingSeconds = seconds % 60;
      return `${String(hours).padStart(2, '0')}:${String(minutes).padStart(
        2,
        '0'
      )}:${String(remainingSeconds).padStart(2, '0')}`;
    });

    // Use proxyquire to inject the stub
    const templateModule = proxyquire(
      '../../../src/helpers/template/assessmentCompletionMailTemplate',
      {
        '../timeFormat': { timeFormat: timeFormatStub },
      }
    );

    // Get the exported functions
    sessionTable = templateModule.sessionTable;
    resultSectionTemplate = templateModule.resultSectionTemplate;
    assessmentCompletionTemplate = templateModule.assessmentCompletionTemplate;
  });

  afterEach(() => {
    sinon.resetHistory();
  });

  after(() => {
    sinon.restore();
  });

  describe('sessionTable', () => {
    const testResult = {
      title: 'Math Test',
      duration: 3600,
      numberOfQuestions: 20,
      numberOfQuestionsAnswered: 18,
      totalPassedScore: 15,
      totalScore: 20,
      testPercentage: 75,
    };

    it('should generate a table with correct test result data', () => {
      const result = sessionTable(testResult);

      expect(result).to.include(testResult.title);
      expect(timeFormatStub.calledWith(testResult.duration)).to.be.true;
      expect(result).to.include(testResult.numberOfQuestions.toString());
      expect(result).to.include(
        testResult.numberOfQuestionsAnswered.toString()
      );
      expect(result).to.include(
        `${testResult.totalPassedScore}/${testResult.totalScore}`
      );
      expect(result).to.include(`${testResult.testPercentage}%`);
    });

    it('should include proper table styling', () => {
      const result = sessionTable(testResult);

      expect(result).to.include('border-collapse: collapse');
      expect(result).to.include('background-color: #eaeaea65');
      expect(result).to.include('width: 100%');
      expect(result).to.include('table-layout: fixed');
    });
  });

  describe('resultSectionTemplate', () => {
    const dataResults = {
      testTakerDurationSeconds: 7200,
      overallAssessmentPercentage: 80,
      testResults: [
        {
          title: 'Math Section',
          duration: 3600,
          numberOfQuestions: 10,
          numberOfQuestionsAnswered: 9,
          totalPassedScore: 8,
          totalScore: 10,
          testPercentage: 80,
        },
        {
          title: 'English Section',
          duration: 3600,
          numberOfQuestions: 10,
          numberOfQuestionsAnswered: 10,
          totalPassedScore: 8,
          totalScore: 10,
          testPercentage: 80,
        },
      ],
    };

    it('should generate result section with all data', () => {
      const result = resultSectionTemplate(dataResults);

      expect(result).to.include('Result is as follows:');
      expect(result).to.include(
        `Overall Assessment Percentage: ${dataResults.overallAssessmentPercentage}%`
      );
      expect(timeFormatStub.calledWith(dataResults.testTakerDurationSeconds)).to
        .be.true;

      // Check for both test results
      dataResults.testResults.forEach((testResult) => {
        expect(result).to.include(testResult.title);
      });
    });

    it('should include proper styling for text elements', () => {
      const result = resultSectionTemplate(dataResults);

      expect(result).to.include("font-family: 'Work Sans', sans-serif");
      expect(result).to.include('font-size: 16px');
      expect(result).to.include('line-height: 24px');
      expect(result).to.include('color: #474d66');
    });
  });

  describe('assessmentCompletionTemplate', () => {
    const dataResults = {
      showResults: true,
      testTakerDurationSeconds: 7200,
      overallAssessmentPercentage: 80,
      testResults: [
        {
          title: 'Math Section',
          duration: 3600,
          numberOfQuestions: 10,
          numberOfQuestionsAnswered: 9,
          totalPassedScore: 8,
          totalScore: 10,
          testPercentage: 80,
        },
      ],
    };

    it('should generate complete email template when showResults is true', () => {
      const result = assessmentCompletionTemplate(dataResults);

      // Check basic structure
      expect(result).to.include('<!DOCTYPE html>');
      expect(result).to.include('<html lang="en">');
      expect(result).to.include('</html>');

      // Check content
      expect(result).to.include('Dear Applicant,');
      expect(result).to.include(
        'Congratulations on completing the assessment.'
      );
      expect(result).to.include('Best Regards,');

      // Check if results section is included
      expect(result).to.include('Result is as follows:');
    });

    it('should not include results section when showResults is false', () => {
      const modifiedData = { ...dataResults, showResults: false };
      const result = assessmentCompletionTemplate(modifiedData);

      expect(result).not.to.include('Result is as follows:');
      expect(result).to.include(
        'Congratulations on completing the assessment.'
      );
    });

    it('should include proper styling and layout', () => {
      const result = assessmentCompletionTemplate(dataResults);

      // Check main container styling
      expect(result).to.include('background-color: #0C4767');
      expect(result).to.include('max-width: 600px');
      expect(result).to.include('border-radius: 8px');

      // Check content styling
      expect(result).to.include("font-family: 'Work Sans', sans-serif");
    });

    it('should include the logo image', () => {
      const result = assessmentCompletionTemplate(dataResults);

      expect(result).to.include('<img');
      expect(result).to.include(
        'src="https://imocha-media-files.s3.eu-west-1.amazonaws.com/application/amap-logo.png"'
      );
      expect(result).to.include('alt="Amap-logo"');
    });

    it('should include retake information when retake delay is configured', () => {
      const dataWithRetake = {
        ...dataResults,
        retakeInfo: {
          hasRetakeDelay: true,
          retakeDelayHours: 24,
          nextRetakeDate: '2024-01-02T10:00:00.000Z',
          nextRetakeDateReadable: 'Tuesday, January 2, 2024 at 10:00 AM GMT',
        },
      };

      const result = assessmentCompletionTemplate(dataWithRetake);

      expect(result).to.include('Assessment Retake Information');
      expect(result).to.include('24-hour waiting period');
      expect(result).to.include('Next retake available:');
      expect(result).to.include('Tuesday, January 2, 2024 at 10:00 AM GMT');
    });

    it('should not include retake information when no retake delay is configured', () => {
      const dataWithoutRetake = {
        ...dataResults,
        retakeInfo: {
          hasRetakeDelay: false,
        },
      };

      const result = assessmentCompletionTemplate(dataWithoutRetake);

      expect(result).to.not.include('Assessment Retake Information');
      expect(result).to.not.include('waiting period');
      expect(result).to.not.include('Next retake available:');
    });
  });
});
