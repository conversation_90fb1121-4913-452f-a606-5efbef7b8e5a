import { expect } from 'chai';
import Sinon from 'sinon';
import proxyquire from 'proxyquire';
import {
  assessmentTakerDependencyMockData,
  assessmentTakerDependencyMockMarkData,
  fakeAssessmentTaker,
  testCreationCandidateMockData,
  testCreationCandidateMockMarkData,
} from '../fakeData/mockresponse';
import { SubmissionType } from '../../src/helpers/types';
import { createPrismaStub } from '../fakeData/mockPayload';
import { AssessmentTakerRepository } from '../../src/repository/assessmentTakerRepository';

describe('Assessment Report', () => {
  let compileAssessmentStub;
  let assessmentSubmitPublishStub;
  let templateStub;
  let mailStub;
  let deleteCacheStub;
  let AssessmentReport;
  let getDependencyMarkStub;
  let prismaMock = createPrismaStub();
  let getDependencyDataStub;
  let markDraftedQuestionsStub;
  let getUncompletedStub;
  let assessmentTakerRepositoryStub;
  let kafkaStub;
  let assessmentProcessStub;
  let sendMailStub;
  let redisCacheStub;
  let emailTemplateStub;
  let sentryStub;
  let redisClientStub;

  beforeEach(() => {
    sentryStub = {
      captureException: Sinon.stub().resolves(),
      captureMessage: Sinon.stub().resolves(),
      init: Sinon.stub().resolves(),
      setupExpressErrorHandler: Sinon.stub().resolves(),
    };

    redisClientStub = {
      get: Sinon.stub().resolves(null),
      set: Sinon.stub().resolves('OK'),
      del: Sinon.stub().resolves(1),
      quit: Sinon.stub().resolves(),
      connect: Sinon.stub().resolves(),
      disconnect: Sinon.stub().resolves(),

      on: Sinon.stub().callsFake(function (event, callback) {
        if (event === 'ready') {
          setTimeout(() => callback(), 0);
        }
        return this;
      }),
      once: Sinon.stub().callsFake(function (event, callback) {
        if (event === 'ready') {
          setTimeout(() => callback(), 0);
        }
        return this;
      }),
      off: Sinon.stub().returns({}),

      emit: Sinon.stub().returns(true),
    };

    deleteCacheStub = Sinon.stub().resolves(undefined);
    getDependencyMarkStub = Sinon.stub().resolves(
      assessmentTakerDependencyMockMarkData
    );
    getDependencyDataStub = Sinon.stub().resolves(
      assessmentTakerDependencyMockData
    );

    redisCacheStub = {
      deleteCache: deleteCacheStub,
      getAssessmentTakerDependencyDataMark: getDependencyMarkStub,
      getAssessmentTakerDependencyData: getDependencyDataStub,
      redisClient: redisClientStub,
    };

    const mockIoRedis = Sinon.stub().returns(redisClientStub);

    // Add Cluster constructor to the factory function
    // mockIoRedis.Cluster = Sinon.stub().returns(redisClientStub);

    compileAssessmentStub = Sinon.stub().resolves({
      id: 'fake-id',
      email: '<EMAIL>',
      status: 'COMPLETED',
      phase: 'ASSESSMENT_COMPLETION',
    });

    assessmentSubmitPublishStub = Sinon.stub().resolves(undefined);
    templateStub = Sinon.stub().returns('email template');
    mailStub = Sinon.stub().resolves(undefined);
    deleteCacheStub = Sinon.stub().resolves(undefined);
    getDependencyMarkStub = Sinon.stub().resolves(
      assessmentTakerDependencyMockMarkData
    );
    getDependencyDataStub = Sinon.stub().resolves(
      assessmentTakerDependencyMockData
    );
    markDraftedQuestionsStub = Sinon.stub().resolves(undefined);
    getUncompletedStub = Sinon.stub().resolves([]);

    prismaMock = createPrismaStub();
    prismaMock.assessmentTaker.update.resolves({
      id: 'fake-id',
      email: '<EMAIL>',
      status: 'COMPLETED',
      phase: 'ASSESSMENT_COMPLETION',
      logSummary: [],
      logs: [],
    });

    assessmentProcessStub = {
      AssessmentProcess: {
        compileAssessment: compileAssessmentStub,
        markDraftedQuestions: markDraftedQuestionsStub,
      },
    };

    kafkaStub = {
      assessmentSubmitPublish: assessmentSubmitPublishStub,
    };

    emailTemplateStub = {
      assessmentCompletionTemplate: templateStub,
    };

    sendMailStub = {
      sendMail: mailStub,
    };

    assessmentTakerRepositoryStub = {
      getUncompletedAssessmentTakers: getUncompletedStub,
    };

    const ReportModule = proxyquire.load('../../src/helpers/assessmentReport', {
      './assessmentProcess': assessmentProcessStub,
      './kafkaPublishMessage': kafkaStub,
      './template/assessmentCompletionMailTemplate': emailTemplateStub,
      './sendmail': sendMailStub,
      '../prisma': { default: prismaMock },
      './redisCache': redisCacheStub,
      '../repository/assessmentTakerRepository': assessmentTakerRepositoryStub,
      '@sentry/node': sentryStub,
      ioredis: mockIoRedis,
      '@noCallThru': true,
    });

    AssessmentReport = ReportModule.AssessmentReport;

    // Immediately emit 'ready' event to simulate successful connection
    redisClientStub.emit('ready');
  });

  afterEach(() => {
    Sinon.restore();
    if (redisClientStub.quit.called) {
      redisClientStub.quit();
    }
    redisClientStub = null;
  });

  describe('sendAssessmentReport', () => {
    it('Should publish assessment report', async () => {
      const testData = {
        ...fakeAssessmentTaker,
        id: 'fake-id',
        email: '<EMAIL>',
        status: 'IN_PROGRESS',
        phase: 'ASSESSMENT_IN_PROGRESS',
      };

      await AssessmentReport.sendAssessmentReport(
        testData,
        testCreationCandidateMockData,
        testCreationCandidateMockMarkData.assessment.tests,
        SubmissionType.cadidate
      );

      expect(compileAssessmentStub.calledOnce).to.be.true;
      expect(assessmentSubmitPublishStub.calledOnce).to.be.true;
      expect(templateStub.calledOnce).to.be.true;
      expect(mailStub.calledOnce).to.be.true;
      expect(prismaMock.assessmentTaker.update.calledOnce).to.be.true;

      const updateCall = prismaMock.assessmentTaker.update.getCall(0);
      expect(updateCall.args[0].where).to.deep.equal({ id: 'fake-id' });
      expect(updateCall.args[0].data.status).to.equal('COMPLETED');
      expect(updateCall.args[0].data.phase).to.equal('ASSESSMENT_COMPLETION');
      expect(updateCall.args[0].data.logSummary).to.deep.equal({
        push: 'ASSESSMENT_COMPLETION',
      });
      expect(updateCall.args[0].data.logs.create.action).to.equal(
        'ASSESSMENT_COMPLETION'
      );
      expect(updateCall.args[0].data.logs.create.createdAt).to.be.instanceOf(
        Date
      );
    });
  });

  describe('autoSendAssessmentReportSubmission', () => {
    it('Should auto submit assessment report', async () => {
      // Reset and recreate all the stubs to ensure they're fresh
      getDependencyMarkStub = Sinon.stub().resolves(
        assessmentTakerDependencyMockMarkData
      );
      getDependencyDataStub = Sinon.stub().resolves(
        assessmentTakerDependencyMockData
      );

      // Create the redis cache stub with our fresh stubs
      redisCacheStub = {
        deleteCache: deleteCacheStub,
        getAssessmentTakerDependencyDataMark: getDependencyMarkStub,
        getAssessmentTakerDependencyData: getDependencyDataStub,
        redisClient: redisClientStub,
      };

      // First get the module with proxyquire
      const ReportModule = proxyquire('../../src/helpers/assessmentReport', {
        './assessmentProcess': assessmentProcessStub,
        './kafkaPublishMessage': kafkaStub,
        './template/assessmentCompletionMailTemplate': emailTemplateStub,
        './sendmail': sendMailStub,
        '../prisma': { default: prismaMock },
        './redisCache': redisCacheStub, // Use our fresh redis cache stub
        '../repository/assessmentTakerRepository':
          assessmentTakerRepositoryStub,
        '@noCallThru': true,
      });

      // Now stub the sendAssessmentReport method of the AssessmentReport class
      const sendReportStub = Sinon.stub(
        ReportModule.AssessmentReport,
        'sendAssessmentReport'
      ).resolves();

      // Call the method
      await ReportModule.AssessmentReport.autoSendAssessmentReportSubmission(
        fakeAssessmentTaker
      );

      // Use a less strict verification that doesn't depend on exact parameter matching
      expect(getDependencyMarkStub.called).to.be.true;
      expect(getDependencyDataStub.called).to.be.true;
      expect(markDraftedQuestionsStub.called).to.be.true;
      expect(sendReportStub.called).to.be.true;

      // Restore the stub
      sendReportStub.restore();
    });
  });

  describe('reportUnSubmittedAssessment', () => {
    let originalAutoSendAssessmentReportSubmission;
    let autoSendStub;
    let getUncompletedStub;

    beforeEach(() => {
      // Save the original method
      originalAutoSendAssessmentReportSubmission =
        AssessmentReport.autoSendAssessmentReportSubmission;

      // Replace it with our stub
      autoSendStub = Sinon.stub().resolves();
      AssessmentReport.autoSendAssessmentReportSubmission = autoSendStub;

      // Stub the repository method
      getUncompletedStub = Sinon.stub(
        AssessmentTakerRepository,
        'getUncompletedAssessmentTaker'
      ).resolves([
        {
          ...fakeAssessmentTaker,
          estimatedEndTime: new Date(Date.now() - 6 * 60000), // 6 minutes ago (past the 5 min extension)
        },
      ]);
    });

    afterEach(() => {
      // Restore the original method
      AssessmentReport.autoSendAssessmentReportSubmission =
        originalAutoSendAssessmentReportSubmission;

      // Restore all stubs
      Sinon.restore();
    });

    it('Should report unsubmitted assessment', async () => {
      // Call the method
      await AssessmentReport.reportUnSubmittedAssessment();

      // Verify
      expect(autoSendStub.calledOnce).to.be.true;
      expect(getUncompletedStub.calledOnce).to.be.true;
    });
  });
});
