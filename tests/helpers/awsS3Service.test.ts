import { expect } from 'chai';
import sinon from 'sinon';
import s3Service from '../../src/helpers/awss3Service';
import { PresignedURLMimeType } from '../../src/helpers/types';
describe('S3Service', () => {
  let s3GetSignedUrlStub: sinon.SinonStub;
  before(() => {
    s3GetSignedUrlStub = sinon.stub().resolves('https://mock-s3-url.com');
    sinon.replace(s3Service['s3'], 'getSignedUrlPromise', s3GetSignedUrlStub);
  });

  after(() => {
    sinon.restore();
  });

  describe('getPreSignedURL', () => {
    it('should return a presigned URL for valid input with headers', async () => {
      const dateStub = sinon
        .stub(Date.prototype, 'toISOString')
        .returns('2023-01-01T00:00:00.000Z');
      const key = 'test-file.png';
      const contentType: PresignedURLMimeType = 'image/png';
      const contentMD5 = 'mock-content-md5';
      const headersMock = {
        'Content-Type': contentType,
        'Content-MD5': contentMD5,
        'x-amz-object-lock-mode': 'GOVERNANCE',
        'x-amz-object-lock-retain-until-date': '2023-01-01T00:00:00.000Z',
      };
      const result = await s3Service.getPreSignedURL(
        key,
        contentType,
        contentMD5
      );

      expect(result).to.deep.equal({
        url: 'https://mock-s3-url.com',
        headers: headersMock,
      });

      dateStub.restore();
    });

    it('should call getSignedUrl with correct parameters', async () => {
      const key = 'test-file.png';
      const contentType: PresignedURLMimeType = 'image/png';
      const contentMD5 = 'mock-content-md5';

      await s3Service.getPreSignedURL(key, contentType, contentMD5);

      expect(s3GetSignedUrlStub.called).to.be.true;
      const callArgs = s3GetSignedUrlStub.getCall(0).args[1];
      expect(callArgs.Bucket).to.equal(process.env.AWS_BUCKET_NAME);
      expect(callArgs.Key).to.equal(key);
      expect(callArgs.ContentType).to.equal(contentType);
      expect(callArgs.Expires).to.equal(60);
    });

    it('should throw an error if getSignedUrl fails', async () => {
      const errorMessage = 'S3 error';

      try {
        await s3Service.getPreSignedURL(
          'test-file.png',
          'image/png',
          'mock-content-md5'
        );
      } catch (error) {
        expect(error.message).to.equal(errorMessage);
      }
    });
  });
});
