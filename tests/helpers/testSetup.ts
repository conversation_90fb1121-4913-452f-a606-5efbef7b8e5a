import sinon from 'sinon';
import KafkaPublisher from '../../src/helpers/kafkaPublisher';
import Redis from 'ioredis';
// import KafkaConsumer from "../../src/helpers/kafkaConsumer";
import { sentryMock } from '../../tests/fakeData/mockPayload';

// Store original environment variables
const originalEnv = { ...process.env };

// Create shared Redis stubs that can be accessed by other tests
export const sharedRedisStubs = {
  get: sinon.stub(),
  set: sinon.stub(),
  del: sinon.stub(),
  keys: sinon.stub(),
};

// Set required environment variables for tests
export const setupTestEnv = () => {
  process.env.KAFKA_BOOTSTRAP_SERVERS = 'localhost:9092';
  process.env.NODE_ENV = 'test';
  process.env.DBUSER = 'test_user';
  process.env.DBPASSWORD = 'test_password';
  process.env.DBNAME_TEST = 'test_db';
  process.env.DBHOST = 'localhost';
  process.env.SYSTEM_INDEX = 'test-system';
};

export const setupTestStubs = () => {
  // Mock Kafka Publisher
  sinon.stub(KafkaPublisher.prototype, 'connect').resolves();
  sinon.stub(KafkaPublisher.prototype, 'disconnect').resolves();
  sinon.stub(KafkaPublisher.prototype, 'publish').resolves();

  // Mock Redis with shared stubs
  sinon.stub(Redis.prototype, 'get').callsFake(sharedRedisStubs.get);
  sinon.stub(Redis.prototype, 'set').callsFake(sharedRedisStubs.set);
  sinon.stub(Redis.prototype, 'del').callsFake(sharedRedisStubs.del);
  sinon.stub(Redis.prototype, 'keys').callsFake(sharedRedisStubs.keys);

  // Replace Sentry module with mock
  require.cache[require.resolve('@sentry/node')] = {
    exports: sentryMock,
  } as NodeModule;
};

export const resetStubs = () => {
  // Reset all stubs
  sinon.resetHistory();

  // Reset Redis stubs
  Object.values(sharedRedisStubs).forEach((stub) => stub.reset());
};

export const restoreStubs = () => {
  // Restore environment variables
  process.env = originalEnv;

  // Restore all stubs
  sinon.restore();

  // Clear Sentry mock from require cache
  delete require.cache[require.resolve('@sentry/node')];
};
