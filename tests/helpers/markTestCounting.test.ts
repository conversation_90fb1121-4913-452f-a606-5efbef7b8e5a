import { expect } from 'chai';
import Sinon from 'sinon';
import { AssessmentProcess } from '../../src/helpers/assessmentProcess';
import { MarkQuestion } from '../../src/helpers/markQuestion';
import { Question } from '../../src/dtos/assessmentTakerDtos';
import { AnswerStatus, QuestionResult } from '../../src/helpers/types';

describe('AssessmentProcess - Question Counting Logic', () => {
  let markStub: Sinon.SinonStub;

  beforeEach(() => {
    markStub = Sinon.stub(MarkQuestion, 'mark');
  });

  afterEach(() => {
    Sinon.restore();
  });

  const createMockQuestion = (id: string, score: number): Question =>
    ({
      id,
      score,
      questionType: 'Multiple_choice',
      questionText: `Question ${id}`,
      multipleChoiceAnswer: { answer: ['Option A'] },
    } as Question);

  const createMockSubmittedQuestion = (
    questionId: string,
    answers: string[] = ['Answer']
  ): QuestionResult => ({
    questionId,
    questionType: 'Multiple_choice',
    testTakerAnswers: answers,
    idleTime: 0,
  });

  describe('markTest - Question Counting', () => {
    it('Should correctly count all questions when all are submitted and answered', async () => {
      // Setup: 3 questions, all submitted with different outcomes
      const assignedQuestions = [
        createMockQuestion('q1', 10),
        createMockQuestion('q2', 5),
        createMockQuestion('q3', 15),
      ];
      const markQuestions = assignedQuestions;
      const submittedQuestions = [
        createMockSubmittedQuestion('q1', ['Correct Answer']),
        createMockSubmittedQuestion('q2', ['Wrong Answer']),
        createMockSubmittedQuestion('q3', ['Correct Answer']),
      ];

      // Mock MarkQuestion.mark responses
      markStub
        .withArgs(Sinon.match.any, 'q1', Sinon.match.any, ['Correct Answer'])
        .resolves({
          answerStatus: AnswerStatus.CORRECT,
          scored: 10,
          isAnswered: true,
          testTakerAnswers: ['Correct Answer'],
        });

      markStub
        .withArgs(Sinon.match.any, 'q2', Sinon.match.any, ['Wrong Answer'])
        .resolves({
          answerStatus: AnswerStatus.WRONG,
          scored: 0,
          isAnswered: true,
          testTakerAnswers: ['Wrong Answer'],
        });

      markStub
        .withArgs(Sinon.match.any, 'q3', Sinon.match.any, ['Correct Answer'])
        .resolves({
          answerStatus: AnswerStatus.CORRECT,
          scored: 15,
          isAnswered: true,
          testTakerAnswers: ['Correct Answer'],
        });

      const result = await AssessmentProcess.markTest({
        assessmentTakerId: 'test-taker-1',
        assignedQuestionsData: assignedQuestions,
        markQuestionsData: markQuestions,
        submitedQuestionsData: submittedQuestions,
      });

      // Verify counts
      expect(result.numberOfQuestionsPassed).to.equal(2); // q1, q3
      expect(result.numberOfQuestionsFailed).to.equal(1); // q2
      expect(result.numberOfQuestionsSkipped).to.equal(0); // none
      expect(result.numberOfQuestionsAnswered).to.equal(3); // all answered
      expect(result.totalQuestions).to.equal(3);

      // Verify totals add up
      const totalCounted =
        result.numberOfQuestionsPassed +
        result.numberOfQuestionsFailed +
        result.numberOfQuestionsSkipped;
      expect(totalCounted).to.equal(result.totalQuestions);

      // Verify scores
      expect(result.totalScore).to.equal(30); // 10 + 5 + 15
      expect(result.totalPassedScore).to.equal(25); // 10 + 0 + 15
    });

    it('Should correctly count questions when some are not submitted (skipped)', async () => {
      // Setup: Only 2 questions submitted (the method now only processes submitted questions)
      const assignedQuestions = [
        createMockQuestion('q1', 10),
        createMockQuestion('q2', 5),
        createMockQuestion('q3', 15),
        createMockQuestion('q4', 8),
      ];
      const markQuestions = assignedQuestions;
      const submittedQuestions = [
        createMockSubmittedQuestion('q1', ['Correct Answer']),
        createMockSubmittedQuestion('q3', ['Wrong Answer']),
        // q2 and q4 not submitted - these won't be processed by markTest anymore
      ];

      // Mock responses for submitted questions
      markStub
        .withArgs(Sinon.match.any, 'q1', Sinon.match.any, ['Correct Answer'])
        .resolves({
          answerStatus: AnswerStatus.CORRECT,
          scored: 10,
          isAnswered: true,
          testTakerAnswers: ['Correct Answer'],
        });

      markStub
        .withArgs(Sinon.match.any, 'q3', Sinon.match.any, ['Wrong Answer'])
        .resolves({
          answerStatus: AnswerStatus.WRONG,
          scored: 0,
          isAnswered: true,
          testTakerAnswers: ['Wrong Answer'],
        });

      const result = await AssessmentProcess.markTest({
        assessmentTakerId: 'test-taker-1',
        assignedQuestionsData: assignedQuestions,
        markQuestionsData: markQuestions,
        submitedQuestionsData: submittedQuestions,
      });

      // Verify counts - now only counts submitted questions
      expect(result.numberOfQuestionsPassed).to.equal(1); // q1
      expect(result.numberOfQuestionsFailed).to.equal(1); // q3
      expect(result.numberOfQuestionsSkipped).to.equal(0); // no submitted questions were skipped
      expect(result.numberOfQuestionsAnswered).to.equal(2); // q1, q3
      expect(result.totalQuestions).to.equal(2); // only submitted questions are counted

      // Verify totals add up
      const totalCounted =
        result.numberOfQuestionsPassed +
        result.numberOfQuestionsFailed +
        result.numberOfQuestionsSkipped;
      expect(totalCounted).to.equal(result.totalQuestions);

      // Verify scores - only from submitted questions
      expect(result.totalScore).to.equal(25); // 10 + 15 (only submitted questions)
      expect(result.totalPassedScore).to.equal(10); // only q1 passed
    });

    it('Should correctly count questions when submitted but marked as skipped (empty answers)', async () => {
      // Setup: 3 questions, all submitted but some with empty answers
      const assignedQuestions = [
        createMockQuestion('q1', 10),
        createMockQuestion('q2', 5),
        createMockQuestion('q3', 15),
      ];
      const markQuestions = assignedQuestions;
      const submittedQuestions = [
        createMockSubmittedQuestion('q1', ['Correct Answer']),
        createMockSubmittedQuestion('q2', []), // Empty answer
        createMockSubmittedQuestion('q3', ['', '   ']), // Empty/whitespace answers
      ];

      // Mock responses
      markStub
        .withArgs(Sinon.match.any, 'q1', Sinon.match.any, ['Correct Answer'])
        .resolves({
          answerStatus: AnswerStatus.CORRECT,
          scored: 10,
          isAnswered: true,
          testTakerAnswers: ['Correct Answer'],
        });

      markStub.withArgs(Sinon.match.any, 'q2', Sinon.match.any, []).resolves({
        answerStatus: AnswerStatus.SKIPPED,
        scored: 0,
        isAnswered: false,
        testTakerAnswers: [],
      });

      markStub
        .withArgs(Sinon.match.any, 'q3', Sinon.match.any, ['', '   '])
        .resolves({
          answerStatus: AnswerStatus.SKIPPED,
          scored: 0,
          isAnswered: false,
          testTakerAnswers: ['', '   '],
        });

      const result = await AssessmentProcess.markTest({
        assessmentTakerId: 'test-taker-1',
        assignedQuestionsData: assignedQuestions,
        markQuestionsData: markQuestions,
        submitedQuestionsData: submittedQuestions,
      });

      // Verify counts
      expect(result.numberOfQuestionsPassed).to.equal(1); // q1
      expect(result.numberOfQuestionsFailed).to.equal(0); // none
      expect(result.numberOfQuestionsSkipped).to.equal(2); // q2, q3
      expect(result.numberOfQuestionsAnswered).to.equal(1); // only q1
      expect(result.totalQuestions).to.equal(3);

      // Verify totals add up
      const totalCounted =
        result.numberOfQuestionsPassed +
        result.numberOfQuestionsFailed +
        result.numberOfQuestionsSkipped;
      expect(totalCounted).to.equal(result.totalQuestions);
    });

    it('Should handle edge case with no questions assigned', async () => {
      const result = await AssessmentProcess.markTest({
        assessmentTakerId: 'test-taker-1',
        assignedQuestionsData: [],
        markQuestionsData: [],
        submitedQuestionsData: [],
      });

      expect(result.numberOfQuestionsPassed).to.equal(0);
      expect(result.numberOfQuestionsFailed).to.equal(0);
      expect(result.numberOfQuestionsSkipped).to.equal(0);
      expect(result.numberOfQuestionsAnswered).to.equal(0);
      expect(result.totalQuestions).to.equal(0);
      expect(result.totalScore).to.equal(0);
      expect(result.totalPassedScore).to.equal(0);
    });

    it('Should handle edge case with all questions skipped (none submitted)', async () => {
      const assignedQuestions = [
        createMockQuestion('q1', 10),
        createMockQuestion('q2', 5),
        createMockQuestion('q3', 15),
      ];

      const result = await AssessmentProcess.markTest({
        assessmentTakerId: 'test-taker-1',
        assignedQuestionsData: assignedQuestions,
        markQuestionsData: assignedQuestions,
        submitedQuestionsData: [], // No questions submitted
      });

      // When no questions are submitted, markTest processes an empty array
      expect(result.numberOfQuestionsPassed).to.equal(0);
      expect(result.numberOfQuestionsFailed).to.equal(0);
      expect(result.numberOfQuestionsSkipped).to.equal(0);
      expect(result.numberOfQuestionsAnswered).to.equal(0);
      expect(result.totalQuestions).to.equal(0); // No submitted questions to count
      expect(result.totalScore).to.equal(0); // No submitted questions to score
      expect(result.totalPassedScore).to.equal(0); // No points earned
    });
  });
});
