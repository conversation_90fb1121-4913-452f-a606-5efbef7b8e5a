import { expect } from 'chai';
import sinon from 'sinon';
import proxyquire from 'proxyquire';
import { AppError, HttpCode } from '../../src/middlewares/errorHandler';
import { RequestExtension } from '../../src/helpers/types';

describe('AccountStatus with ErrorHandler Integration', () => {
  let AccountStatus;
  let axiosStub;
  let redisCacheStub;
  let errorHandlerStub;
  let sendMailStub;
  let winstonStub;
  let reqMock: RequestExtension;
  const sandbox = sinon.createSandbox();

  beforeEach(() => {
    // Mock dependencies
    axiosStub = {
      get: sandbox.stub(),
    };

    redisCacheStub = {
      getCachedData: sandbox.stub(),
      setCacheData: sandbox.stub().resolves(),
    };

    sendMailStub = sandbox.stub().resolves();

    winstonStub = {
      format: {
        combine: sandbox.stub(),
        timestamp: sandbox.stub(),
        json: sandbox.stub(),
        prettyPrint: sandbox.stub(),
      },
      transports: {
        Console: sandbox.stub(),
        File: sandbox.stub(),
      },
      createLogger: sandbox.stub().returns({
        error: sandbox.stub(),
      }),
    };

    errorHandlerStub = {
      handleError: sandbox.stub().resolves(),
      isTrustedError: sandbox.stub().callsFake((error) => {
        return error instanceof AppError && error.isOperational;
      }),
    };

    // Use proxyquire to mock dependencies
    AccountStatus = proxyquire('../../src/helpers/accountStatus', {
      axios: axiosStub,
      './redisCache': {
        getCachedData: redisCacheStub.getCachedData,
        setCacheData: redisCacheStub.setCacheData,
      },
      '../middlewares/errorHandler': {
        AppError,
        HttpCode,
        default: errorHandlerStub,
      },
      winston: winstonStub,
      '../helpers/sendmail': {
        sendMail: sendMailStub,
      },
    }).default;

    // Mock request object
    reqMock = {
      user: {
        userId: 'user123',
        organizationId: 'org123',
        permissions: ['default:permission'],
        role: 'default-role',
      },
      headers: {
        authorization: 'Bearer token123',
      },
      method: 'GET',
      path: '/test',
    } as unknown as RequestExtension;
  });

  afterEach(() => {
    sandbox.restore();
  });

  describe('checkAccountStatus', () => {
    it('should properly handle AppError from auth service', async () => {
      redisCacheStub.getCachedData.resolves(null);

      // Create an axios error with response object
      const axiosError = new Error('Request failed') as any;
      axiosError.response = {
        status: 401,
        data: {
          message: 'Invalid token',
        },
      };
      axiosStub.get.rejects(axiosError);

      try {
        await AccountStatus.checkAccountStatus(reqMock);
        expect.fail('Should have thrown an error');
      } catch (error) {
        expect(error).to.be.instanceOf(AppError);
        expect((error as AppError).httpCode).to.equal(401);
        expect((error as AppError).message).to.equal('Invalid token');

        // The error handler is not called directly by AccountStatus
        // It would be called by Express middleware
      }
    });

    it('should return null for server errors (status >= 500)', async () => {
      redisCacheStub.getCachedData.resolves(null);

      const serverError = new Error('Server error') as any;
      serverError.response = {
        status: 503,
        data: {
          message: 'Service unavailable',
        },
      };
      axiosStub.get.rejects(serverError);

      const result = await AccountStatus.checkAccountStatus(reqMock);
      expect(result).to.equal(true);
    });

    it('should return null for network errors', async () => {
      redisCacheStub.getCachedData.resolves(null);

      const networkError = new Error('Network error') as any;
      networkError.request = {}; // Just needs to exist
      axiosStub.get.rejects(networkError);

      const result = await AccountStatus.checkAccountStatus(reqMock);
      expect(result).to.equal(true);
    });

    it('should throw AppError for other errors', async () => {
      redisCacheStub.getCachedData.resolves(null);

      const otherError = new Error('Unexpected error');
      axiosStub.get.rejects(otherError);

      try {
        await AccountStatus.checkAccountStatus(reqMock);
        expect.fail('Should have thrown an error');
      } catch (error) {
        expect(error).to.be.instanceOf(AppError);
        expect((error as AppError).httpCode).to.equal(
          HttpCode.INTERNAL_SERVER_ERROR
        );
        expect((error as AppError).message).to.equal(
          'Error authenticating user'
        );
        expect((error as AppError).isOperational).to.equal(false);
      }
    });

    it('should properly handle organization deactivation', async () => {
      const cachedUser = {
        id: 'user123',
        email: '<EMAIL>',
        role_id: 1,
        organizationId: 'org123',
        system: false,
        activated: true,
        organization_activated: false,
        permissions: ['test:permission'],
        role: 'test-role',
      };

      redisCacheStub.getCachedData.resolves(cachedUser);

      try {
        await AccountStatus.checkAccountStatus(reqMock);
        expect.fail('Should have thrown an error');
      } catch (error) {
        expect(error).to.be.instanceOf(AppError);
        expect((error as AppError).httpCode).to.equal(HttpCode.BAD_REQUEST);
        expect((error as AppError).message).to.equal(
          "Your organization's account has been deactivated"
        );
      }
    });

    it('should properly handle user account deactivation', async () => {
      const cachedUser = {
        id: 'user123',
        email: '<EMAIL>',
        role_id: 1,
        organizationId: 'org123',
        system: false,
        activated: false,
        organization_activated: true,
        permissions: ['test:permission'],
        role: 'test-role',
      };

      redisCacheStub.getCachedData.resolves(cachedUser);

      try {
        await AccountStatus.checkAccountStatus(reqMock);
        expect.fail('Should have thrown an error');
      } catch (error) {
        expect(error).to.be.instanceOf(AppError);
        expect((error as AppError).httpCode).to.equal(HttpCode.BAD_REQUEST);
        expect((error as AppError).message).to.equal(
          'Your account has been deactivated, Contact your organization admin.'
        );
      }
    });

    it('should update user permissions and role from cache', async () => {
      const cachedUser = {
        id: 'user123',
        email: '<EMAIL>',
        role_id: 1,
        organizationId: 'org123',
        system: false,
        activated: true,
        organization_activated: true,
        permissions: ['updated:permission'],
        role: 'updated-role',
      };

      redisCacheStub.getCachedData.resolves(cachedUser);

      const result = await AccountStatus.checkAccountStatus(reqMock);

      expect(result).to.equal(true);
      expect(reqMock.user.permissions).to.deep.equal(['updated:permission']);
      expect(reqMock.user.role).to.equal('updated-role');
      expect(redisCacheStub.setCacheData.calledOnce).to.be.true;
    });

    it('should fetch account status if not in cache', async () => {
      redisCacheStub.getCachedData.resolves(null);

      const userResponse = {
        data: {
          data: {
            user: {
              id: 'user123',
              email: '<EMAIL>',
              role_id: 1,
              organizationId: 'org123',
              system: false,
              activated: true,
              organization_activated: true,
              permissions: ['api:permission'],
              role: 'api-role',
            },
          },
        },
      };

      axiosStub.get.resolves(userResponse);

      const result = await AccountStatus.checkAccountStatus(reqMock);

      expect(result).to.equal(true);
      expect(reqMock.user.permissions).to.deep.equal(['api:permission']);
      expect(reqMock.user.role).to.equal('api-role');
      expect(axiosStub.get.calledOnce).to.be.true;
      expect(redisCacheStub.setCacheData.calledOnce).to.be.true;
    });
  });

  describe('ErrorHandler standalone tests', () => {
    let originalEnv: NodeJS.ProcessEnv;

    beforeEach(() => {
      originalEnv = process.env;
      process.env = {
        ...originalEnv,
        DEV_EMAIL_1: '<EMAIL>',
        DEV_EMAIL_2: '<EMAIL>',
        DEV_EMAIL_3: '<EMAIL>',
      };
    });

    afterEach(() => {
      process.env = originalEnv;
    });

    it('should correctly identify trusted errors', () => {
      const trustedError = new AppError({
        httpCode: HttpCode.BAD_REQUEST,
        description: 'Test error',
        isOperational: true,
      });
      const untrustedError = new Error('Regular error');

      expect(errorHandlerStub.isTrustedError(trustedError)).to.be.true;
      expect(errorHandlerStub.isTrustedError(untrustedError)).to.be.false;
    });

    it('should handle error with proper middleware integration', async () => {
      const resMock = {
        status: sandbox.stub().returnsThis(),
        send: sandbox.stub(),
      };
      const reqMock = {
        method: 'GET',
        path: '/api/test',
      };

      const appError = new AppError({
        httpCode: HttpCode.FORBIDDEN,
        description: 'Access denied',
        data: { reason: 'insufficient permissions' },
      });

      await errorHandlerStub.handleError(appError, reqMock, resMock);

      // Since we're using a stub, we can only verify it was called
      expect(errorHandlerStub.handleError.calledOnce).to.be.true;
      expect(
        errorHandlerStub.handleError.calledWith(appError, reqMock, resMock)
      ).to.be.true;
    });
  });
});
