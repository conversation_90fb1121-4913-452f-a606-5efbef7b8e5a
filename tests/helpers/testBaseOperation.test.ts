import Sinon from 'sinon';
import { expect } from 'chai';
import proxyquire from 'proxyquire';
import {
  fakeAssessmentTaker,
  saveAssessmentMonitoringDataMock,
  testCreationCandidateMockData,
  testCreationCandidateMockMarkData,
} from '../fakeData/mockresponse';
import { submitAssessmentMockPayload } from '../fakeData/mockPayload';

describe('testBaseOperation', () => {
  let testBaseOperation: any;
  let AssessmentProcessStub: any;
  let prismaStub: any;
  let proctoringBasedSubmissionStub: any;
  let AssessmentTakerRepositoryStub: any;
  let TestResultRepositoryStub: any;
  let AssessmentLogStub: any;

  beforeEach(() => {
    // Create stubs for all dependencies
    AssessmentProcessStub = {
      AssessmentProcess: {
        markTest: Sinon.stub().resolves({
          numberOfQuestionsAnswered: 1,
          numberOfQuestionsFailed: 0,
          numberOfQuestionsPassed: 1,
          testResults: [],
          totalPassedScore: 1,
          totalScore: 1,
        }),
      },
    };

    prismaStub = {
      testResult: {
        update: Sinon.stub().resolves({}),
      },
      assessmentTaker: {
        update: Sinon.stub().resolves({}),
      },
    };

    proctoringBasedSubmissionStub = {
      saveAssessmentMonitoringData: Sinon.stub().resolves(
        saveAssessmentMonitoringDataMock
      ),
    };

    AssessmentTakerRepositoryStub = {
      AssessmentTakerRepository: {
        saveAssessmentTakerTestProctoringSummary: Sinon.stub().resolves({}),
      },
    };

    TestResultRepositoryStub = {
      TestResultRepository: {
        updateTestResultAfterTestSubmission: Sinon.stub().resolves({}),
      },
    };

    AssessmentLogStub = {
      createLog: Sinon.stub().resolves({}),
    };

    // Load the module with stubs
    testBaseOperation = proxyquire('../../src/helpers/testBaseOperation', {
      '../helpers/assessmentProcess': AssessmentProcessStub,
      '../prisma': { default: prismaStub },
      '../helpers/protoringBasedSubmission': proctoringBasedSubmissionStub,
      '../repository/assessmentTakerRepository': AssessmentTakerRepositoryStub,
      '../repository/testResultRepository': TestResultRepositoryStub,
      '../repository/AssessmentTakerLog': { AssessmentLog: AssessmentLogStub },
    });

    // Handle both class and non-class exports
    // if (typeof testBaseOperation === 'object' && testBaseOperation.default) {
    //   testBaseOperation = testBaseOperation.default;
    // }
  });

  afterEach(() => {
    Sinon.restore();
  });

  describe('markAndSaveTest', () => {
    it('Should update assessmentTaker for SUBMITTED status', async () => {
      const mockMarkTestResult = {
        numberOfQuestionsAnswered: 1,
        numberOfQuestionsFailed: 0,
        numberOfQuestionsPassed: 1,
        testResults: [],
        totalPassedScore: 10,
        totalScore: 10,
      };
      AssessmentProcessStub.AssessmentProcess.markTest.resolves(
        mockMarkTestResult
      );

      await testBaseOperation.markAndSaveTest({
        assessmentTakerInfo: fakeAssessmentTaker,
        submittedTestPayload: submitAssessmentMockPayload,
        markTestData: testCreationCandidateMockMarkData.assessment.tests[0],
        asssignedTestData: testCreationCandidateMockData.assessment.tests[0],
        status: 'SUBMITTED',
      });

      // Verify assessmentTaker.update was called for SUBMITTED status
      expect(prismaStub.assessmentTaker.update.calledOnce).to.be.true;
    });

    it('Should handle failed test case', async () => {
      // Mock a failed test result (score below passMark)
      const mockMarkTestResult = {
        numberOfQuestionsAnswered: 1,
        numberOfQuestionsFailed: 1,
        numberOfQuestionsPassed: 0,
        testResults: [],
        totalPassedScore: 2, // Assuming passMark is >5
        totalScore: 10,
      };
      AssessmentProcessStub.AssessmentProcess.markTest.resolves(
        mockMarkTestResult
      );

      await testBaseOperation.markAndSaveTest({
        assessmentTakerInfo: fakeAssessmentTaker,
        submittedTestPayload: submitAssessmentMockPayload,
        markTestData: testCreationCandidateMockMarkData.assessment.tests[0],
        asssignedTestData: testCreationCandidateMockData.assessment.tests[0],
        status: 'SUBMITTED',
      });

      const updateData = prismaStub.testResult.update.firstCall.args[0].data;
      expect(updateData.passStatus).to.equal('FAIL');
    });
  });

  describe('testSubmissionProcessor', () => {
    it('Should process test submission', async function () {
      this.timeout(5000); // Increase timeout to handle async operations

      await testBaseOperation.testSubmissionProcessor({
        assessmentTakerInfo: fakeAssessmentTaker,
        currentMarkerDBTest:
          testCreationCandidateMockMarkData.assessment.tests[0],
        currentAssignedDBTest:
          testCreationCandidateMockData.assessment.tests[0],
        submittedTestResult: submitAssessmentMockPayload,
      });

      // Check if AssessmentProcess.markTest was called
      expect(AssessmentProcessStub.AssessmentProcess.markTest.called).to.be
        .true;

      // Check if saveAssessmentMonitoringData was called
      expect(proctoringBasedSubmissionStub.saveAssessmentMonitoringData.called)
        .to.be.true;

      // Check if createLog was called
      expect(AssessmentLogStub.createLog.called).to.be.true;

      // Restore the spy
    });
  });

  describe('saveProctoringData', () => {
    it('Should save proctoring data', async () => {
      await testBaseOperation.saveProctoringData(
        fakeAssessmentTaker,
        submitAssessmentMockPayload
      );

      expect(
        proctoringBasedSubmissionStub.saveAssessmentMonitoringData.calledOnce
      ).to.be.true;
      expect(
        AssessmentTakerRepositoryStub.AssessmentTakerRepository
          .saveAssessmentTakerTestProctoringSummary.calledOnce
      ).to.be.true;
      expect(
        TestResultRepositoryStub.TestResultRepository
          .updateTestResultAfterTestSubmission.calledOnce
      ).to.be.true;
    });
  });
});
