// eslint-disable-next-line @typescript-eslint/no-explicit-any
import { expect } from 'chai';
import { Question } from '../../src/dtos/assessmentTakerDtos';
import { MarkQuestion } from '../../src/helpers/markQuestion';
import {
  essayQuestionMarkData,
  fillInQuestionMarkData,
  matrixQuestionMarkData,
  multiChoiceQuestionMarkData,
  multiSelectQuestionMarkData,
  trueOrFalseQuestionMarkData,
} from '../fakeData/mockresponse';
import Sinon from 'sinon';
import { AnswerStatus, MarkQuestionResult } from '../../src/helpers/types';
import { OpenaiMarker } from '../../src/helpers/AImarker/openAIMarker';
import { AppError } from '../../src/middlewares/errorHandler';

describe('Mark Question', () => {
  afterEach(() => {
    Sinon.restore();
  });

  describe('mark', () => {
    it('Should mark question as wrong when not answer is not given', async () => {
      const testTakerAnswer = [];
      const response = await MarkQuestion.mark(
        'v1',
        'qwersasa',
        {} as Question,
        testTakerAnswer as string[]
      );

      expect(response).to.deep.equal({
        answerStatus: 'SKIPPED',
        scored: 0,
        isAnswered: false,
        testTakerAnswers: [] as string[],
      });
    });

    it('Should mark multi-select question', async () => {
      const fakeTestTakerAnswers = ['Option-1'];
      const multiSelectTypeStub = Sinon.stub(
        MarkQuestion,
        'multiSelectType'
      ).returns({
        answerStatus: AnswerStatus.WRONG,
        scored: 0,
        isAnswered: true,
        testTakerAnswers: fakeTestTakerAnswers,
      });
      const response = await MarkQuestion.mark(
        'v1',
        'qwersasa',
        multiSelectQuestionMarkData,
        fakeTestTakerAnswers
      );

      expect(multiSelectTypeStub.calledOnce).to.be.true;
      expect(multiSelectTypeStub.args[0][0]).to.be.equal(fakeTestTakerAnswers);
      expect(multiSelectTypeStub.args[0][1]).to.be.equal(
        multiSelectQuestionMarkData.multipleSelectAnswer?.answer
      );
      expect(multiSelectTypeStub.args[0][2]).to.be.equal(
        multiSelectQuestionMarkData.score
      );
      expect(multiSelectTypeStub.args[0][3]).to.be.equal(
        multiSelectQuestionMarkData.strictMark
      );

      expect(response.answerStatus).to.be.a('string');
      expect(response.isAnswered).to.equal(true);
      expect(response.scored).to.be.a('number');
      expect(response.testTakerAnswers).to.equal(fakeTestTakerAnswers);

      multiSelectTypeStub.restore();
    });
    it('Should mark multi-choice question', async () => {
      const fakeTestTakerAnswers = ['Option-1'];
      const singleChoiceTypeStub = Sinon.stub(
        MarkQuestion,
        'singleChoiceType'
      ).returns({
        answerStatus: AnswerStatus.WRONG,
        scored: 0,
        isAnswered: true,
        testTakerAnswers: fakeTestTakerAnswers,
      });
      const response = await MarkQuestion.mark(
        'v1',
        'qwersasa',
        multiChoiceQuestionMarkData,
        fakeTestTakerAnswers
      );

      expect(singleChoiceTypeStub.calledOnce).to.be.true;
      expect(singleChoiceTypeStub.args[0][0]).to.be.equal(fakeTestTakerAnswers);
      expect(singleChoiceTypeStub.args[0][1]).to.be.equal(
        multiChoiceQuestionMarkData.multipleChoiceAnswer?.answer
      );
      expect(singleChoiceTypeStub.args[0][2]).to.be.equal(
        multiSelectQuestionMarkData.score
      );

      expect(response.answerStatus).to.be.a('string');
      expect(response.isAnswered).to.equal(true);
      expect(response.scored).to.be.a('number');
      expect(response.testTakerAnswers).to.equal(fakeTestTakerAnswers);

      singleChoiceTypeStub.restore();
    });
    it('Should mark true-or-false question', async () => {
      const fakeTestTakerAnswers = ['True'];
      const singleChoiceTypeStub = Sinon.stub(
        MarkQuestion,
        'singleChoiceType'
      ).returns({
        answerStatus: AnswerStatus.WRONG,
        scored: 0,
        isAnswered: true,
        testTakerAnswers: fakeTestTakerAnswers,
      });
      const response = await MarkQuestion.mark(
        'v1',
        'qwersasa',
        trueOrFalseQuestionMarkData,
        fakeTestTakerAnswers
      );

      expect(singleChoiceTypeStub.calledOnce).to.be.true;
      expect(singleChoiceTypeStub.args[0][0]).to.be.equal(fakeTestTakerAnswers);
      expect(singleChoiceTypeStub.args[0][1]).to.be.equal(
        trueOrFalseQuestionMarkData.trueOrFalseAnswer?.answer
      );
      expect(singleChoiceTypeStub.args[0][2]).to.be.equal(
        trueOrFalseQuestionMarkData.score
      );

      expect(response.answerStatus).to.be.a('string');
      expect(response.isAnswered).to.equal(true);
      expect(response.scored).to.be.a('number');
      expect(response.testTakerAnswers).to.equal(fakeTestTakerAnswers);

      singleChoiceTypeStub.restore();
    });
    it('Should mark fillin question', async () => {
      const fakeTestTakerAnswers = ['Option-3', 'Option-1'];
      const fillInTypeStub = Sinon.stub(MarkQuestion, 'fillInType').returns({
        answerStatus: AnswerStatus.WRONG,
        scored: 0,
        isAnswered: true,
        testTakerAnswers: fakeTestTakerAnswers,
      });
      const response = await MarkQuestion.mark(
        'v1',
        'qwersasa',
        fillInQuestionMarkData,
        fakeTestTakerAnswers
      );

      expect(fillInTypeStub.calledOnce).to.be.true;
      expect(fillInTypeStub.args[0][0]).to.be.equal(fakeTestTakerAnswers);
      expect(fillInTypeStub.args[0][1]).to.be.equal(
        fillInQuestionMarkData.fillInAnswer?.answer
      );
      expect(fillInTypeStub.args[0][2]).to.be.equal(
        fillInQuestionMarkData.score
      );

      expect(response.answerStatus).to.be.a('string');
      expect(response.isAnswered).to.equal(true);
      expect(response.scored).to.be.a('number');
      expect(response.testTakerAnswers).to.equal(fakeTestTakerAnswers);

      fillInTypeStub.restore();
    });
    it('Should mark essay question', async () => {
      const fakeTestTakerAnswers = ['essay-answer'];
      const essayTypeStub = Sinon.stub(MarkQuestion, 'essayType').resolves({
        answerStatus: AnswerStatus.WRONG,
        scored: 0,
        isAnswered: true,
        testTakerAnswers: fakeTestTakerAnswers,
      });
      const response = await MarkQuestion.mark(
        'v1',
        'qwersasa',
        essayQuestionMarkData,
        fakeTestTakerAnswers
      );

      expect(essayTypeStub.calledOnce).to.be.true;
      expect(essayTypeStub.args[0][0]).to.be.equal(
        essayQuestionMarkData.questionText
      );
      expect(essayTypeStub.args[0][1]).to.be.equal(fakeTestTakerAnswers.join());
      expect(essayTypeStub.args[0][2]).to.be.equal(
        essayQuestionMarkData.essayAnswer?.rubrics
      );
      expect(essayTypeStub.args[0][3]).to.be.equal(essayQuestionMarkData.score);

      expect(response.isAnswered).to.equal(true);
      expect(response.scored).to.be.a('number');
      expect(response.testTakerAnswers).to.equal(fakeTestTakerAnswers);

      essayTypeStub.restore();
    });
    it('Should mark matrix question', async () => {
      const fakeTestTakerAnswers = ['matrix-answer'];
      const matrixTypeStub = Sinon.stub(MarkQuestion, 'matrixType').returns({
        answerStatus: AnswerStatus.WRONG,
        scored: 0,
        isAnswered: true,
        testTakerAnswers: fakeTestTakerAnswers,
      });
      const response = await MarkQuestion.mark(
        'v1',
        'qwersasa',
        matrixQuestionMarkData,
        fakeTestTakerAnswers
      );

      expect(matrixTypeStub.calledOnce).to.be.true;
      expect(matrixTypeStub.args[0][0]).to.be.equal(fakeTestTakerAnswers);
      expect(matrixTypeStub.args[0][1]).to.be.equal(
        matrixQuestionMarkData.matchMatrixAnswer.questions
      );

      expect(matrixTypeStub.args[0][2]).to.be.equal(
        essayQuestionMarkData.score
      );

      expect(response.answerStatus).to.be.a('string');
      expect(response.isAnswered).to.equal(true);
      expect(response.scored).to.be.a('number');
      expect(response.testTakerAnswers).to.equal(fakeTestTakerAnswers);

      matrixTypeStub.restore();
    });
  });

  describe('singleChoiceType', () => {
    it('Should mark question as wrong when wrong answer is provided', () => {
      const fakeTestTakerAnswer = ['Option-3'];
      const fakeCorrectAnswer = ['Option-4'];
      const fakeQuestionScore = 2;

      const response = MarkQuestion.singleChoiceType(
        fakeTestTakerAnswer,
        fakeCorrectAnswer,
        fakeQuestionScore
      );

      expect(response.answerStatus).to.equal('WRONG');
      expect(response.isAnswered).to.equal(true);
      expect(response.scored).to.equal(0);
    });

    it('Should mark question as correct when correct answer is provided', () => {
      const fakeTestTakerAnswer = ['Option-4'];
      const fakeCorrectAnswer = ['Option-4'];
      const fakeQuestionScore = 2;

      const response = MarkQuestion.singleChoiceType(
        fakeTestTakerAnswer,
        fakeCorrectAnswer,
        fakeQuestionScore
      );

      expect(response.answerStatus).to.equal('CORRECT');
      expect(response.isAnswered).to.equal(true);
      expect(response.scored).to.equal(2);
    });

    it('Should mark question as skipped when no answer is provided', () => {
      const fakeTestTakerAnswer = [];
      const fakeCorrectAnswer = ['Option-4'];
      const fakeQuestionScore = 2;

      const response = MarkQuestion.singleChoiceType(
        fakeTestTakerAnswer,
        fakeCorrectAnswer,
        fakeQuestionScore
      );

      expect(response.answerStatus).to.equal('SKIPPED');
      expect(response.isAnswered).to.equal(false);
      expect(response.scored).to.equal(0);
    });

    it('Should mark question as skipped when null/undefined answer is provided', () => {
      const fakeTestTakerAnswer = null;
      const fakeCorrectAnswer = ['Option-4'];
      const fakeQuestionScore = 2;

      const response = MarkQuestion.singleChoiceType(
        fakeTestTakerAnswer as any,
        fakeCorrectAnswer,
        fakeQuestionScore
      );

      expect(response.answerStatus).to.equal('SKIPPED');
      expect(response.isAnswered).to.equal(false);
      expect(response.scored).to.equal(0);
    });
  });

  describe('multiSelectType', () => {
    it('Should mark question as wrong when number of provided answers exceed expected number of answers', () => {
      const fakeTestTakerAnswers = ['Option-1', 'Option-2', 'Option-3'];
      const fakeCorrectAnswer = ['Option-1', 'Option-2'];
      const fakeQuestionScore = 2;
      const response = MarkQuestion.multiSelectType(
        fakeTestTakerAnswers,
        fakeCorrectAnswer,
        fakeQuestionScore,
        false
      );

      expect(response.answerStatus).to.equal('WRONG');
      expect(response.isAnswered).to.equal(true);
      expect(response.scored).to.equal(0);
    });
    it('Should mark question as wrong when correct answer are not provided', () => {
      const fakeTestTakerAnswers = ['Option-1', 'Option-2', 'Option-3'];
      const fakeCorrectAnswer = [];
      const fakeQuestionScore = 2;
      const response = MarkQuestion.multiSelectType(
        fakeTestTakerAnswers,
        fakeCorrectAnswer,
        fakeQuestionScore,
        false
      );

      expect(response.answerStatus).to.equal('WRONG');
      expect(response.isAnswered).to.equal(true);
      expect(response.scored).to.equal(0);
    });

    it('Should mark question partial when strickmark value is false', () => {
      const fakeTestTakerAnswers = ['Option-1', 'Option-2'];
      const fakeCorrectAnswer = ['Option-1', 'Option-2', 'Option-3'];
      const fakeQuestionScore = 2;
      const mockPartialMarkResult = {
        isAnswered: true,
        isAnswerCorrect: false,
        scored: 0,
        testTakerAnswers: fakeTestTakerAnswers,
      };
      const strictMark = false;
      const partialMarkMultiSelectTypeStub = Sinon.stub(
        MarkQuestion,
        'partialMarkMultiSelectType'
      ).returns(mockPartialMarkResult);
      const response = MarkQuestion.multiSelectType(
        fakeTestTakerAnswers,
        fakeCorrectAnswer,
        fakeQuestionScore,
        strictMark
      );

      expect(response).to.deep.equal(mockPartialMarkResult);
      expect(partialMarkMultiSelectTypeStub.args[0][0]).to.be.equal(
        fakeTestTakerAnswers
      );
      expect(partialMarkMultiSelectTypeStub.args[0][1]).to.be.equal(
        fakeCorrectAnswer
      );
      expect(partialMarkMultiSelectTypeStub.args[0][2]).to.be.equal(
        fakeQuestionScore
      );

      partialMarkMultiSelectTypeStub.restore();
    });
    it('Should mark question partial when strickmark value is true', () => {
      const fakeTestTakerAnswers = ['Option-1', 'Option-2'];
      const fakeCorrectAnswer = ['Option-1', 'Option-2', 'Option-3'];
      const fakeQuestionScore = 2;
      const mockPartialMarkResult = {
        isAnswered: true,
        isAnswerCorrect: false,
        scored: 0,
        testTakerAnswers: fakeTestTakerAnswers,
      };
      const strictMark = true;

      const partialMarkMultiSelectTypeStub = Sinon.stub(
        MarkQuestion,
        'partialMarkMultiSelectType'
      ).returns({} as MarkQuestionResult);
      const strictMarkMultiSelectType = Sinon.stub(
        MarkQuestion,
        'strictMarkMultiSelectType'
      ).returns(mockPartialMarkResult);

      const response = MarkQuestion.multiSelectType(
        fakeTestTakerAnswers,
        fakeCorrectAnswer,
        fakeQuestionScore,
        strictMark
      );

      expect(response).to.deep.equal(mockPartialMarkResult);
      expect(partialMarkMultiSelectTypeStub.callCount).to.be.equal(0);
      expect(strictMarkMultiSelectType.args[0][0]).to.be.equal(
        fakeTestTakerAnswers
      );
      expect(strictMarkMultiSelectType.args[0][1]).to.be.equal(
        fakeCorrectAnswer
      );
      expect(strictMarkMultiSelectType.args[0][2]).to.be.equal(
        fakeQuestionScore
      );

      partialMarkMultiSelectTypeStub.restore();
      strictMarkMultiSelectType.restore();
    });
  });

  describe('fillInType', () => {
    it('Should mark fill-in-blank question', () => {
      const fakeTestTakerAnswers = ['Option-1', 'Option-3'];
      const fakeCorrectAnswer = ['Option-1', 'Option-2', 'Option-3'];
      const fakeQuestionScore = 6;

      const response = MarkQuestion.fillInType(
        fakeTestTakerAnswers,
        fakeCorrectAnswer,
        fakeQuestionScore
      );

      expect(response.answerStatus).to.be.equal('CORRECT');
      expect(response.scored).to.be.a('number');
      expect(response.testTakerAnswers).to.be.equal(fakeTestTakerAnswers);
    });

    it('Should mark question as skipped when no answers are provided', () => {
      const fakeTestTakerAnswers = [];
      const fakeCorrectAnswer = ['Option-1', 'Option-2', 'Option-3'];
      const fakeQuestionScore = 6;

      const response = MarkQuestion.fillInType(
        fakeTestTakerAnswers,
        fakeCorrectAnswer,
        fakeQuestionScore
      );

      expect(response.answerStatus).to.equal('SKIPPED');
      expect(response.isAnswered).to.equal(false);
      expect(response.scored).to.equal(0);
    });

    it('Should mark question as skipped when only empty strings are provided', () => {
      const fakeTestTakerAnswers = ['', '   ', ''];
      const fakeCorrectAnswer = ['Option-1', 'Option-2', 'Option-3'];
      const fakeQuestionScore = 6;

      const response = MarkQuestion.fillInType(
        fakeTestTakerAnswers,
        fakeCorrectAnswer,
        fakeQuestionScore
      );

      expect(response.answerStatus).to.equal('SKIPPED');
      expect(response.isAnswered).to.equal(false);
      expect(response.scored).to.equal(0);
    });

    it('Should mark question as answered when at least one non-empty answer is provided', () => {
      const fakeTestTakerAnswers = ['', 'Option-2', ''];
      const fakeCorrectAnswer = ['Option-1', 'Option-2', 'Option-3'];
      const fakeQuestionScore = 6;

      const response = MarkQuestion.fillInType(
        fakeTestTakerAnswers,
        fakeCorrectAnswer,
        fakeQuestionScore
      );

      expect(response.isAnswered).to.equal(true);
      expect(response.answerStatus).to.equal('CORRECT');
      expect(response.scored).to.be.greaterThan(0);
    });
  });

  describe('essayType', () => {
    const testQuestion = 'What is the capital of France?';
    const testAnswer = 'The capital of France is Paris.';
    const testRubrics = 'Check for accuracy and completeness';
    const maxScore = 10;

    beforeEach(() => {
      // Mock environment variables before each test
      process.env.AI_PROVIDER = 'GPT';
      process.env.OPENAI_API_KEY = 'fake-api-key';
    });

    afterEach(() => {
      // Clean up after each test
      Sinon.restore();
      delete process.env.AI_PROVIDER;
      delete process.env.OPENAI_API_KEY;
    });

    it('Should mark essay question with perfect AI response', async () => {
      const openaiMarkerStub = Sinon.stub(
        OpenaiMarker.prototype,
        'marker'
      ).resolves('Score: 8\nFeedback: Well done');

      const response = await MarkQuestion.essayType(
        testQuestion,
        testAnswer,
        testRubrics,
        maxScore
      );

      expect(openaiMarkerStub.calledOnce).to.be.true;
      expect(response).to.deep.equal({
        isAnswered: true,
        scored: 8,
        answerStatus: 'CORRECT',
        testTakerAnswers: [testAnswer],
      });
    });

    it('Should handle different score formats in AI response', async () => {
      const responses = [
        'Score: 7\nGood understanding shown.',
        'The score is: 7\nGood understanding shown.',
        '7\nVery good response.',
        'Points: 7\nGood work.',
        'Rating: 7\nWell done.',
        'Marks: 7\nNice job.',
        'This response earns 7 points.',
        '7 points awarded.',
        'Score is 7.',
      ];

      for (const aiResponse of responses) {
        Sinon.restore(); // Clear previous stub
        const markerStub = Sinon.stub(
          OpenaiMarker.prototype,
          'marker'
        ).resolves(aiResponse);

        const response = await MarkQuestion.essayType(
          testQuestion,
          testAnswer,
          testRubrics,
          maxScore
        );

        expect(markerStub.calledOnce).to.be.true;
        expect(
          response.scored,
          `Failed to extract score from: ${aiResponse}`
        ).to.equal(7);
        expect(response.isAnswered).to.be.true;
        expect(response.answerStatus).to.be.equal('CORRECT');
      }
    });

    it('Should retry on empty AI response', async () => {
      const markerStub = Sinon.stub(OpenaiMarker.prototype, 'marker');
      markerStub.onFirstCall().resolves('');
      markerStub.onSecondCall().resolves('Score: 6\nGood answer.');

      const response = await MarkQuestion.essayType(
        testQuestion,
        testAnswer,
        testRubrics,
        maxScore
      );

      expect(response.scored).to.equal(6);
      expect(markerStub.calledTwice).to.be.true;
    });

    it('Should handle invalid score with retry', async () => {
      const markerStub = Sinon.stub(OpenaiMarker.prototype, 'marker');
      markerStub.onFirstCall().resolves('Invalid format');
      markerStub.onSecondCall().resolves('Score: 5\nDecent response.');

      const response = await MarkQuestion.essayType(
        testQuestion,
        testAnswer,
        testRubrics,
        maxScore
      );

      expect(response.scored).to.equal(5);
      expect(markerStub.calledTwice).to.be.true;
    });

    it('Should handle score outside valid range', async () => {
      const markerStub = Sinon.stub(OpenaiMarker.prototype, 'marker').resolves(
        'Score: 15\nExcellent answer.'
      );

      const response = await MarkQuestion.essayType(
        testQuestion,
        testAnswer,
        testRubrics,
        maxScore
      );

      expect(response.scored).to.equal(0);
      expect(response.isAnswered).to.be.true;
      expect(markerStub.called).to.be.true; // Add assertion to use markerStub
    });

    it('Should throw error after max retries on AI failure', async () => {
      const markerStub = Sinon.stub(OpenaiMarker.prototype, 'marker').rejects(
        new Error('AI service unavailable')
      );

      try {
        await MarkQuestion.essayType(
          testQuestion,
          testAnswer,
          testRubrics,
          maxScore
        );
        expect.fail('Should have thrown an error');
      } catch (error: unknown) {
        // Changed from any to unknown
        expect(error).to.be.instanceOf(AppError);
        if (error instanceof AppError) {
          // Type guard
          expect(error.message).to.include('Failed to mark essay');
        }
        expect(markerStub.callCount).to.equal(3); // Initial + 2 retries
      }
    });

    it('Should handle malformed AI responses', async () => {
      const malformedResponses = [
        'Excellent essay but no score mentioned',
        'Score: invalid_number',
        'Points: abc',
        'Rating: undefined',
        'Marks: none',
        'The student wrote well',
        '',
        undefined,
        null,
      ];

      for (const response of malformedResponses) {
        Sinon.restore(); // Clear previous stub
        const markerStub = Sinon.stub(OpenaiMarker.prototype, 'marker');

        // All attempts return invalid responses
        markerStub.onFirstCall().resolves(response as string);
        markerStub.onSecondCall().resolves('Invalid response without score');
        markerStub.onThirdCall().resolves('Still no valid score format');

        const result = await MarkQuestion.essayType(
          testQuestion,
          testAnswer,
          testRubrics,
          maxScore
        );

        // After all retries fail, should return default values
        expect(result).to.deep.equal({
          isAnswered: true,
          scored: 0,
          answerStatus: 'WRONG',
          testTakerAnswers: [testAnswer],
        });

        // Verify the marker was called the expected number of times
        expect(markerStub.callCount).to.equal(3);
      }
    });

    it('Should handle scores above maximum', async () => {
      const highScoreResponses = [
        'Score: 999',
        'Score: 11', // When maxScore is 10
        'Points: 15',
        '20',
        'Rating: 100',
      ];

      for (const response of highScoreResponses) {
        Sinon.restore();
        const markerStub = Sinon.stub(OpenaiMarker.prototype, 'marker');

        // All attempts return invalid scores
        markerStub.onFirstCall().resolves(response);
        markerStub.onSecondCall().resolves(response);
        markerStub.onThirdCall().resolves(response);

        const result = await MarkQuestion.essayType(
          testQuestion,
          testAnswer,
          testRubrics,
          maxScore
        );

        // Should return default values when score is above maximum
        expect(result).to.deep.equal({
          isAnswered: true,
          scored: 0,
          answerStatus: 'WRONG',
          testTakerAnswers: [testAnswer],
        });

        // Verify all retries were attempted
        expect(markerStub.callCount).to.equal(3);
      }
    });

    it('Should mark essay as skipped when empty answer is provided', async () => {
      const emptyAnswer = '';
      const testRubrics = 'Check for accuracy and completeness';
      const maxScore = 10;

      const response = await MarkQuestion.essayType(
        'What is the capital of France?',
        emptyAnswer,
        testRubrics,
        maxScore
      );

      expect(response).to.deep.equal({
        answerStatus: 'SKIPPED',
        scored: 0,
        isAnswered: false,
        testTakerAnswers: [emptyAnswer],
      });
    });

    it('Should mark essay as skipped when only whitespace is provided', async () => {
      const whitespaceAnswer = '   \n\t   ';
      const testRubrics = 'Check for accuracy and completeness';
      const maxScore = 10;

      const response = await MarkQuestion.essayType(
        'What is the capital of France?',
        whitespaceAnswer,
        testRubrics,
        maxScore
      );

      expect(response).to.deep.equal({
        answerStatus: 'SKIPPED',
        scored: 0,
        isAnswered: false,
        testTakerAnswers: [whitespaceAnswer],
      });
    });

    it('Should mark essay question', async () => {
      const fakeTestTakerAnswers = ['essay-answer'];
      const essayTypeStub = Sinon.stub(MarkQuestion, 'essayType').resolves({
        answerStatus: AnswerStatus.WRONG,
        scored: 0,
        isAnswered: true,
        testTakerAnswers: fakeTestTakerAnswers,
      });
      const response = await MarkQuestion.mark(
        'v1',
        'qwersasa',
        essayQuestionMarkData,
        fakeTestTakerAnswers
      );

      expect(essayTypeStub.calledOnce).to.be.true;
      expect(essayTypeStub.args[0][0]).to.be.equal(
        essayQuestionMarkData.questionText
      );
      expect(essayTypeStub.args[0][1]).to.be.equal(fakeTestTakerAnswers.join());
      expect(essayTypeStub.args[0][2]).to.be.equal(
        essayQuestionMarkData.essayAnswer?.rubrics
      );
      expect(essayTypeStub.args[0][3]).to.be.equal(essayQuestionMarkData.score);

      expect(response.answerStatus).to.be.a('string');
      expect(response.isAnswered).to.equal(true);
      expect(response.scored).to.be.a('number');
      expect(response.testTakerAnswers).to.equal(fakeTestTakerAnswers);

      essayTypeStub.restore();
    });
  });

  describe('strictMarkMultiSelectType', () => {
    it('Should mark question as wrong when all expect answers are not provided', () => {
      const fakeTestTakerAnswers = ['Option-1', 'Option-2'];
      const fakeCorrectAnswer = ['Option-1', 'Option-2', 'Option-3'];
      const fakeQuestionScore = 6;

      const response = MarkQuestion.strictMarkMultiSelectType(
        fakeTestTakerAnswers,
        fakeCorrectAnswer,
        fakeQuestionScore
      );

      expect(response).to.deep.equal({
        answerStatus: 'WRONG',
        isAnswered: true,
        scored: 0,
        testTakerAnswers: fakeTestTakerAnswers,
      });
    });

    it('Should mark question as correct when all expect answers are provided', () => {
      const fakeTestTakerAnswers = ['Option-1', 'Option-2', 'Option-3'];
      const fakeCorrectAnswer = ['Option-1', 'Option-2', 'Option-3'];
      const fakeQuestionScore = 6;
      const response = MarkQuestion.strictMarkMultiSelectType(
        fakeTestTakerAnswers,
        fakeCorrectAnswer,
        fakeQuestionScore
      );

      expect(response).to.deep.equal({
        answerStatus: 'CORRECT',
        isAnswered: true,
        scored: fakeQuestionScore,
        testTakerAnswers: fakeTestTakerAnswers,
      });
    });

    it('Should mark question as skipped when no answers are provided', () => {
      const fakeTestTakerAnswers = [];
      const fakeCorrectAnswer = ['Option-1', 'Option-2', 'Option-3'];
      const fakeQuestionScore = 6;

      const response = MarkQuestion.strictMarkMultiSelectType(
        fakeTestTakerAnswers,
        fakeCorrectAnswer,
        fakeQuestionScore
      );

      expect(response).to.deep.equal({
        answerStatus: 'SKIPPED',
        isAnswered: false,
        scored: 0,
        testTakerAnswers: fakeTestTakerAnswers,
      });
    });
  });

  describe('partialMarkMultiSelectType', () => {
    it('Should mark question as correct when all expect answers are provided', () => {
      const fakeTestTakerAnswers = ['Option-1', 'Option-2', 'Option-3'];
      const fakeCorrectAnswer = ['Option-1', 'Option-2', 'Option-3'];
      const fakeQuestionScore = 6;
      const response = MarkQuestion.partialMarkMultiSelectType(
        fakeTestTakerAnswers,
        fakeCorrectAnswer,
        fakeQuestionScore
      );

      expect(response).to.deep.equal({
        answerStatus: 'CORRECT',
        isAnswered: true,
        scored: fakeQuestionScore,
        testTakerAnswers: fakeTestTakerAnswers,
      });
    });

    it('Should mark question as correct when some expect answers are provided', () => {
      const fakeTestTakerAnswers = ['Option-1'];
      const fakeCorrectAnswer = ['Option-1', 'Option-3'];
      const fakeQuestionScore = 6;
      const response = MarkQuestion.partialMarkMultiSelectType(
        fakeTestTakerAnswers,
        fakeCorrectAnswer,
        fakeQuestionScore
      );

      expect(response).to.deep.equal({
        answerStatus: 'CORRECT',
        isAnswered: true,
        scored: 3,
        testTakerAnswers: fakeTestTakerAnswers,
      });
    });

    it('Should mark question as skipped when no answers are provided', () => {
      const fakeTestTakerAnswers = [];
      const fakeCorrectAnswer = ['Option-1', 'Option-2', 'Option-3'];
      const fakeQuestionScore = 6;

      const response = MarkQuestion.partialMarkMultiSelectType(
        fakeTestTakerAnswers,
        fakeCorrectAnswer,
        fakeQuestionScore
      );

      expect(response).to.deep.equal({
        answerStatus: 'SKIPPED',
        isAnswered: false,
        scored: 0,
        testTakerAnswers: fakeTestTakerAnswers,
      });
    });
  });

  describe('matrixType', () => {
    const mockMatrixSubquestions = [
      {
        id: 'sub1',
        matchMatrixAnswerId: 'mma-sub1',
        subquestion: 'Question 1',
        answer: ['A', 'B'],
      },
      {
        id: 'sub2',
        matchMatrixAnswerId: 'mma-sub2',
        subquestion: 'Question 2',
        answer: ['C'],
      },
    ];

    it('Should mark matrix as answered when at least one subquestion has answers', () => {
      const testTakerAnswers = [
        { subquestionId: 'sub1', answers: ['A'] },
        { subquestionId: 'sub2', answers: [] },
      ];
      const questionScore = 10;

      const response = MarkQuestion.matrixType(
        testTakerAnswers,
        mockMatrixSubquestions,
        questionScore
      );

      expect(response.isAnswered).to.equal(true);
      expect(response.answerStatus).to.equal('CORRECT');
      expect(response.scored).to.be.greaterThan(0);
    });

    it('Should mark matrix as skipped when no subquestions have answers', () => {
      const testTakerAnswers = [
        { subquestionId: 'sub1', answers: [] },
        { subquestionId: 'sub2', answers: [] },
      ];
      const questionScore = 10;

      const response = MarkQuestion.matrixType(
        testTakerAnswers,
        mockMatrixSubquestions,
        questionScore
      );

      expect(response.isAnswered).to.equal(false);
      expect(response.answerStatus).to.equal('SKIPPED');
      expect(response.scored).to.equal(0);
    });

    it('Should mark matrix as skipped when no answers are provided at all', () => {
      const testTakerAnswers = [];
      const questionScore = 10;

      const response = MarkQuestion.matrixType(
        testTakerAnswers,
        mockMatrixSubquestions,
        questionScore
      );

      expect(response.isAnswered).to.equal(false);
      expect(response.answerStatus).to.equal('SKIPPED');
      expect(response.scored).to.equal(0);
    });
  });
});
