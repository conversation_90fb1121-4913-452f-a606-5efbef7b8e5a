import { expect } from 'chai';
import {
  AssessmentMetricsNormalizer,
  NormalizedCandidate,
} from '../../src/helpers/assessmentMetricsNormalizer';

describe('AssessmentMetricsNormalizer', () => {
  describe('getCandidateFilter', () => {
    it('should return correct filter with test results requirement', () => {
      const assessmentId = 'test-assessment-id';
      const organizationId = 'test-org-id';

      const filter = AssessmentMetricsNormalizer.getCandidateFilter(
        assessmentId,
        organizationId
      );

      expect(filter).to.deep.equal({
        AND: [
          { assessmentId, organizationId },
          { email: { not: null } },
          { testResults: { some: {} } },
        ],
      });
    });
  });

  describe('getCandidateAssessmentsFilter', () => {
    it('should return correct filter for candidate assessments', () => {
      const email = '<EMAIL>';
      const assessmentIds = ['id1', 'id2'];

      const filter = AssessmentMetricsNormalizer.getCandidateAssessmentsFilter(
        email,
        assessmentIds
      );

      expect(filter).to.deep.equal({
        email,
        assessmentId: { in: assessmentIds },
        testResults: { some: {} },
      });
    });
  });

  describe('getAssessmentTakerFilter', () => {
    it('should return correct filter for assessment taker', () => {
      const assessmentTakerId = 'taker-id';
      const organizationId = 'org-id';

      const filter = AssessmentMetricsNormalizer.getAssessmentTakerFilter(
        assessmentTakerId,
        organizationId
      );

      expect(filter).to.deep.equal({
        AND: [
          { id: assessmentTakerId, organizationId },
          { email: { not: null } },
          { testResults: { some: {} } },
        ],
      });
    });
  });

  describe('normalizeCandidateData', () => {
    it('should normalize candidate with test results', () => {
      const candidate = {
        id: 'candidate-1',
        email: '<EMAIL>',
        status: 'COMPLETED',
        startTime: new Date('2023-01-01T10:00:00Z'),
        endTime: new Date('2023-01-01T11:00:00Z'),
        duration: 3600,
        testResults: [
          { totalPassedScore: 80, totalScore: 100 },
          { totalPassedScore: 70, totalScore: 100 },
        ],
      };

      const normalized =
        AssessmentMetricsNormalizer.normalizeCandidateData(candidate);

      expect(normalized).to.deep.equal({
        candidateId: 'candidate-1',
        candidateName: 'test',
        candidateEmail: '<EMAIL>',
        assessmentStatus: 'COMPLETED',
        startTime: new Date('2023-01-01T10:00:00Z'),
        endTime: new Date('2023-01-01T11:00:00Z'),
        duration: 3600,
        totalPassedScore: 150,
        totalScore: 200,
        overallPercentage: 75,
        hasTestResults: true,
      });
    });

    it('should normalize candidate without test results using fallback scores', () => {
      const candidate = {
        id: 'candidate-2',
        email: '<EMAIL>',
        status: 'INCOMPLETE',
        startTime: new Date('2023-01-01T10:00:00Z'),
        endTime: null,
        duration: 1800,
        assessmentTakerScorePercentage: 65,
        assessmentTakerScore: 65,
        assessmentScore: 100,
        testResults: [],
      };

      const normalized =
        AssessmentMetricsNormalizer.normalizeCandidateData(candidate);

      expect(normalized).to.deep.equal({
        candidateId: 'candidate-2',
        candidateName: 'test2',
        candidateEmail: '<EMAIL>',
        assessmentStatus: 'INCOMPLETE',
        startTime: new Date('2023-01-01T10:00:00Z'),
        endTime: null,
        duration: 1800,
        totalPassedScore: 65,
        totalScore: 100,
        overallPercentage: 65,
        hasTestResults: false,
      });
    });

    it('should handle candidate with unknown email format', () => {
      const candidate = {
        id: 'candidate-3',
        email: null,
        status: 'IN_PROGRESS',
        testResults: [],
      };

      const normalized =
        AssessmentMetricsNormalizer.normalizeCandidateData(candidate);

      expect(normalized.candidateName).to.equal('Unknown');
      expect(normalized.candidateEmail).to.be.null;
    });
  });

  describe('calculateAssessmentMetrics', () => {
    let mockCandidates: NormalizedCandidate[];

    beforeEach(() => {
      mockCandidates = [
        {
          candidateId: '1',
          candidateName: 'John',
          candidateEmail: '<EMAIL>',
          assessmentStatus: 'COMPLETED',
          startTime: new Date(),
          endTime: new Date(),
          duration: 3600,
          totalPassedScore: 80,
          totalScore: 100,
          overallPercentage: 80,
          hasTestResults: true,
        },
        {
          candidateId: '2',
          candidateName: 'Jane',
          candidateEmail: '<EMAIL>',
          assessmentStatus: 'INCOMPLETE',
          startTime: new Date(),
          endTime: null,
          duration: 1800,
          totalPassedScore: 60,
          totalScore: 100,
          overallPercentage: 60,
          hasTestResults: true, // INCOMPLETE but has test results (system submitted)
        },
        {
          candidateId: '3',
          candidateName: 'Bob',
          candidateEmail: '<EMAIL>',
          assessmentStatus: 'INCOMPLETE',
          startTime: new Date(),
          endTime: null,
          duration: 900,
          totalPassedScore: 0,
          totalScore: 0,
          overallPercentage: 0,
          hasTestResults: false, // INCOMPLETE without test results (just registered)
        },
        {
          candidateId: '4',
          candidateName: 'Alice',
          candidateEmail: '<EMAIL>',
          assessmentStatus: 'IN_PROGRESS',
          startTime: new Date(),
          endTime: null,
          duration: 1200,
          totalPassedScore: 0,
          totalScore: 0,
          overallPercentage: 0,
          hasTestResults: false,
        },
      ];
    });

    it('should calculate comprehensive assessment metrics including candidatesWhoTookTest', () => {
      const metrics =
        AssessmentMetricsNormalizer.calculateAssessmentMetrics(mockCandidates);

      expect(metrics).to.deep.equal({
        totalCandidates: 4,
        completedCandidates: 1,
        incompleteCandidates: 2,
        inProgressCandidates: 1,
        candidatesWithResults: 2,
        candidatesWhoTookTest: 2, // COMPLETED (1) + INCOMPLETE with test results (1)
        averageScore: 35, // (80 + 60 + 0 + 0) / 4 = 35
        averagePercentage: 35,
        totalPassedScore: 140, // 80 + 60 + 0 + 0
        totalMaxScore: 200, // 100 + 100 + 0 + 0
      });
    });

    it('should handle empty candidates array', () => {
      const metrics = AssessmentMetricsNormalizer.calculateAssessmentMetrics(
        []
      );

      expect(metrics).to.deep.equal({
        totalCandidates: 0,
        completedCandidates: 0,
        incompleteCandidates: 0,
        inProgressCandidates: 0,
        candidatesWithResults: 0,
        candidatesWhoTookTest: 0,
        averageScore: 0,
        averagePercentage: 0,
        totalPassedScore: 0,
        totalMaxScore: 0,
      });
    });
  });

  describe('calculateCandidatesWhoTookTestMetrics', () => {
    it('should calculate metrics only for candidates who actually took the test', () => {
      const candidates: NormalizedCandidate[] = [
        {
          candidateId: '1',
          candidateName: 'Completed',
          candidateEmail: '<EMAIL>',
          assessmentStatus: 'COMPLETED',
          startTime: new Date(),
          endTime: new Date(),
          duration: 3600,
          totalPassedScore: 90,
          totalScore: 100,
          overallPercentage: 90,
          hasTestResults: true,
        },
        {
          candidateId: '2',
          candidateName: 'IncompleteWithResults',
          candidateEmail: '<EMAIL>',
          assessmentStatus: 'INCOMPLETE',
          startTime: new Date(),
          endTime: null,
          duration: 2400,
          totalPassedScore: 70,
          totalScore: 100,
          overallPercentage: 70,
          hasTestResults: true, // System submitted due to technical issues
        },
        {
          candidateId: '3',
          candidateName: 'IncompleteNoResults',
          candidateEmail: '<EMAIL>',
          assessmentStatus: 'INCOMPLETE',
          startTime: new Date(),
          endTime: null,
          duration: 300,
          totalPassedScore: 0,
          totalScore: 0,
          overallPercentage: 0,
          hasTestResults: false, // Just registered, never took test
        },
        {
          candidateId: '4',
          candidateName: 'InProgress',
          candidateEmail: '<EMAIL>',
          assessmentStatus: 'IN_PROGRESS',
          startTime: new Date(),
          endTime: null,
          duration: 1200,
          totalPassedScore: 0,
          totalScore: 0,
          overallPercentage: 0,
          hasTestResults: false,
        },
      ];

      const metrics =
        AssessmentMetricsNormalizer.calculateCandidatesWhoTookTestMetrics(
          candidates
        );

      // Should only include candidates 1 and 2 (COMPLETED + INCOMPLETE with test results)
      expect(metrics.totalCandidates).to.equal(2);
      expect(metrics.completedCandidates).to.equal(1);
      expect(metrics.incompleteCandidates).to.equal(1);
      expect(metrics.candidatesWhoTookTest).to.equal(2);
      expect(metrics.averageScore).to.equal(80); // (90 + 70) / 2 = 80
      expect(metrics.totalPassedScore).to.equal(160); // 90 + 70
      expect(metrics.totalMaxScore).to.equal(200); // 100 + 100
    });
  });

  describe('calculateCompletedCandidateMetrics', () => {
    it('should calculate metrics only for completed candidates', () => {
      const candidates: NormalizedCandidate[] = [
        {
          candidateId: '1',
          candidateName: 'Completed1',
          candidateEmail: '<EMAIL>',
          assessmentStatus: 'COMPLETED',
          startTime: new Date(),
          endTime: new Date(),
          duration: 3600,
          totalPassedScore: 85,
          totalScore: 100,
          overallPercentage: 85,
          hasTestResults: true,
        },
        {
          candidateId: '2',
          candidateName: 'Incomplete',
          candidateEmail: '<EMAIL>',
          assessmentStatus: 'INCOMPLETE',
          startTime: new Date(),
          endTime: null,
          duration: 1800,
          totalPassedScore: 60,
          totalScore: 100,
          overallPercentage: 60,
          hasTestResults: true,
        },
        {
          candidateId: '3',
          candidateName: 'Completed2',
          candidateEmail: '<EMAIL>',
          assessmentStatus: 'COMPLETED',
          startTime: new Date(),
          endTime: new Date(),
          duration: 3600,
          totalPassedScore: 95,
          totalScore: 100,
          overallPercentage: 95,
          hasTestResults: true,
        },
      ];

      const metrics =
        AssessmentMetricsNormalizer.calculateCompletedCandidateMetrics(
          candidates
        );

      // Should only include the 2 completed candidates
      expect(metrics.totalCandidates).to.equal(2);
      expect(metrics.completedCandidates).to.equal(2);
      expect(metrics.incompleteCandidates).to.equal(0);
      expect(metrics.candidatesWhoTookTest).to.equal(2);
      expect(metrics.averageScore).to.equal(90); // (85 + 95) / 2 = 90
    });
  });

  describe('calculateCandidatesWithResultsMetrics', () => {
    it('should calculate metrics only for candidates with test results', () => {
      const candidates: NormalizedCandidate[] = [
        {
          candidateId: '1',
          candidateName: 'WithResults1',
          candidateEmail: '<EMAIL>',
          assessmentStatus: 'COMPLETED',
          startTime: new Date(),
          endTime: new Date(),
          duration: 3600,
          totalPassedScore: 80,
          totalScore: 100,
          overallPercentage: 80,
          hasTestResults: true,
        },
        {
          candidateId: '2',
          candidateName: 'NoResults',
          candidateEmail: '<EMAIL>',
          assessmentStatus: 'INCOMPLETE',
          startTime: new Date(),
          endTime: null,
          duration: 300,
          totalPassedScore: 0,
          totalScore: 0,
          overallPercentage: 0,
          hasTestResults: false,
        },
        {
          candidateId: '3',
          candidateName: 'WithResults2',
          candidateEmail: '<EMAIL>',
          assessmentStatus: 'INCOMPLETE',
          startTime: new Date(),
          endTime: null,
          duration: 2400,
          totalPassedScore: 70,
          totalScore: 100,
          overallPercentage: 70,
          hasTestResults: true,
        },
      ];

      const metrics =
        AssessmentMetricsNormalizer.calculateCandidatesWithResultsMetrics(
          candidates
        );

      // Should only include candidates with hasTestResults: true
      expect(metrics.totalCandidates).to.equal(2);
      expect(metrics.candidatesWithResults).to.equal(2);
      expect(metrics.averageScore).to.equal(75); // (80 + 70) / 2 = 75
    });
  });

  describe('normalizeTestData', () => {
    it('should normalize test result data correctly', () => {
      const testResult = {
        testId: 'test-123',
        title: 'JavaScript Test',
        totalPassedScore: 85,
        totalScore: 100,
        testPercentage: 85,
        numberOfQuestions: 10,
        result: [
          { isAnswered: true, isAnswerCorrect: true },
          { isAnswered: true, isAnswerCorrect: false },
          { isAnswered: true, isAnswerCorrect: true },
          { isAnswered: false, isAnswerCorrect: false },
          { isAnswered: true, isAnswerCorrect: true },
        ],
      };

      const normalized =
        AssessmentMetricsNormalizer.normalizeTestData(testResult);

      expect(normalized).to.deep.equal({
        testId: 'test-123',
        testTitle: 'JavaScript Test',
        testScore: 85,
        maxTestScore: 100,
        testPercentage: 85,
        questionsAnswered: 4, // 4 questions were answered
        questionCount: 5, // Uses result.length (5) instead of numberOfQuestions (10)
        questionsPassed: 3, // 3 correct answers
        questionsFailed: 1, // 4 answered - 3 passed = 1 failed
      });
    });

    it('should handle test result with missing result array', () => {
      const testResult = {
        testId: 'test-456',
        title: 'Python Test',
        totalPassedScore: 0,
        totalScore: 0,
        testPercentage: 0,
        numberOfQuestions: 5,
        result: null,
      };

      const normalized =
        AssessmentMetricsNormalizer.normalizeTestData(testResult);

      expect(normalized.questionsAnswered).to.equal(0);
      expect(normalized.questionCount).to.equal(5);
      expect(normalized.questionsPassed).to.equal(0);
      expect(normalized.questionsFailed).to.equal(0);
    });
  });

  describe('calculateAssessmentLinksStats', () => {
    it('should calculate stats like getAssessmentLinks method', () => {
      const allCandidates = [
        {
          status: 'COMPLETED',
          assessmentTakerScorePercentage: 85,
        },
        {
          status: 'INCOMPLETE',
          assessmentTakerScorePercentage: 60,
        },
        {
          status: 'COMPLETED',
          assessmentTakerScorePercentage: 95,
        },
        {
          status: 'IN_PROGRESS',
          assessmentTakerScorePercentage: 0,
        },
      ];

      const passMark = 70;
      const totalInvited = 10;

      const stats = AssessmentMetricsNormalizer.calculateAssessmentLinksStats(
        allCandidates,
        passMark,
        totalInvited
      );

      expect(stats).to.deep.equal({
        averageScore: '80.00', // (85 + 60 + 95) / 3 = 240/3 = 80.00
        totalCandidatesInvited: 10,
        totalCandidatesCompleted: 2, // Only COMPLETED status
        passRate: '66.67', // 2 out of 3 (85>=70, 95>=70) = 66.67%
        completionRate: '20.00', // 2 completed out of 10 invited = 20%
        passMark: 70,
      });
    });

    it('should handle empty candidates array', () => {
      const stats = AssessmentMetricsNormalizer.calculateAssessmentLinksStats(
        [],
        70,
        5
      );

      expect(stats).to.deep.equal({
        averageScore: '0.00',
        totalCandidatesInvited: 5,
        totalCandidatesCompleted: 0,
        passRate: '0.00',
        completionRate: '0.00',
        passMark: 70,
      });
    });
  });

  describe('Edge Cases', () => {
    it('should handle candidates with zero scores correctly', () => {
      const candidates: NormalizedCandidate[] = [
        {
          candidateId: '1',
          candidateName: 'ZeroScore',
          candidateEmail: '<EMAIL>',
          assessmentStatus: 'COMPLETED',
          startTime: new Date(),
          endTime: new Date(),
          duration: 3600,
          totalPassedScore: 0,
          totalScore: 100,
          overallPercentage: 0,
          hasTestResults: true,
        },
      ];

      const metrics =
        AssessmentMetricsNormalizer.calculateAssessmentMetrics(candidates);

      expect(metrics.averageScore).to.equal(0);
      expect(metrics.totalPassedScore).to.equal(0);
      expect(metrics.totalMaxScore).to.equal(100);
    });

    it('should handle candidates with null/undefined values', () => {
      const candidate = {
        id: 'test-id',
        email: '<EMAIL>',
        status: 'COMPLETED',
        startTime: null,
        endTime: null,
        duration: null,
        assessmentTakerScorePercentage: null,
        assessmentTakerScore: null,
        assessmentScore: null,
        testResults: null,
      };

      const normalized =
        AssessmentMetricsNormalizer.normalizeCandidateData(candidate);

      expect(normalized.startTime).to.be.null;
      expect(normalized.endTime).to.be.null;
      expect(normalized.duration).to.equal(0);
      expect(normalized.totalPassedScore).to.equal(0);
      expect(normalized.totalScore).to.equal(0);
      expect(normalized.overallPercentage).to.equal(0);
      expect(normalized.hasTestResults).to.be.false;
    });
  });
});
