import { expect } from 'chai';
import { timeFormat } from '../../src/helpers/timeFormat'; // Adjust the import path as needed

describe('timeFormat', () => {
  it('should format 0 seconds correctly', () => {
    expect(timeFormat(0)).to.equal('00:00:00');
  });

  it('should format seconds less than a minute', () => {
    expect(timeFormat(30)).to.equal('00:00:30');
    expect(timeFormat(59)).to.equal('00:00:59');
  });

  it('should format minutes correctly', () => {
    expect(timeFormat(60)).to.equal('00:01:00');
    expect(timeFormat(119)).to.equal('00:01:59');
    expect(timeFormat(3599)).to.equal('00:59:59');
  });

  it('should format hours correctly', () => {
    expect(timeFormat(3600)).to.equal('01:00:00');
    expect(timeFormat(3661)).to.equal('01:01:01');
    expect(timeFormat(86399)).to.equal('23:59:59');
  });

  it('should handle large hour values', () => {
    expect(timeFormat(90000)).to.equal('25:00:00');
    expect(timeFormat(123456)).to.equal('34:17:36');
  });

  it('should pad single-digit values with leading zeros', () => {
    expect(timeFormat(5)).to.equal('00:00:05');
    expect(timeFormat(65)).to.equal('00:01:05');
    expect(timeFormat(3605)).to.equal('01:00:05');
  });
});
