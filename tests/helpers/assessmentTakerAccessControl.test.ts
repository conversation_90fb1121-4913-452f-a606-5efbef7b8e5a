import { expect } from 'chai';
import sinon from 'sinon';
import proxyquire from 'proxyquire';
import { RequestExtension, ErrorMessage } from '../../src/helpers/types';
import { AppError, HttpCode } from '../../src/middlewares/errorHandler';
import { Response } from 'express';

describe('Assessment Middleware Tests', () => {
  let req: RequestExtension;
  let res: Response;
  let next: sinon.SinonStub;
  let status: sinon.SinonStub;
  let json: sinon.SinonSpy;

  let prismaMock;
  let redisCacheMock;
  let assessmentTakerAccessControl;

  beforeEach(() => {
    req = {
      params: {},
      headers: {},
      url: '',
      method: 'GET',
    } as unknown as RequestExtension;

    status = sinon.stub();
    json = sinon.spy();
    res = {
      status,
      json,
    } as unknown as Response;
    status.returns(res);

    next = sinon.stub();

    prismaMock = {
      assessmentTaker: {
        findFirst: sinon.stub(),
      },
    };

    redisCacheMock = {
      getCachedData: sinon.stub(),
      setCacheData: sinon.stub(),
    };

    assessmentTakerAccessControl = proxyquire(
      '../../src/middlewares/assessmentTakerAccessContol',
      {
        '../../src/prisma': { default: prismaMock },
        '../../src/helpers/redisCache': redisCacheMock,
      }
    );
  });

  afterEach(() => {
    sinon.restore();
  });

  describe('assessmentTakerAccessPermission', () => {
    it('should return 403 when assessmentTakerId is not a valid UUID', async () => {
      req.params.assessmentTakerId = 'invalid-uuid';

      await assessmentTakerAccessControl.assessmentTakerAccessPermission(
        req,
        res,
        next
      );

      expect(status.calledWith(HttpCode.FORBIDDEN)).to.be.true;
      expect(
        json.calledWithMatch({
          success: false,
          message: ErrorMessage.CANDIDATE_NOT_FOUND,
        })
      ).to.be.true;
    });

    it('should proceed when accessing assessment results in the correct phase', async () => {
      req.params.assessmentTakerId = '123e4567-e89b-12d3-a456-************';
      req.url = '/assessment-result';
      req.method = 'GET';
      prismaMock.assessmentTaker.findFirst.resolves({
        phase: 'ASSESSMENT_COMPLETION',
      });

      await assessmentTakerAccessControl.assessmentTakerAccessPermission(
        req,
        res,
        next
      );

      expect(next.calledOnce).to.be.true;
    });

    it('should proceed when submitting a survey in the correct phase', async () => {
      req.params.assessmentTakerId = '123e4567-e89b-12d3-a456-************';
      req.url = '/survey';
      req.method = 'POST';
      prismaMock.assessmentTaker.findFirst.resolves({
        phase: 'ASSESSMENT_IN_PROGRESS',
      });

      await assessmentTakerAccessControl.assessmentTakerAccessPermission(
        req,
        res,
        next
      );

      expect(next.calledOnce).to.be.true;
    });
  });

  describe('fingerprintValidation', () => {
    it('should throw error when fingerprint header is missing', async () => {
      req.params.assessmentTakerId = '123e4567-e89b-12d3-a456-************';

      await assessmentTakerAccessControl.fingerprintValidation(req, res, next);

      const error = next.args[0][0];
      expect(error).to.be.instanceOf(AppError);
      expect(error.httpCode).to.equal(HttpCode.FORBIDDEN);
    });

    it('should throw error when assessmentTakerId is missing', async () => {
      req.headers['x-device-fingerprint'] = 'test-fingerprint';

      await assessmentTakerAccessControl.fingerprintValidation(req, res, next);

      const error = next.args[0][0];
      expect(error).to.be.instanceOf(AppError);
      expect(error.httpCode).to.equal(HttpCode.FORBIDDEN);
    });

    it('should store fingerprint and call next() when fingerprint is not stored yet', async () => {
      const fingerprint = 'test-fingerprint';
      const assessmentTakerId = '123e4567-e89b-12d3-a456-************';
      req.headers['x-device-fingerprint'] = fingerprint;
      req.params.assessmentTakerId = assessmentTakerId;
      redisCacheMock.getCachedData.resolves(null);
      redisCacheMock.setCacheData.resolves();

      await assessmentTakerAccessControl.fingerprintValidation(req, res, next);

      expect(redisCacheMock.getCachedData.calledOnce).to.be.true;
      expect(redisCacheMock.setCacheData.calledOnce).to.be.false;
    });

    it('should call next() when fingerprint matches stored one', async () => {
      const fingerprint = 'test-fingerprint';
      const assessmentTakerId = '123e4567-e89b-12d3-a456-************';
      req.headers['x-device-fingerprint'] = fingerprint;
      req.params.assessmentTakerId = assessmentTakerId;
      redisCacheMock.getCachedData.resolves(fingerprint);

      await assessmentTakerAccessControl.fingerprintValidation(req, res, next);

      expect(redisCacheMock.getCachedData.calledOnce).to.be.true;
      expect(next.calledOnce).to.be.true;
    });

    it('should pass any error to next() when redis operations fail', async () => {
      req.headers['x-device-fingerprint'] = 'test-fingerprint';
      req.params.assessmentTakerId = '123e4567-e89b-12d3-a456-************';
      const testError = new Error('Redis error');
      redisCacheMock.getCachedData.rejects(testError);

      await assessmentTakerAccessControl.fingerprintValidation(req, res, next);

      expect(next.calledOnce).to.be.true;
      expect(next.args[0][0]).to.equal(testError);
    });
  });
});
