import { expect } from 'chai';
import sinon from 'sinon';
import proxyquire from 'proxyquire';

describe('RetakeDelay', () => {
  let prismaStub: sinon.SinonStub;
  let RetakeDelay: any;

  before(() => {
    // Create mock prisma
    const mockPrisma = {
      assessmentTaker: {
        findMany: sinon.stub(),
      },
    };

    // Use proxyquire to inject the mock
    const RetakeDelayModule = proxyquire('../../src/helpers/retakeDelay', {
      '../prisma': { default: mockPrisma },
    });

    RetakeDelay = RetakeDelayModule.RetakeDelay;
    prismaStub = mockPrisma.assessmentTaker.findMany;
  });

  beforeEach(() => {
    // Reset stub
    prismaStub.reset();
  });

  afterEach(() => {
    sinon.restore();
  });

  // Global retake delay methods removed - now using assessment-specific delays only

  describe('getRetakeDelayHours', () => {
    it('should return assessment-specific retake delay when provided', () => {
      const result = RetakeDelay.getRetakeDelayHours(1200); // 50 days
      expect(result).to.equal(1200);
    });

    it('should return 0 when no retake delay provided', () => {
      const result = RetakeDelay.getRetakeDelayHours();
      expect(result).to.equal(0);
    });

    it('should return 0 when retakeDelayHours is 0', () => {
      const result = RetakeDelay.getRetakeDelayHours(0);
      expect(result).to.equal(0);
    });

    it('should return 0 when retakeDelayHours is undefined', () => {
      const result = RetakeDelay.getRetakeDelayHours(undefined);
      expect(result).to.equal(0);
    });
  });

  describe('canRetakeAssessment', () => {
    let originalArgv: string[];
    let originalNodeEnv: string | undefined;
    let originalCI: string | undefined;

    beforeEach(() => {
      // Store original environment variables and argv
      originalArgv = [...process.argv];
      originalNodeEnv = process.env.NODE_ENV;
      originalCI = process.env.CI;

      // Modify environment to not be detected as test environment
      process.argv = ['node', 'script.js'];
      process.env.NODE_ENV = 'production';
      delete process.env.CI;
      delete process.env.JENKINS_URL;
    });

    afterEach(() => {
      // Restore original environment
      process.argv = originalArgv;
      if (originalNodeEnv !== undefined) {
        process.env.NODE_ENV = originalNodeEnv;
      } else {
        delete process.env.NODE_ENV;
      }
      if (originalCI !== undefined) {
        process.env.CI = originalCI;
      }
    });

    it('should return true when no previous attempts exist', async () => {
      prismaStub.resolves([]);

      const result = await RetakeDelay.canRetakeAssessment(
        '<EMAIL>',
        'assessment-123',
        48 // retake delay hours
      );

      expect(result).to.be.true;
    });

    it('should return false when delay period has not passed', async () => {
      const oneHourAgo = new Date(Date.now() - 1 * 60 * 60 * 1000);

      prismaStub.resolves([
        {
          endTime: oneHourAgo,
          status: 'COMPLETED',
        },
      ]);

      const result = await RetakeDelay.canRetakeAssessment(
        '<EMAIL>',
        'assessment-123',
        48 // retake delay hours
      );

      expect(result).to.be.false;
    });

    it('should return true when delay period has passed', async () => {
      const fiftyHoursAgo = new Date(Date.now() - 50 * 60 * 60 * 1000);
      prismaStub.resolves([
        {
          endTime: fiftyHoursAgo,
          status: 'COMPLETED',
        },
      ]);

      const result = await RetakeDelay.canRetakeAssessment(
        '<EMAIL>',
        'assessment-123',
        48 // retake delay hours
      );

      expect(result).to.be.true;
    });

    it('should return true when no retake delay is specified', async () => {
      prismaStub.resolves([]);

      const result = await RetakeDelay.canRetakeAssessment(
        '<EMAIL>',
        'assessment-123',
        0 // no retake delay
      );

      expect(result).to.be.true;
    });
  });
});
