import axios from 'axios';
import sinon from 'sinon';
import { expect } from 'chai';
import {
  fetchCandidateDetails,
  fetchSurveyStatements,
} from '../../src/helpers/fetchCandidateDetails';
import { AppError, HttpCode } from '../../src/middlewares/errorHandler';

describe('fetchCandidateDetails', () => {
  let axiosGetStub: sinon.SinonStub;

  beforeEach(() => {
    axiosGetStub = sinon.stub(axios, 'get');
  });

  afterEach(() => {
    sinon.restore();
  });

  it('should return candidate details data when request is successful', async () => {
    // Define mock data
    const mockData = { id: '1', name: 'Test Candidate' };
    axiosGetStub.resolves({ data: { data: mockData } });

    const result = await fetchCandidateDetails('1');

    expect(axiosGetStub.calledOnce).to.be.true;
    expect(result).to.deep.equal(mockData);
  });

  it('should throw AppError with response status and message when server returns error response', async () => {
    // Define error response
    const errorResponse = {
      response: {
        status: HttpCode.NOT_FOUND,
        data: { message: 'Candidate not found' },
      },
    };
    axiosGetStub.rejects(errorResponse);

    try {
      await fetchCandidateDetails('invalid-id');
    } catch (err) {
      expect(err).to.be.instanceOf(AppError);
      expect(err.httpCode).to.equal(HttpCode.NOT_FOUND);
      expect(err.message).to.equal('Candidate not found');
    }
  });

  it('should throw AppError with BAD_REQUEST when request fails', async () => {
    const errorRequest = { request: {} };
    axiosGetStub.rejects(errorRequest);

    try {
      await fetchCandidateDetails('1');
    } catch (err) {
      expect(err).to.be.instanceOf(AppError);
      expect(err.httpCode).to.equal(HttpCode.BAD_REQUEST);
      expect(err.message).to.equal('Request failed, check request details');
    }
  });

  it('should throw AppError with INTERNAL_SERVER_ERROR for other errors', async () => {
    const errorOther = new Error('Unexpected error');
    axiosGetStub.rejects(errorOther);

    try {
      await fetchCandidateDetails('1');
    } catch (err) {
      expect(err).to.be.instanceOf(AppError);
      expect(err.httpCode).to.equal(HttpCode.INTERNAL_SERVER_ERROR);
      expect(err.message).to.equal('Error getting test taker details details');
    }
  });
});

describe('fetchSurveyStatements', () => {
  let axiosGetStub;

  beforeEach(() => {
    axiosGetStub = sinon.stub(axios, 'get');
  });

  afterEach(() => {
    sinon.restore();
  });

  it('should return survey statements data when request is successful', async () => {
    const mockData = { id: '1', statement: 'Test Statement' };
    axiosGetStub.resolves({ data: { data: mockData } });

    const result = await fetchSurveyStatements('1');

    expect(axiosGetStub.calledOnce).to.be.true;
    expect(result).to.deep.equal(mockData);
  });

  it('should throw AppError with response status and message when server returns error response', async () => {
    const errorResponse = {
      response: {
        status: HttpCode.NOT_FOUND,
        data: { message: 'Survey not found' },
      },
    };
    axiosGetStub.rejects(errorResponse);

    try {
      await fetchSurveyStatements('invalid-id');
    } catch (err) {
      expect(err).to.be.instanceOf(AppError);
      expect(err.httpCode).to.equal(HttpCode.NOT_FOUND);
      expect(err.message).to.equal('Survey not found');
    }
  });

  it('should throw AppError with BAD_REQUEST when request fails', async () => {
    const errorRequest = { request: {} };
    axiosGetStub.rejects(errorRequest);

    try {
      await fetchSurveyStatements('1');
    } catch (err) {
      expect(err).to.be.instanceOf(AppError);
      expect(err.httpCode).to.equal(HttpCode.BAD_REQUEST);
      expect(err.message).to.equal('Request failed, check request details');
    }
  });

  it('should throw AppError with INTERNAL_SERVER_ERROR for other errors', async () => {
    const errorOther = new Error('Unexpected error');
    axiosGetStub.rejects(errorOther);

    try {
      await fetchSurveyStatements('1');
    } catch (err) {
      expect(err).to.be.instanceOf(AppError);
      expect(err.httpCode).to.equal(HttpCode.INTERNAL_SERVER_ERROR);
      expect(err.message).to.equal('Error survey statements');
    }
  });
});
