import {
  AssessmentTaker,
  Identity,
  PassStatus,
  QuestionFlag,
  TestSubmissionStatus,
} from '@prisma/client'; // Import TestSubmissionStatus
import {
  MatrixSubquestion,
  Question,
  TCandidateData,
} from '../../src/dtos/assessmentTakerDtos';
import { AnswerStatus } from '../../src/helpers/types';

export const questionflagResponse = {
  id: 'flagged question Id',
  organizationId: '1bf5694a-8452-4811-818f-dc6293f634ad',
  assessmentId: '963b50c4-817a-449f-82ae-5a9c029ba11e',
  testTakerId: '725186e0-36d2-4484-843c-2febf4fa115b',
  testId: '49df0ae4-e4c8-46f5-b2f8-319ca43bcf27',
  questionId: 'cfd46de2-29b0-4700-bb54-542d043cd4d8',
  questionText: 'What is React?',
  reasonOfFlagging: 'My reason',
  testTakerEmail: '<EMAIL>',
  createdAt: new Date(),
  updatedAt: new Date(),
};

export const fakeIdentity: Identity = {
  id: 'fake-identity-id',
  linkId: 'fake-link-id',
  linkHead: 'fake-link-head',
  assessmentTakerId: 'fake-assessment-taker-id',
  createdAt: new Date(),
  updatedAt: new Date(),
};

export const fakeFlaggedQuestion: QuestionFlag = {
  id: 'flagged question Id',
  organizationId: 'fake-organization-id',
  assessmentId: 'fake-assessment-id',
  testTakerId: 'fake-test-taker-id',
  testId: 'fake-test-id',
  questionId: 'fake-question-id',
  testTakerEmail: 'fake-test-taker-email',
  reasonOfFlagging: 'fake-reason-of-flagging',
  createdAt: new Date(),
  updatedAt: new Date(),
  questionText: 'What is React?',
};

export const fakeAssessmentTakerResult = {
  id: '848da31f-2a51-45ec-b8af-805f6aaa671a',
  assessmentName: '5 minute assessment for duplicates',
  overallAssessmentPercentage: 2.13,
  overallAssessmentPassScore: 1,
  overallAssessmentScore: 47,
  showResults: true as const,
  startTime: new Date('2024-10-22T12:31:48.523Z'),
  endTime: new Date('2024-10-22T12:32:06.107Z'),
  assessmentDuration: 480,
  assessmenTakerDuration: 14,
  testResults: [
    {
      testId: '453609ad-5295-4246-a243-320956023aa2',
      title: 'Title Comprehension',
      startTime: new Date('2024-10-22T12:31:48.531Z'),
      finishTime: new Date('2024-10-22T12:32:00.338Z'),
      testPercentage: 0,
      totalScore: 46,
      totalPassedScore: 0,
      duration: 11,
      numberOfQuestions: 2,
      numberOfQuestionsPassed: 0,
      numberOfQuestionsAnswered: 2,
      numberOfQuestionsFailed: 2,
      passStatus: PassStatus.FAIL, // Use enum
      status: TestSubmissionStatus.SUBMITTED, // Use enum
    },
    {
      testId: '9de6462a-dcb2-44a7-9581-822d6c3c3e15',
      title: 'One Minutes Test',
      startTime: new Date('2024-10-22T12:32:02.376Z'),
      finishTime: new Date('2024-10-22T12:32:05.549Z'),
      testPercentage: 100,
      totalScore: 1,
      totalPassedScore: 1,
      duration: 3,
      numberOfQuestions: 1,
      numberOfQuestionsPassed: 1,
      numberOfQuestionsAnswered: 1,
      numberOfQuestionsFailed: 0,
      passStatus: PassStatus.PASS, // Use enum
      status: TestSubmissionStatus.SUBMITTED, // Use enum
    },
  ],
};

export const fakeSurveyResponse = {
  testTakerId: 'fake-test-taker-id',
  takerEmail: 'fake-test-taker-email',
  organizationId: 'fake-organization-id',
  assessmentId: 'fake-assessment-id',
  surveyQuestions: [
    {
      questionText: 'What is React?',
      questionAnswer:
        'React is a JavaScript library for building user interfaces.',
      type: 'text',
    },
    {},
  ],
};

export const fakeShotResponse = [
  {
    id: 1,
    imageURL: 'https://fake-screenshot-url.com',
    isViolationShot: true,
    isIntegrityShot: false,
    createdAt: new Date(),
  },
  {
    id: 2,
    imageURL: 'https://fake-screenshot-url.com',
    isViolationShot: false,
    isIntegrityShot: true,
    createdAt: new Date(),
  },
  {
    id: 3,
    imageURL: 'https://fake-screenshot-url.com',
    isViolationShot: false,
    isIntegrityShot: false,
    createdAt: new Date(),
  },
];

export const sampleTestMarkingResponse = {
  testTakerScore: 0,
  testTakerPercentageScore: '0%',
  testScore: 0,
  testResult: [
    {
      isAnswerCorrect: false,
      scored: 0,
      isAnswered: false,
      testTakerAnswers: [],
      questionType: 'text',
      score: 0,
      answer: [''],
      testTakerAnswer: [''],
    },
    {
      isAnswerCorrect: false,
      scored: 0,
      isAnswered: false,
      testTakerAnswers: [],
      questionType: 'matrix',
      score: 0,
      answer: [''],
      testTakerAnswer: [''],
    },
  ],
};

export const fakeAssessmentTaker = {
  id: '2be65206-8e4a-44cb-8a99-6174a755efee',
  assessmentId: '3f9998c6-9387-49ae-8f6e-2377ba220223',
  assessmentName: 'Second One Minutes Assessment',
  dispatcher: '7e1b7485-ec04-4c1e-b97d-d4bd58dfc153',
  showResults: true,
  organizationId: '1bf5694a-8452-4811-818f-dc6293f634ad',
  email: '<EMAIL>',
  assessmentLink:
    'https://amap-v2.amalitech-dev.net/test-taker/system-instruction?organizationId=1bf5694a-8452-4811-818f-dc6293f634ad&assessmentId=3f9998c6-9387-49ae-8f6e-2377ba220223&testTakerId=2be65206-8e4a-44cb-8a99-6174a755efee',
  assessmentWindowViolationCount: 0,
  assessmentWindowViolationDuration: 0,
  assessmentTakerShotCount: 0,
  assessmentTakerViolationShotCount: 0,
  windowShotCount: 0,
  windowViolationShotCount: 0,
  assessmentDuration: 480,
  estimatedEndTime: '2024-09-10T12:50:18.180Z' as unknown as Date,
  expireDate: new Date('2024-09-11T13:48:00.000Z') as unknown as Date,
  commenceDate: '2024-09-10T12:48:29.810Z' as unknown as Date,
  startTime: '2024-09-10T12:49:18.180Z' as unknown as Date,
  endTime: '2024-09-10T12:49:23.875Z' as unknown as Date,
  proctor: null,
  conductSurvey: true,
  proctorFeatures: ['Honour Code'],
  screenshotsInterval: 15,
  camerashotsInterval: 15,
  testList: ['9de6462a-dcb2-44a7-9581-822d6c3c3e15'],
  submittedTests: ['9de6462a-dcb2-44a7-9581-822d6c3c3e15'],
  phase: 'ASSESSMENT_COMPLETION',
  linkStatus: 'VALID',
  status: 'COMPLETED',
  logSummary: ['LINK_VIEW', 'ASSESSMENT IN PROGRESS', 'ASSESSMENT_COMPLETION'],
  createdAt: '2024-09-10T12:49:05.504Z' as unknown as Date,
  updatedAt: '2024-09-10T12:49:23.902Z' as unknown as Date,
} as unknown as AssessmentTaker;

export const testCreationCandidateMockData = {
  organizationId: '1bf5694a-8452-4811-818f-dc6293f634ad',
  assessmentId: '3f9998c6-9387-49ae-8f6e-2377ba220223',
  id: '54347114-7883-4406-8d2f-9869467037bd',
  email: '<EMAIL>',
  showResults: true,
  showClock: true,
  conductSurvey: false,
  commenceDate: '2024-10-23T13:29:39.491Z' as unknown as Date,
  expireDate: '2024-10-24T17:29:00.000Z' as unknown as Date,
  assessmentLink:
    'https://amap-v2.amalitech-dev.net/test-taker/system-instruction?organizationId=1bf5694a-8452-4811-818f-dc6293f634ad&assessmentId=3f9998c6-9387-49ae-8f6e-2377ba220223&testTakerId=54347114-7883-4406-8d2f-9869467037bd',
  startTime: '0001-01-01T00:00:00.000Z',
  endTime: '0001-01-01T00:00:00.000Z',
  status: 'Viewed',
  invalid: false,
  proctor: null,
  estimatedEndTime: null,
  genericId: '86267fe8-4399-4734-8c47-19120e2cf7d0',
  screenshotsInterval: '',
  camerashotsInterval: '',
  dispatcher: '7e1b7485-ec04-4c1e-b97d-d4bd58dfc153',
  duration: null,
  mailStatus: 'Scheduled',
  submissionType: 'Pending',
  assessmentTakerScore: 'Pending',
  assessmentTakerScorePercentage: 'Pending',
  assessmentScore: 1,
  createdAt: '2024-10-23T13:34:51.256Z',
  updatedAt: '2024-10-23T13:34:52.816Z',
  reportCallbackURL: 'http://localhost:3000',
  assessment: {
    id: '3f9998c6-9387-49ae-8f6e-2377ba220223',
    title: 'Second One Minutes Assessment',
    instructions: '<p>No instruction</p>',
    showClock: true,
    conductSurvey: false,
    screenshotsInterval: null,
    camerashotsInterval: null,
    duration: 60,
    isDispatched: true,
    system: true,
    attempted: true,
    createdAt: '2024-09-10T12:47:48.247Z',
    updatedAt: '2024-10-23T09:05:51.282Z',
    proctor: null,
    organizationId: '1bf5694a-8452-4811-818f-dc6293f634ad',
    testOrder: ['9de6462a-dcb2-44a7-9581-822d6c3c3e15'],
    hash: '773020254d3d8eef2aeb9eb81bc265c52f700f6a8316e678a71e7153e1c2e2f8',
    Survey: [],
    tests: [
      {
        id: '9de6462a-dcb2-44a7-9581-822d6c3c3e15',
        title: 'One Minutes Test',
        isActivated: true,
        system: true,
        useDomainQuestions: false,
        passMark: 50,
        noOfQuestions: null,
        description: '<p>No description</p>',
        instructions: '<p>No Instruction</p>',
        passage: null,
        duration: 60,
        difficultyLevel: 'Beginner',
        organizationId: '1bf5694a-8452-4811-818f-dc6293f634ad',
        createdAt: '2024-02-16T13:57:14.906Z',
        updatedAt: '2024-10-23T09:05:41.878Z',
        domainId: '1a36ff4e-faa0-4435-80bb-d0ca008e8ca4',
        hash: '28ad4cd685c305cb1a736dab2a8652ce6024a12a88a67f62bce08b2a9bc55420',
        questions: [
          {
            id: '63d4dbc8-6aa3-4f96-9cad-a6f8a0663ea3',
            questionText:
              '<p>Thank you. For providing this for testing purposes</p>',
            isComprehension: false,
            questionType: 'Multiple_choice',
            score: 1,
            creator: null,
            createdAt: '2024-02-16T08:06:04.403Z',
            updatedAt: '2024-10-23T09:04:49.236Z',
            categoryId: '394d8f7f-2ba5-4da4-9164-91eea886c318',
            domainId: '1a36ff4e-faa0-4435-80bb-d0ca008e8ca4',
            system: true,
            organizationId: '1bf5694a-8452-4811-818f-dc6293f634ad',
            timeLimit: '10',
            isActive: true,
            difficultyLevel: 'Beginner',
            strictMark: false,
            hash: '3f60466d90db06cd9f72829bf5fc6ebbf658d823692012d388a73cd62b70bcd2',
            domain: { name: 'Programming language' },
            category: { name: 'String' },
            multipleChoiceAnswer: {
              id: 'e7d7682b-36c5-4720-b132-134bbeacdec0',
              questionId: '63d4dbc8-6aa3-4f96-9cad-a6f8a0663ea3',
              options: ['gfddfsghes', 'dffhfgdsdfgf', 'jgjkhkiiuyy'],
            },
            trueOrFalseAnswer: null,
            multipleSelectAnswer: null,
            essayAnswer: null,
            fillInAnswer: null,
            matchMatrixAnswer: null,
          },
        ],
        domain: { name: 'Programming language' },
      },
    ],
    proctorFeatures: [
      {
        id: '67f5abc1-faab-4145-8acc-bc09d096f423',
        name: 'Honour Code',
        description: 'The user is required to sign an honor code.',
        createdAt: '2023-09-18T07:45:24.331Z',
        updatedAt: '2024-07-08T13:35:00.633Z',
      },
    ],
  },
  proctorFeatures: [
    {
      id: '67f5abc1-faab-4145-8acc-bc09d096f423',
      name: 'Honour Code',
      description: 'The user is required to sign an honor code.',
      createdAt: '2023-09-18T07:45:24.331Z' as unknown as Date,
      updatedAt: '2024-07-08T13:35:00.633Z' as unknown as Date,
    },
  ],
} as unknown as TCandidateData;

export const testCreationCandidateMockMarkData = {
  organizationId: '1bf5694a-8452-4811-818f-dc6293f634ad',
  assessmentId: '3f9998c6-9387-49ae-8f6e-2377ba220223',
  id: '54347114-7883-4406-8d2f-9869467037bd',
  email: '<EMAIL>',
  showResults: true,
  showClock: true,
  conductSurvey: false,
  commenceDate: '2024-10-23T13:29:39.491Z',
  expireDate: '2024-10-24T17:29:00.000Z',
  assessmentLink:
    'https://amap-v2.amalitech-dev.net/test-taker/system-instruction?organizationId=1bf5694a-8452-4811-818f-dc6293f634ad&assessmentId=3f9998c6-9387-49ae-8f6e-2377ba220223&testTakerId=54347114-7883-4406-8d2f-9869467037bd',
  startTime: '0001-01-01T00:00:00.000Z',
  endTime: '0001-01-01T00:00:00.000Z',
  status: 'Viewed',
  invalid: false,
  proctor: null,
  estimatedEndTime: null,
  genericId: '86267fe8-4399-4734-8c47-19120e2cf7d0',
  screenshotsInterval: '',
  camerashotsInterval: '',
  dispatcher: '7e1b7485-ec04-4c1e-b97d-d4bd58dfc153',
  duration: null,
  mailStatus: 'Scheduled',
  submissionType: 'Pending',
  assessmentTakerScore: 'Pending',
  assessmentTakerScorePercentage: 'Pending',
  assessmentScore: 1,
  createdAt: '2024-10-23T13:34:51.256Z',
  updatedAt: '2024-10-23T13:34:52.816Z',
  assessment: {
    id: '3f9998c6-9387-49ae-8f6e-2377ba220223',
    title: 'Second One Minutes Assessment',
    instructions: '<p>No instruction</p>',
    showClock: true,
    conductSurvey: false,
    screenshotsInterval: null,
    camerashotsInterval: null,
    duration: 60,
    isDispatched: true,
    system: true,
    attempted: true,
    createdAt: '2024-09-10T12:47:48.247Z',
    updatedAt: '2024-10-23T09:05:51.282Z',
    proctor: null,
    organizationId: '1bf5694a-8452-4811-818f-dc6293f634ad',
    testOrder: ['9de6462a-dcb2-44a7-9581-822d6c3c3e15'],
    hash: '773020254d3d8eef2aeb9eb81bc265c52f700f6a8316e678a71e7153e1c2e2f8',
    Survey: [],
    tests: [
      {
        id: '9de6462a-dcb2-44a7-9581-822d6c3c3e15',
        title: 'One Minutes Test',
        isActivated: true,
        system: true,
        useDomainQuestions: false,
        passMark: 50,
        noOfQuestions: null,
        description: '<p>No description</p>',
        instructions: '<p>No Instruction</p>',
        passage: null,
        duration: 60,
        difficultyLevel: 'Beginner',
        organizationId: '1bf5694a-8452-4811-818f-dc6293f634ad',
        createdAt: '2024-02-16T13:57:14.906Z',
        updatedAt: '2024-10-23T09:05:41.878Z',
        domainId: '1a36ff4e-faa0-4435-80bb-d0ca008e8ca4',
        hash: '28ad4cd685c305cb1a736dab2a8652ce6024a12a88a67f62bce08b2a9bc55420',
        questions: [
          {
            id: '63d4dbc8-6aa3-4f96-9cad-a6f8a0663ea3',
            questionText:
              '<p>Thank you. For providing this for testing purposes</p>',
            isComprehension: false,
            questionType: 'Multiple_choice',
            score: 1,
            creator: null,
            createdAt: '2024-02-16T08:06:04.403Z',
            updatedAt: '2024-10-23T09:04:49.236Z',
            categoryId: '394d8f7f-2ba5-4da4-9164-91eea886c318',
            domainId: '1a36ff4e-faa0-4435-80bb-d0ca008e8ca4',
            system: true,
            organizationId: '1bf5694a-8452-4811-818f-dc6293f634ad',
            timeLimit: '10',
            isActive: true,
            difficultyLevel: 'Beginner',
            strictMark: null,
            hash: '3f60466d90db06cd9f72829bf5fc6ebbf658d823692012d388a73cd62b70bcd2',
            multipleChoiceAnswer: {
              id: 'e7d7682b-36c5-4720-b132-134bbeacdec0',
              questionId: '63d4dbc8-6aa3-4f96-9cad-a6f8a0663ea3',
              options: ['dffhfgdsdfgf', 'gfddfsghes', 'jgjkhkiiuyy'],
              answer: ['jgjkhkiiuyy'],
            },
            trueOrFalseAnswer: null,
            multipleSelectAnswer: null,
            essayAnswer: null,
            fillInAnswer: null,
            matchMatrixAnswer: null,
            category: { name: 'String' },
            domain: { name: 'Programming language' },
          },
        ],
        domain: { name: 'Programming language' },
      },
    ],
    proctorFeatures: [
      {
        id: '67f5abc1-faab-4145-8acc-bc09d096f423',
        name: 'Honour Code',
        description: 'The user is required to sign an honor code.',
        createdAt: '2023-09-18T07:45:24.331Z',
        updatedAt: '2024-07-08T13:35:00.633Z',
      },
    ],
  },
  proctorFeatures: [
    {
      id: '67f5abc1-faab-4145-8acc-bc09d096f423',
      name: 'Honour Code',
      description: 'The user is required to sign an honor code.',
      createdAt: '2023-09-18T07:45:24.331Z',
      updatedAt: '2024-07-08T13:35:00.633Z',
    },
  ],
} as unknown as TCandidateData;

export const assessmentTakerDependencyMockData = {
  assessmentTaker: { ...fakeAssessmentTaker, ...testCreationCandidateMockData },
} as unknown as Record<string, TCandidateData>;

export const assessmentTakerDependencyMockMarkData = {
  assessmentTaker: {
    ...fakeAssessmentTaker,
    ...testCreationCandidateMockMarkData,
  },
} as unknown as Record<string, TCandidateData>;

export const saveAssessmentMonitoringDataMock = {
  windowShotSummary: {
    windowTotalShots: 0,
    windowViolationTotalShots: 0,
  },
  assessmentTakerShotSummary: {
    assessmentTakerTotalShots: 0,
    assessmentTakerTotalViolationShots: 0,
  },
  windowViolationCount: 0,
  windowViolationDuration: 0,
};

export const markQuestionResultMock = {
  answerCorrect: AnswerStatus.CORRECT,

  scored: 5,
  isAnswered: true,
  testTakerAnswers: ['Option A'],
};

export const fakeDBIdentityData: Identity = {
  id: 'fake-identity-id',
  assessmentTakerId: 'fake-assessment-taker-id',
  linkId: 'fake-link-id',
  linkHead: 'fake-link-head',
  createdAt: new Date(),
  updatedAt: new Date(),
};

export const fakeScreenShotListData = [
  {
    id: 1,
    imageURL: 'https://fake-image-url-1',
    isViolationShot: true,
    isIntegrityShot: false,
    createdAt: new Date(),
  },
  {
    id: 2,
    imageURL: 'https://fake-image-url-2',
    isViolationShot: false,
    isIntegrityShot: true,
    createdAt: new Date(),
  },
  {
    id: 3,
    imageURL: 'https://fake-image-url-3',
    isViolationShot: false,
    isIntegrityShot: false,
    createdAt: new Date(),
  },
  {
    id: 4,
    imageURL: 'https://fake-image-url-4',
    isViolationShot: true,
    isIntegrityShot: true,
    createdAt: new Date(),
  },
];

export const fakeAssessmentTakerCaptureListData = [
  {
    id: 1,
    imageURL: 'https://fake-image-url-1',
    isViolationShot: true,
    createdAt: new Date(),
  },
  {
    id: 2,
    imageURL: 'https://fake-image-url-2',
    isViolationShot: false,
    createdAt: new Date(),
  },
  {
    id: 3,
    imageURL: 'https://fake-image-url-3',
    isViolationShot: false,
    createdAt: new Date(),
  },
  {
    id: 4,
    imageURL: 'https://fake-image-url-4',
    isViolationShot: true,
    createdAt: new Date(),
  },
];

export const fakeTestResult = [
  {
    passStatus: 'FAIL',
    testId: '9de6462a-dcb2-44a7-9581-822d6c3c3e15',
    title: 'One Minutes Test',
    startTime: '2024-09-10T12:49:17.309Z',
    finishTime: '2024-09-10T12:49:22.970Z',
    duration: 5,
    totalScore: 1,
    totalPassedScore: 0,
    numberOfQuestionsFailed: 1,
    numberOfQuestionsPassed: 0,
    numberOfQuestionsAnswered: 1,
    numberOfQuestions: 1,
    status: 'NOT_SUBMITTED',
    testPercentage: 0,
  },
];

export const multiSelectQuestionMarkData: Question = {
  strictMark: false,
  score: 2,
  questionType: 'Multi_select',
  multipleSelectAnswer: {
    answer: ['Option-1', 'Option-3'],
    options: ['Option-1', 'Option-2', 'Option-3', 'Option-4'],
  },
} as Question;
export const multiChoiceQuestionMarkData: Question = {
  score: 2,
  questionType: 'Multiple_choice',
  multipleChoiceAnswer: {
    answer: ['Option-3'],
    options: ['Option-1', 'Option-2', 'Option-3', 'Option-4'],
  },
} as Question;
export const trueOrFalseQuestionMarkData: Question = {
  score: 2,
  questionType: 'True_or_false',
  trueOrFalseAnswer: {
    answer: ['True'],
    options: ['True', 'False'],
  },
} as Question;
export const fillInQuestionMarkData: Question = {
  score: 2,
  questionType: 'Fill_in',
  fillInAnswer: {
    answer: ['Option-3', 'Option-1'],
    options: ['Option-1', 'Option-2', 'Option-3', 'Option-4'],
  },
} as Question;
export const essayQuestionMarkData: Question = {
  questionText: 'Essay Type question-text',
  score: 2,
  questionType: 'Essay',
  essayAnswer: {
    rubrics: 'essay-rubric',
  },
} as Question;
export const matrixQuestionMarkData: Question = {
  questionText: 'Essay Type question-text',
  score: 2,
  questionType: 'Matrix',
  matchMatrixAnswer: {
    questions: [] as MatrixSubquestion[],
  },
} as Question;
