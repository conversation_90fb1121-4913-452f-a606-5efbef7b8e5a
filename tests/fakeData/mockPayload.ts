import sinon from 'sinon';
import {
  DraftQuestion,
  SubmitAssessmentTestDTO,
} from '../../src/helpers/types';

export const submitAssessmentMockPayload = {
  startTime: '2024-10-23T14:04:13.297Z',
  finishTime: '2024-10-23T14:04:28.134Z',
  testResults: [
    {
      testId: '9de6462a-dcb2-44a7-9581-822d6c3c3e15',
      questionResults: [
        {
          questionId: '63d4dbc8-6aa3-4f96-9cad-a6f8a0663ea3',
          testTakerAnswers: ['gfddfsghes'],
          idleTime: 0,
          questionType: 'Multiple_choice',
        },
      ],
    },
  ],
  screenMonitoring: [
    {
      imageURL: 'https://fake-image-url-1',
      isIntegrityShot: false,
      timeStamp: '2024-10-23T14:04:13.297Z',
      violation: false,
    },
    {
      violation: true,
      startTime: '2024-10-23T14:04:10.000Z', // Example start time
      endTime: '2024-10-23T14:04:15.000Z', // Example end time
      shots: [
        // MUST have the 'shots' array property
        {
          imageURL: 'https://fake-image-url-2', // The original image can be one of the shots
          timeStamp: '2024-10-23T14:04:13.297Z',
        },
        // Add other shots taken during the violation period if applicable
      ],
      // Add other required ViolationCapture properties:
      questNumber: 1, // Example
      questionId: '63d4dbc8-6aa3-4f96-9cad-a6f8a0663ea3', // Example
      testId: '9de6462a-dcb2-44a7-9581-822d6c3c3e15', // Example
      testNumber: 1, // Example
      violationNumber: 1, // Example
    },
    {
      imageURL: 'https://fake-image-url-3',
      isIntegrityShot: false,
      timeStamp: '2024-10-23T14:04:13.297Z',
      violation: false,
    },
  ],
  candidateMonitoring: [
    {
      imageURL: 'https://fake-image-url-1',
      isIntegrityShot: false,
      timeStamp: '2024-10-23T14:04:13.297Z',
      violation: false,
    },
    {
      imageURL: 'https://fake-image-url-2',
      isIntegrityShot: false,
      timeStamp: '2024-10-23T14:04:13.297Z',
      violation: true,
      shots: [
        {
          imageURL: 'https://fake-image-url-2',
          timeStamp: '2024-10-23T14:04:13.297Z',
        },
        // Add other shots taken during the violation period if applicable
      ],
      // Add other required ViolationCapture properties:
      questNumber: 1, // Example
      questionId: '63d4dbc8-6aa3-4f96-9cad-a6f8a0663ea3', // Example
      testId: '9de6462a-dcb2-44a7-9581-822d6c3c3e15', // Example
      testNumber: 1, // Example
      violationNumber: 1, // Example
    },
    {
      imageURL: 'https://fake-image-url-3',
      isIntegrityShot: false,
      timeStamp: '2024-10-23T14:04:13.297Z',
      violation: false,
    },
  ],
  isAutoSubmission: false,
} as SubmitAssessmentTestDTO;

export const fakeCandidateDraftPayload: DraftQuestion = {
  testResults: [
    {
      testId: '9de6462a-dcb2-44a7-9581-822d6c3c3e15',
      questionResults: [
        {
          questionId: '63d4dbc8-6aa3-4f96-9cad-a6f8a0663ea3',
          testTakerAnswers: ['gfddfsghes'],
          idleTime: 0,
          questionType: 'Multiple_choice',
        },
        {
          questionId: '63d4dbc8-6aa3-4f96-9cad-a6f8a0663ea3',
          testTakerAnswers: ['gfddfsghes'],
          idleTime: 0,
          questionType: 'Matrix',
        },
      ],
    },
  ],
  screenMonitoring: [],
  candidateMonitoring: [],
};

export const sentryMock = {
  captureException: sinon.fake(),
  captureMessage: sinon.fake(),
  init: sinon.fake(),
  setupExpressErrorHandler: sinon.fake(),
};

export const createPrismaStub = () => ({
  assessmentTaker: {
    update: sinon.stub().resolves({
      id: 'fake-id',
      email: '<EMAIL>',
      status: 'IN_PROGRESS',
      phase: 'CODE_CONDUCT_SIGNING',
      logSummary: [],
      logs: [],
    }),
    findUnique: sinon.stub().resolves({
      id: 'fake-id',
      email: '<EMAIL>',
      status: 'IN_PROGRESS',
      phase: 'CODE_CONDUCT_SIGNING',
    }),
  },
  $transaction: sinon
    .stub()
    .callsFake((fn) => (Array.isArray(fn) ? Promise.all(fn) : fn())),
  '@noCallThru': true,
});
