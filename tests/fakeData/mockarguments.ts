// import { AssessmentTaker } from "@prisma/client";

import { SampleTestDto } from '../../src/helpers/types';

export const flagQuestionPayload = {
  testId: '49df0ae4-e4c8-46f5-b2f8-319ca43bcf27',
  questionId: 'cfd46de2-29b0-4700-bb54-542d043cd4d8',
  questionText: 'What is React?',
  reasonOfFlagging: ['My reason'],
};

// export const assessmentTakerInfo = {

// } satisfies AssessmentTaker

export const user = {
  userId: 'fake-user-id',
  email: '<EMAIL>',
  role: 'fake-role',
  system: true,
  organizationId: 'fake-organisation-id',
  permissions: ['fake-permission-1', 'fake-permission-1'],
};

export const sampleTestDtoMock: SampleTestDto = {
  test: {
    questions: [
      {
        questionType: 'multiple-choice',
        score: 5,
        answer: ['Option A', 'Option B'],
        testTakerAnswer: ['Option A'],
      },
      {
        questionType: 'true-false',
        score: 2,
        answer: ['True'],
        testTakerAnswer: ['False'],
      },
      {
        questionType: 'short-answer',
        score: 3,
        answer: ['42'],
        testTakerAnswer: ['40'],
      },
    ],
  },
};
