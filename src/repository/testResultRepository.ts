import { DraftQuestion, ProctoringDataSummary } from '../helpers/types';
import prisma from '../prisma';

export class TestResultRepository {
  static async getUnsubmittedTest(assessmentTakerId: string) {
    return prisma.testResult.findMany({
      where: {
        assessmentTakerId,
        status: 'NOT_SUBMITTED',
      },
      include: {
        drafts: {
          include: {
            matchMatrixAnswers: true,
            codeDraftInfo: true,
          },
        },
      },
    });
  }

  static async draftProctoringData({
    testId,
    assessmentTakerId,
    draftCandidateMonitoring,
    draftScreenMonitoring,
  }: {
    testId: string;
    assessmentTakerId: string;
    draftCandidateMonitoring: DraftQuestion['candidateMonitoring'];
    draftScreenMonitoring: DraftQuestion['screenMonitoring'];
  }) {
    return prisma.testResult.update({
      where: {
        testId_assessmentTakerId: {
          testId,
          assessmentTakerId,
        },
      },
      data: {
        draftIntervalScreenshots: draftScreenMonitoring as unknown,
        draftIntervalAssessmentTakerShots: draftCandidateMonitoring as unknown,
      },
    });
  }

  static async updateTestResultAfterTestSubmission({
    assessmentTakerId,
    proctoringDataSummary,
    testId,
  }: {
    assessmentTakerId: string;
    testId: string;
    proctoringDataSummary: ProctoringDataSummary;
  }) {
    const { assessmentTakerShotSummary, violationSummary, windowShotSummary } =
      proctoringDataSummary;
    return prisma.testResult.update({
      where: {
        testId_assessmentTakerId: {
          testId: testId,
          assessmentTakerId,
        },
      },
      data: {
        testTakerShotCount:
          assessmentTakerShotSummary.assessmentTakerTotalShots,
        testTakerViolationShotCount:
          assessmentTakerShotSummary.assessmentTakerTotalViolationShots,
        testWindowViolationShotCount:
          windowShotSummary.windowViolationTotalShots,
        testWindowShotCount: windowShotSummary.windowTotalShots,
        testWindowViolationCount: violationSummary.windowViolationCount,
        testWindowViolationDuration: violationSummary.windowViolationDuration,
        draftIntervalAssessmentTakerShots: [],
        draftIntervalScreenshots: [],
      },
    });
  }
}
