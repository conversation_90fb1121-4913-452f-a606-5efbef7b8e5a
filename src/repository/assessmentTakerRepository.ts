import { ProctoringDataSummary } from '../helpers/types';
import prisma from '../prisma';

export class AssessmentTakerRepository {
  static async getUncompletedAssessmentTaker() {
    return prisma.assessmentTaker.findMany({
      where: {
        phase: 'TEST_TAKING',
        status: 'IN_PROGRESS',
        estimatedEndTime: {
          not: null,
        },
      },
    });
  }

  static async saveAssessmentTakerTestProctoringSummary({
    assessmentTakerId,
    proctoringDataSummary,
  }: {
    assessmentTakerId: string;
    proctoringDataSummary: ProctoringDataSummary;
  }) {
    const { assessmentTakerShotSummary, violationSummary, windowShotSummary } =
      proctoringDataSummary;
    return await prisma.assessmentTaker.update({
      where: {
        id: assessmentTakerId,
      },
      data: {
        assessmentTakerShotCount: {
          increment: assessmentTakerShotSummary.assessmentTakerTotalShots,
        },
        assessmentTakerViolationShotCount: {
          increment:
            assessmentTakerShotSummary.assessmentTakerTotalViolationShots,
        },
        windowShotCount: {
          increment: windowShotSummary.windowTotalShots,
        },
        windowViolationShotCount: {
          increment: windowShotSummary.windowViolationTotalShots,
        },
        assessmentWindowViolationCount: {
          increment: violationSummary.windowViolationCount,
        },
        assessmentWindowViolationDuration: {
          increment: violationSummary.windowViolationDuration,
        },
      },
    });
  }
}
