import {
  CreateCodeDraft,
  CreateMatchMatrixAnswerDraft,
  CreateStringArrayDraft,
} from '../helpers/types';
import prisma from '../prisma';

export class DraftRepository {
  static async createOrUpdateStringArrayAnswer(data: CreateStringArrayDraft) {
    const {
      assessmentTakerId,
      questionId,
      questionType,
      testId,
      testTakerAnswers,
    } = data;
    const draftExist = await prisma.draft.findFirst({
      where: {
        questionId,
        testId,
        assessmentTakerId,
      },
    });
    if (draftExist) {
      // update draft
      return prisma.draft.update({
        where: {
          id: draftExist.id,
        },
        data: {
          stringArrayAnswers: testTakerAnswers,
        },
      });
    }

    return prisma.draft.create({
      data: {
        assessmentTakerId,
        testId,
        questionId,
        questionType,
        stringArrayAnswers: testTakerAnswers,
      },
    });
  }

  static async createOrUpdateMatchMatrixAnswer(
    data: CreateMatchMatrixAnswerDraft
  ) {
    const {
      assessmentTakerId,
      questionId,
      questionType,
      testId,
      testTakerAnswers,
    } = data;

    const draftExist = await prisma.draft.findFirst({
      where: {
        questionId,
        testId,
        assessmentTakerId,
      },
    });
    if (draftExist) {
      // draft exist
      await prisma.matchMatrixDraftAnswer.deleteMany({
        where: {
          draftId: draftExist.id,
        },
      });

      return prisma.draft.update({
        where: {
          id: draftExist.id,
        },
        data: {
          matchMatrixAnswers: {
            createMany: {
              data: testTakerAnswers,
            },
          },
        },
      });
    }

    return prisma.draft.create({
      data: {
        questionId,
        questionType,
        assessmentTakerId,
        testId,
        matchMatrixAnswers: {
          createMany: {
            data: testTakerAnswers,
          },
        },
      },
    });
  }

  static async createOrUpdateCodeDraft(data: CreateCodeDraft) {
    const {
      assessmentTakerId,
      questionId,
      questionType,
      testId,
      testTakerAnswers,
    } = data;

    const draftExist = await prisma.draft.findFirst({
      where: {
        questionId,
        testId,
        assessmentTakerId,
      },
    });

    if (draftExist) {
      // draft exist
      await prisma.codeDraftInfo.deleteMany({
        where: {
          draftId: draftExist.id,
        },
      });

      return prisma.draft.update({
        where: {
          id: draftExist.id,
        },
        data: {
          codeDraftInfo: {
            createMany: {
              data: testTakerAnswers,
            },
          },
        },
      });
    }
    return prisma.draft.create({
      data: {
        questionId,
        questionType,
        assessmentTakerId,
        testId,
        codeDraftInfo: {
          createMany: {
            data: testTakerAnswers,
          },
        },
      },
    });
  }
}
