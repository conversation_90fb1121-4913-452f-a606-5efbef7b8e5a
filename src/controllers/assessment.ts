/* eslint-disable no-useless-escape */
import { NextFunction, Request, Response } from 'express';
import dotenv from 'dotenv';
import prisma from '../prisma';
import { IQuestionFlagDTO } from '../dtos/questionFlagDto';
import * as QuestionFlagService from '../services/questionFlagService';
import { AppError, HttpCode } from '../middlewares/errorHandler';
import {
  questionUnflaggingPublish,
  sendAssessmentSurvey,
} from '../helpers/kafkaPublishMessage';
import { AssessmentService } from '../services/assessment';
import {
  ErrorMessage,
  PresignedURLDto,
  RequestExtension,
} from '../helpers/types';
import logger from '../helpers/logger';
import { AssessmentMetricsNormalizer } from '../helpers/assessmentMetricsNormalizer';
import {
  getAssessmentTakerDependencyData,
  getAssessmentTakerDependencyDataMark,
} from '../helpers/redisCache';
import { AssessmentProcess } from '../helpers/assessmentProcess';
import { AssessmentReport } from '../helpers/assessmentReport';
import { QuestionService } from '../services/questionService';
import { AIAnalyticsService } from '../services/aiAnalyticsService';

dotenv.config();

export const assessmentProgress = async (
  req: RequestExtension,
  res: Response,
  next: NextFunction
) => {
  try {
    const processAssessmentTakerData = await AssessmentService.progress(
      req.assessmentTakerInfo,
      req.body
    );

    res.status(200).send({ success: true, data: processAssessmentTakerData });
  } catch (error) {
    next(error);
  }
};

export const getAssessmentTakerIdentityInfo = async (
  req: RequestExtension,
  res: Response,
  next: NextFunction
) => {
  try {
    const identity = await AssessmentService.getAssessmentIdentityInfo(
      req.assessmentTakerInfo.id
    );
    res.status(200).send({
      success: true,
      data: { identity },
    });
  } catch (error) {
    next(error);
  }
};

export const flagQuestion = async (
  req: RequestExtension,
  res: Response,
  next: NextFunction
) => {
  try {
    const data = req.body as IQuestionFlagDTO;

    const { questionFlagged } = await QuestionFlagService.createFlag(
      data,
      req.assessmentTakerInfo
    );

    res.status(201).send({
      success: true,
      data: questionFlagged,
    });
  } catch (error) {
    next(error);
  }
};

export const unflagQuestion = async (
  req: RequestExtension,
  res: Response,
  next: NextFunction
) => {
  try {
    const { questionId } = req.body as IQuestionFlagDTO;
    const { id, assessmentId } = req.assessmentTakerInfo;
    const whereClause = {
      questionId,
      assessmentId,
      testTakerId: id,
    };
    const questionToBeUnflagged = await QuestionFlagService.getFlagQuestionBy(
      whereClause
    );
    if (!questionToBeUnflagged) {
      throw new AppError({
        httpCode: HttpCode.NOT_FOUND,
        description: 'Cannot unflag the question',
      });
    }
    await QuestionFlagService.deleteFlagQuestion(questionToBeUnflagged.id);

    // publish question unflagging event
    await questionUnflaggingPublish(
      req.assessmentTakerInfo,
      questionToBeUnflagged
    );
    res.status(200).send({
      success: true,
      data: { message: 'Question unflagged successfully' },
    });
  } catch (error) {
    next(error);
  }
};

export const assessmentTakerResult = async (
  req: RequestExtension,
  res: Response,
  next: NextFunction
) => {
  try {
    const assessmentResponse = await AssessmentService.getAssessmentTakerResult(
      req.assessmentTakerInfo
    );
    res.status(200).json({
      success: true,
      data: {
        assessmentTakerResult: assessmentResponse,
      },
    });
  } catch (error) {
    next(error);
  }
};

export const deleteResult = async (
  req: Request,
  res: Response,
  next: NextFunction
) => {
  try {
    const { id } = req.body;
    await prisma.testTakerBasicAssessmentResult.delete({
      where: { id },
    });
    res.status(200).send({
      success: true,
      data: { message: 'Result deleted successfully' },
    });
  } catch (error) {
    next(error);
  }
};

export const submitAssessment = async (
  req: RequestExtension,
  res: Response,
  next: NextFunction
) => {
  try {
    await AssessmentService.submitAssessment(req.body, req.assessmentTakerInfo);
    res.status(200).send({
      success: true,
      data: {
        message: 'Assessment submitted successfully',
      },
    });
  } catch (error) {
    logger.error('Error submitting assessment', error);
    next(error);
  }
};
export const sendSurvey = async (
  req: RequestExtension,
  res: Response,
  next: NextFunction
) => {
  try {
    const survey = await sendAssessmentSurvey(
      req.assessmentTakerInfo,
      req.body
    );

    res.status(200).send({ success: true, data: { survey } });
  } catch (error) {
    next(error);
  }
};

export const getAssessmentTakerScreenshot = async (
  req: RequestExtension,
  res: Response,
  next: NextFunction
) => {
  try {
    const assessmentScreenShots =
      await AssessmentService.getAssessmentTakerScreenshot(
        req.assessmentTakerInfo.id
      );

    return res.status(200).json({
      success: true,
      data: {
        assessmentScreenShots,
      },
    });
  } catch (error) {
    next(error);
  }
};
export const getAssessmentTakerCaptureShots = async (
  req: RequestExtension,
  res: Response,
  next: NextFunction
) => {
  try {
    const assessmentTakerCaptureShots =
      await AssessmentService.getAssessmentTakerCaptureShots(
        req.assessmentTakerInfo.id
      );

    return res.status(200).json({
      success: true,
      data: {
        assessmentTakerCaptureShots,
      },
    });
  } catch (error) {
    next(error);
  }
};

export const sampleTestMarking = async (
  req: Request,
  res: Response,
  next: NextFunction
) => {
  try {
    const testResult = AssessmentService.sampleTestMarking(req.body);

    return res.status(201).json({
      success: true,
      data: {
        testResult,
      },
    });
  } catch (error) {
    next(error);
  }
};

export const getAssessmentTakingStatus = async (
  req: Request,
  res: Response,
  next: NextFunction
) => {
  try {
    const { assessmentTakerId } = req.params;
    const currentAssessmentStage =
      await AssessmentService.getAssessmentTakingStatus(assessmentTakerId);

    res.status(HttpCode.OK).json({
      success: true,
      data: currentAssessmentStage,
    });
  } catch (error) {
    if (
      error instanceof AppError &&
      error.message &&
      error.message.startsWith('retake_delay_')
    ) {
      const messageParts = error.message.split('_');
      const hoursRemaining = parseInt(messageParts[2]);

      const retakeDate = new Date();
      const hours = Number(hoursRemaining);
      if (isNaN(hours)) {
        throw new Error(`Invalid hoursRemaining: ${hoursRemaining}`);
      }
      retakeDate.setHours(retakeDate.getHours() + hours);
      return res.status(HttpCode.FORBIDDEN).json({
        success: false,
        error: {
          message: `You can retake this assessment in ${hours} hours.`,
          retakeDelay: true,
          hoursRemaining: hours,
          retakeDate: retakeDate.toISOString(),
          retakeDateReadable: retakeDate.toLocaleString(),
        },
      });
    }
    next(error);
  }
};

export const submitAssessmentTest = async (
  req: RequestExtension,
  res: Response,
  next: NextFunction
) => {
  try {
    const response = await AssessmentService.submitAssessmentTest(
      req.assessmentTakerInfo,
      req.body
    );
    res.status(HttpCode.OK).json({
      success: true,
      data: {
        message: `${response.title} Test submitted successfully`,
      },
    });
  } catch (error) {
    next(error);
  }
};

export const draftCandidateProgress = async (
  req: RequestExtension,
  res: Response,
  next: NextFunction
) => {
  try {
    await AssessmentService.draftCandidateProgress(
      req.assessmentTakerInfo,
      req.body
    );

    res.status(201).json({
      success: true,
      message: 'Progress drafted',
    });
  } catch (error) {
    next(error);
  }
};

export const getCandidatePreSignedURL = async (
  req: RequestExtension,
  res: Response,
  next: NextFunction
) => {
  try {
    const response = await AssessmentService.getCandidatePreSignedURL(
      req.assessmentTakerInfo,
      req.body as unknown as PresignedURLDto
    );

    res.status(200).json({
      success: true,
      data: response,
    });
  } catch (error) {
    next(error);
  }
};

export const getCandidateAssessmentsInfo = async (
  req: RequestExtension,
  res: Response,
  next: NextFunction
) => {
  try {
    const { assessmentIds, email } = req.body;

    if (
      !assessmentIds ||
      !Array.isArray(assessmentIds) ||
      assessmentIds.length === 0 ||
      !email
    ) {
      throw new AppError({
        httpCode: HttpCode.BAD_REQUEST,
        description: ErrorMessage.INVALID_REQUEST_BODY,
      });
    }
    const candidateAssessments =
      await AssessmentService.getCandidateAssessmentsInfo(
        assessmentIds,
        email as string
      );

    res.status(200).send({
      success: true,
      data: {
        candidateAssessments,
      },
    });
  } catch (error) {
    next(error);
  }
};

export const getAssessmentTakerInfo = async (
  req: RequestExtension,
  res: Response,
  next: NextFunction
) => {
  try {
    const { assessmentTakerId } = req.params;
    const { testId } = req.query as { testId?: string };

    const assessmentTakerInfo = await prisma.assessmentTaker.findFirst({
      where: {
        id: assessmentTakerId,
      },
      include: {
        testResults: {
          where: {
            testId,
          },
        },
      },
    });

    if (!assessmentTakerInfo) {
      throw new AppError({
        httpCode: HttpCode.NOT_FOUND,
        description: ErrorMessage.ASSESSMENT_TAKER_NOT_FOUND,
      });
    }

    res.status(HttpCode.OK).json({
      success: true,
      data: assessmentTakerInfo,
    });
  } catch (error) {
    next(error);
  }
};

/**
 * Manually trigger marking of draft questions for an assessment taker
 * This is a fallback mechanism when auto-marking fails
 */
export const markDraftQuestions = async (
  req: RequestExtension,
  res: Response,
  next: NextFunction
) => {
  try {
    const { assessmentTakerId } = req.params;

    // Get assessment taker info with test data
    const assessmentTaker = await prisma.assessmentTaker.findUnique({
      where: { id: assessmentTakerId },
    });
    if (!assessmentTaker) {
      throw new AppError({
        httpCode: HttpCode.NOT_FOUND,
        description: ErrorMessage.ASSESSMENT_TAKER_NOT_FOUND,
      });
    }

    // Check if all tests are already submitted
    if (
      assessmentTaker.submittedTests.length === assessmentTaker.testList.length
    ) {
      return res.status(HttpCode.OK).json({
        success: true,
        message: 'All tests have already been submitted',
      });
    }

    // Get dependency data for marking
    const assessmentMarkDependency = await getAssessmentTakerDependencyDataMark(
      assessmentTaker
    );
    const assessmentDependency = await getAssessmentTakerDependencyData(
      assessmentTaker
    );

    // Mark drafted questions
    await AssessmentProcess.markDraftedQuestions(
      assessmentDependency.assessmentTaker,
      assessmentMarkDependency.assessmentTaker.assessment.tests
    );

    // Generate assessment report
    await AssessmentReport.autoSendAssessmentReportSubmission(assessmentTaker);
    res.status(HttpCode.OK).json({
      success: true,
      message: 'Draft questions marked successfully',
    });
  } catch (error) {
    next(error);
  }
};

/**
 * Verify a test taker using email and assessmentTakerId
 * This endpoint allows verification of a test taker's identity
 */
export const verifyTestTaker = async (
  req: Request,
  res: Response,
  next: NextFunction
) => {
  try {
    const { email, assessmentTakerId } = req.body;

    // Find the assessment taker with the provided ID and email
    const assessmentTaker = await prisma.assessmentTaker.findFirst({
      where: {
        id: assessmentTakerId,
        email: email,
      },
      select: {
        id: true,
        email: true,
        assessmentName: true,
        status: true,
        phase: true,
        linkStatus: true,
      },
    });

    if (!assessmentTaker) {
      throw new AppError({
        httpCode: HttpCode.BAD_REQUEST,
        description:
          'No matching test taker found with the provided email and ID',
      });
    }

    res.status(HttpCode.OK).json({
      success: true,
      data: {
        verified: true,
        testTaker: assessmentTaker,
      },
    });
  } catch (error) {
    next(error);
  }
};

export const getCandidateAssessmentReport = async (
  req: RequestExtension,
  res: Response,
  next: NextFunction
) => {
  try {
    const { assessmentId } = req.params;
    const { organizationId } = req.user;

    if (!assessmentId) {
      throw new AppError({
        httpCode: HttpCode.BAD_REQUEST,
        description: 'Assessment ID is required',
      });
    }
    const assessmentTakers = await prisma.assessmentTaker.findMany({
      where: AssessmentMetricsNormalizer.getCandidateFilter(
        assessmentId,
        organizationId
      ),
      include: {
        testResults: true,
      },
    });

    if (!assessmentTakers || assessmentTakers.length === 0) {
      throw new AppError({
        httpCode: HttpCode.NOT_FOUND,
        description: 'No candidates found for this assessment',
      });
    }

    const candidateReports = await Promise.all(
      assessmentTakers.map(async (candidate) => {
        const tests = [];

        for (const testResult of candidate.testResults) {
          const questionIds = testResult.result.map((r: any) => r.questionId);
          const questionDataMap = await QuestionService.getQuestionTextsByIds(
            questionIds,
            organizationId
          );

          const questions = testResult.result.map(
            (result: any, index: number) => {
              const questionData = questionDataMap[result.questionId] ?? {};

              return {
                questionNumber: index + 1,
                questionId: result.questionId,
                questionTitle: questionData.questionTitle ?? 'N/A',
                questionType: result.questionType ?? questionData.questionType,
                questionText: questionData.questionText ?? 'N/A',
                candidateAnswer: result.testTakerAnswers ?? [],
                correctAnswer: getCorrectAnswer(questionData),
                scoreAwarded: result.scored ?? 0,
                maxScore: result.score || questionData.score || 0,
                answerStatus: result.answerStatus,
                isAnswered: result.isAnswered,
              };
            }
          );

          tests.push({
            testId: testResult.testId,
            testTitle: testResult.title,
            testScore: testResult.totalPassedScore,
            maxTestScore: testResult.totalScore,
            testPercentage: testResult.testPercentage,
            questions,
          });
        }

        // Use normalized candidate data for consistency
        const normalizedCandidate =
          AssessmentMetricsNormalizer.normalizeCandidateData(candidate);
        return {
          ...normalizedCandidate,
          tests,
        };
      })
    );

    // Calculate comprehensive metrics for debugging and consistency
    const normalizedCandidates = assessmentTakers.map((candidate) =>
      AssessmentMetricsNormalizer.normalizeCandidateData(candidate)
    );

    const allCandidatesMetrics =
      AssessmentMetricsNormalizer.calculateAssessmentMetrics(
        normalizedCandidates
      );
    const completedCandidatesMetrics =
      AssessmentMetricsNormalizer.calculateCompletedCandidateMetrics(
        normalizedCandidates
      );
    const candidatesWithResultsMetrics =
      AssessmentMetricsNormalizer.calculateCandidatesWithResultsMetrics(
        normalizedCandidates
      );

    res.status(HttpCode.OK).json({
      success: true,
      data: {
        assessmentId,
        ...allCandidatesMetrics,
        // Additional metrics for debugging discrepancies
        completedCandidatesMetrics,
        candidatesWithResultsMetrics,
        candidates: candidateReports,
      },
    });
  } catch (error) {
    logger.error('Error generating candidate assessment report:', error);
    next(error);
  }
};

export const generateAIAnalyticsReport = async (
  req: RequestExtension,
  res: Response,
  next: NextFunction
) => {
  try {
    const { assessmentTakerId } = req.params;
    const { organizationId } = req.user;
    if (!assessmentTakerId) {
      throw new AppError({
        httpCode: HttpCode.BAD_REQUEST,
        description: 'Assessment Taker ID is required',
      });
    }

    logger.info(
      `Generating AI analytics report for assessment taker: ${assessmentTakerId}`
    );

    const pdfBuffer = await AIAnalyticsService.generateAnalyticsReport({
      assessmentTakerId,
      organizationId,
    });

    logger.info(`PDF generated successfully, size: ${pdfBuffer.length} bytes`);

    // Set response type to 'arraybuffer' for binary data
    res.type('application/pdf');
    res.setHeader(
      'Content-Disposition',
      `attachment; filename="analytics-report-${assessmentTakerId}.pdf"`
    );
    res.setHeader('Content-Length', pdfBuffer.length);

    // Send buffer directly without parsing as JSON
    return res.send(pdfBuffer);
  } catch (error: any) {
    logger.error('Error generating AI analytics report:', {
      message: error.message,
      stack: error.stack,
    });
    next(error);
  }
};

function getCorrectAnswer(questionData: any) {
  if (questionData.multipleChoiceAnswer)
    return questionData.multipleChoiceAnswer;
  if (questionData.multipleSelectAnswer)
    return questionData.multipleSelectAnswer;
  if (questionData.trueOrFalseAnswer) return questionData.trueOrFalseAnswer;
  if (questionData.fillInAnswer) return questionData.fillInAnswer;
  if (questionData.matchMatrixAnswer) return questionData.matchMatrixAnswer;
  if (questionData.essayAnswer) return questionData.essayAnswer;
  if (questionData.referenceSolution) return questionData.referenceSolution;
  return 'N/A';
}
