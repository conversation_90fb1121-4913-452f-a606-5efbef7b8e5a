import { NextFunction, Response } from 'express';
import { RequestExtension } from '../helpers/types';
import { AppError, HttpCode } from '../middlewares/errorHandler';
import {
  createSourceCode,
  executeCode,
  generateRunnerCode,
  prepareAndProcessTestCases,
} from '../helpers/codeExecution';
import logger from '../helpers/logger';

export const runTestCase = async (
  req: RequestExtension,
  res: Response,
  next: NextFunction
) => {
  try {
    const { code, language_id, input } = req.body;

    // Enhanced validation
    if (!code || typeof code !== 'string' || code.trim().length === 0) {
      throw new AppError({
        httpCode: HttpCode.BAD_REQUEST,
        description: 'Code must be a non-empty string',
      });
    }

    if (!language_id || typeof language_id !== 'number') {
      throw new AppError({
        httpCode: HttpCode.BAD_REQUEST,
        description: 'Language ID must be a valid number',
      });
    }

    if (input === undefined || input === null) {
      throw new AppError({
        httpCode: HttpCode.BAD_REQUEST,
        description: 'Input is required',
      });
    }

    const inputStr = typeof input === 'string' ? input : JSON.stringify(input);

    const runnerCode = generateRunnerCode(language_id, code, inputStr);
    const sourceCode = createSourceCode(code, runnerCode, language_id);
    const result = await executeCode(sourceCode, language_id, inputStr);

    res.status(200).send({
      success: true,
      data: {
        output: result,
      },
    });
  } catch (error) {
    logger.error('Error in runTestCase:', error);
    next(error);
  }
};

export const runMultipleTestCases = async (
  req: RequestExtension,
  res: Response,
  next: NextFunction
) => {
  try {
    const { code, language_id, testCases, codeConstraints } = req.body;
    const { assessmentTakerId, questionId } = req.params;

    // Enhanced validation
    if (!code || typeof code !== 'string' || code.trim().length === 0) {
      throw new AppError({
        httpCode: HttpCode.BAD_REQUEST,
        description: 'Code must be a non-empty string',
      });
    }

    if (!language_id || typeof language_id !== 'number') {
      throw new AppError({
        httpCode: HttpCode.BAD_REQUEST,
        description: 'Language ID must be a valid number',
      });
    }

    if (!Array.isArray(testCases) || testCases.length === 0) {
      throw new AppError({
        httpCode: HttpCode.BAD_REQUEST,
        description: 'Test cases must be a non-empty array',
      });
    }

    if (testCases.length > 100) {
      // Reasonable limit
      throw new AppError({
        httpCode: HttpCode.BAD_REQUEST,
        description: 'Too many test cases (maximum 100 allowed)',
      });
    }

    if (!assessmentTakerId || typeof assessmentTakerId !== 'string') {
      throw new AppError({
        httpCode: HttpCode.BAD_REQUEST,
        description: 'Valid assessment taker ID is required',
      });
    }

    if (!questionId || typeof questionId !== 'string') {
      throw new AppError({
        httpCode: HttpCode.BAD_REQUEST,
        description: 'Valid question ID is required',
      });
    }

    // Validate test case structure
    testCases.forEach((testCase, index) => {
      if (
        !testCase.test_case_id ||
        !testCase.input ||
        testCase.output === undefined
      ) {
        throw new AppError({
          httpCode: HttpCode.BAD_REQUEST,
          description: `Test case at index ${index} is missing required fields (test_case_id, input, output)`,
        });
      }
    });

    // Process test cases using the enhanced chunking mechanism
    const response = await prepareAndProcessTestCases(
      assessmentTakerId,
      questionId,
      code,
      language_id,
      testCases,
      codeConstraints
    );

    res.status(200).send({
      success: true,
      data: response,
    });
  } catch (error) {
    logger.error('Error in runMultipleTestCases:', error);
    next(error);
  }
};

export const validateTestCases = async (
  req: RequestExtension,
  res: Response,
  next: NextFunction
) => {
  try {
    const { code, language_id, testCases } = req.body;

    // Enhanced validation
    if (!code || typeof code !== 'string' || code.trim().length === 0) {
      throw new AppError({
        httpCode: HttpCode.BAD_REQUEST,
        description: 'Code must be a non-empty string',
      });
    }

    if (!language_id || typeof language_id !== 'number') {
      throw new AppError({
        httpCode: HttpCode.BAD_REQUEST,
        description: 'Language ID must be a valid number',
      });
    }

    if (!Array.isArray(testCases) || testCases.length === 0) {
      throw new AppError({
        httpCode: HttpCode.BAD_REQUEST,
        description: 'Test cases must be a non-empty array',
      });
    }

    if (testCases.length > 50) {
      // Reasonable limit for validation
      throw new AppError({
        httpCode: HttpCode.BAD_REQUEST,
        description: 'Too many test cases for validation (maximum 50 allowed)',
      });
    }

    const results = [];
    const errorMessages = [];
    const concurrencyLimit = 5; // Process 5 test cases concurrently

    // Process test cases in batches to avoid overwhelming the system
    for (let i = 0; i < testCases.length; i += concurrencyLimit) {
      const batch = testCases.slice(i, i + concurrencyLimit);
      const batchPromises = batch.map(async (testCase, batchIndex) => {
        const globalIndex = i + batchIndex;

        if (!testCase.input || testCase.expected_output === undefined) {
          const errorMessage = `Test case ${
            globalIndex + 1
          } is missing required fields`;
          errorMessages.push(errorMessage);
          return null;
        }

        try {
          const inputStr =
            typeof testCase.input === 'string'
              ? testCase.input
              : JSON.stringify(testCase.input);
          const runnerCode = generateRunnerCode(language_id, code, inputStr);
          const sourceCode = createSourceCode(code, runnerCode, language_id);
          const output = await executeCode(sourceCode, language_id, inputStr);

          let actualOutput: unknown;
          try {
            actualOutput = JSON.parse(output);
          } catch {
            actualOutput = output;
          }

          const expected = testCase.expected_output;
          const isEqual =
            JSON.stringify(expected) === JSON.stringify(actualOutput);

          return {
            index: globalIndex,
            testCase,
            actualOutput,
            wasCorrect: isEqual,
            hasError: false,
          };
        } catch (err) {
          const errorMessage = `Test case ${
            globalIndex + 1
          } execution failed: ${
            err instanceof Error
              ? err.message.substring(0, 200)
              : String(err).substring(0, 200)
          }`;
          errorMessages.push(errorMessage);
          return null;
        }
      });

      const batchResults = await Promise.allSettled(batchPromises);
      batchResults.forEach((result) => {
        if (result.status === 'fulfilled' && result.value) {
          results.push(result.value);
        }
      });
    }

    // Create validated test cases only from successful executions
    const validatedTestCases = results.map((result) => {
      return {
        input: result.testCase.input,
        expected_output: result.wasCorrect
          ? result.testCase.expected_output
          : result.actualOutput, // Replace with actual output if wrong
        description: result.testCase.description ?? '',
        hidden: result.testCase.hidden ?? false,
      };
    });

    res.status(200).send({
      success: true,
      data: {
        validatedTestCases,
        errorMessages: errorMessages.length > 0 ? errorMessages : undefined,
        summary: {
          totalTestCases: testCases.length,
          validTestCases: validatedTestCases.length,
          errorCount: errorMessages.length,
          correctedCount: results.filter((r) => !r.wasCorrect).length,
        },
      },
    });
  } catch (error) {
    logger.error('Error in validateTestCases:', error);
    next(error);
  }
};
