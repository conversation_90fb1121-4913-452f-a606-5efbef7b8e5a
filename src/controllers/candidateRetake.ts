import { Request, Response, NextFunction } from 'express';
import { RetakeDelay } from '../helpers/retakeDelay';
import { AppError, HttpCode } from '../middlewares/errorHandler';
import prisma from '../prisma';

/**
 * Check if a candidate can retake an assessment
 */
export const checkRetakeEligibility = async (
  req: Request,
  res: Response,
  next: NextFunction
) => {
  try {
    const { email, assessmentId } = req.body;

    if (!email || !assessmentId) {
      throw new AppError({
        httpCode: HttpCode.BAD_REQUEST,
        description: 'Email and assessment ID are required',
      });
    }

    // Check retake eligibility
    const canRetake = await RetakeDelay.canRetakeAssessment(
      email,
      assessmentId
    );
    const hoursRemaining = await RetakeDelay.getHoursRemaining(
      email,
      assessmentId
    );

    // Get assessment-specific retake delay for response
    const assessmentRetakeDelayHours =
      await RetakeDelay.getAssessmentRetakeDelay(email, assessmentId);

    const response = {
      canRetake,
      hoursRemaining: hoursRemaining || 0,
      retakeDelayHours: assessmentRetakeDelayHours,
      checkedAt: new Date().toISOString(),
    };

    // If cannot retake, provide helpful information
    if (!canRetake && hoursRemaining) {
      const nextRetakeTime = new Date(
        Date.now() + hoursRemaining * 60 * 60 * 1000
      );
      response['nextRetakeAvailable'] = nextRetakeTime.toISOString();
      response[
        'message'
      ] = `You can retake this assessment in ${hoursRemaining} hours (available after ${nextRetakeTime.toLocaleString()}).`;
    }

    res.status(200).json({
      success: true,
      data: response,
    });
  } catch (error) {
    next(error);
  }
};

/**
 * Generate a new assessment link for retake
 */
export const generateRetakeLink = async (
  req: Request,
  res: Response,
  next: NextFunction
) => {
  try {
    const { email, assessmentId } = req.body;

    if (!email || !assessmentId) {
      throw new AppError({
        httpCode: HttpCode.BAD_REQUEST,
        description: 'Email and assessment ID are required',
      });
    }

    // First check if retake is allowed
    const canRetake = await RetakeDelay.canRetakeAssessment(
      email,
      assessmentId
    );

    if (!canRetake) {
      const hoursRemaining = await RetakeDelay.getHoursRemaining(
        email,
        assessmentId
      );

      throw new AppError({
        httpCode: HttpCode.FORBIDDEN,
        description: `You cannot retake this assessment yet. Please wait ${
          hoursRemaining || 0
        } more hours.`,
      });
    }

    // Generate a new assessment taker ID for the retake
    const retakeAssessmentTakerId = `${email}-${assessmentId}-${Date.now()}`;

    // Create a new assessment taker record for the retake
    const newAssessmentTaker = await prisma.assessmentTaker.create({
      data: {
        id: retakeAssessmentTakerId,
        email,
        assessmentId,
        status: 'IN_PROGRESS',
        startTime: new Date(),
        createdAt: new Date(),
        updatedAt: new Date(),
      },
    });

    res.status(201).json({
      success: true,
      data: {
        assessmentTakerId: retakeAssessmentTakerId,
        assessmentTaker: newAssessmentTaker,
        message: 'Retake assessment link generated successfully',
        generatedAt: new Date().toISOString(),
      },
    });
  } catch (error) {
    next(error);
  }
};

/**
 * Get candidate's assessment history
 */
export const getCandidateAssessmentHistory = async (
  req: Request,
  res: Response,
  next: NextFunction
) => {
  try {
    const { email, assessmentId } = req.query;

    if (!email || !assessmentId) {
      throw new AppError({
        httpCode: HttpCode.BAD_REQUEST,
        description: 'Email and assessment ID are required as query parameters',
      });
    }

    // Get all attempts for this candidate and assessment
    const attempts = await prisma.assessmentTaker.findMany({
      where: {
        email: email as string,
        assessmentId: assessmentId as string,
        endTime: { not: null },
      },
      select: {
        id: true,
        startTime: true,
        endTime: true,
        status: true,
        assessmentDuration: true,
        createdAt: true,
        testResults: {
          select: {
            testPercentage: true,
            totalScore: true,
            totalPassedScore: true,
            passStatus: true,
          },
        },
      },
      orderBy: {
        endTime: 'desc',
      },
    });

    // Calculate overall statistics
    const totalAttempts = attempts.length;
    const completedAttempts = attempts.filter(
      (attempt) => attempt.status === 'COMPLETED'
    ).length;
    const passedAttempts = attempts.filter((attempt) =>
      attempt.testResults.some((result) => result.passStatus === 'PASS')
    ).length;

    // Get current retake eligibility
    const canRetake = await RetakeDelay.canRetakeAssessment(
      email as string,
      assessmentId as string
    );
    const hoursRemaining = await RetakeDelay.getHoursRemaining(
      email as string,
      assessmentId as string
    );

    res.status(200).json({
      success: true,
      data: {
        email,
        assessmentId,
        statistics: {
          totalAttempts,
          completedAttempts,
          passedAttempts,
        },
        attempts: attempts.map((attempt) => ({
          id: attempt.id,
          startTime: attempt.startTime,
          endTime: attempt.endTime,
          status: attempt.status,
          duration: attempt.assessmentDuration,
          results: attempt.testResults,
          createdAt: attempt.createdAt,
        })),
        retakeInfo: {
          canRetake,
          hoursRemaining: hoursRemaining || 0,
          nextRetakeAvailable: hoursRemaining
            ? new Date(
                Date.now() + hoursRemaining * 60 * 60 * 1000
              ).toISOString()
            : null,
        },
      },
    });
  } catch (error) {
    next(error);
  }
};
