import { Request, Response, NextFunction } from 'express';
import prisma from '../prisma';
import { AppError, HttpCode } from '../middlewares/errorHandler';
import { QuestionService } from '../services/questionService';
import logger from '../helpers/logger';
import { AnswerStatus, TestsResult, TestsResults } from '../helpers/types';
import { AssessmentMetricsNormalizer } from '../helpers/assessmentMetricsNormalizer';

export const exportTestTakerResults = async (
  req: Request,
  res: Response,
  next: NextFunction
) => {
  try {
    const { assessmentTakerId, organizationId } = req.params;

    validateParams(assessmentTakerId, organizationId);

    const assessmentTaker = await getAssessmentTaker(
      assessmentTakerId,
      organizationId
    );
    const enhancedTestResults = await processTestResults(assessmentTaker);

    const responseData = buildResponseData(
      assessmentTaker,
      enhancedTestResults
    );

    res.status(200).json({
      success: true,
      data: responseData,
    });
  } catch (error) {
    logger.error('Error exporting test taker results:', error);
    next(error);
  }
};

function validateParams(assessmentTakerId: string, organizationId: string) {
  if (!assessmentTakerId) {
    throw new AppError({
      httpCode: HttpCode.BAD_REQUEST,
      description: 'Assessment taker ID is required',
    });
  }

  if (!organizationId) {
    throw new AppError({
      httpCode: HttpCode.BAD_REQUEST,
      description: 'Organization ID is required',
    });
  }
}

async function getAssessmentTaker(
  assessmentTakerId: string,
  organizationId: string
) {
  const assessmentTaker = await prisma.assessmentTaker.findFirst({
    where: AssessmentMetricsNormalizer.getAssessmentTakerFilter(
      assessmentTakerId,
      organizationId
    ),
    include: {
      testResults: true,
    },
  });

  if (!assessmentTaker) {
    throw new AppError({
      httpCode: HttpCode.NOT_FOUND,
      description: 'Assessment taker not found.',
    });
  }

  if (
    !assessmentTaker.testResults ||
    assessmentTaker.testResults.length === 0
  ) {
    throw new AppError({
      httpCode: HttpCode.NOT_FOUND,
      description: 'No test results found for this assessment taker.',
    });
  }

  return assessmentTaker;
}

async function processTestResults(assessmentTaker) {
  return Promise.all(
    assessmentTaker.testResults.map(async (testResult) => {
      const testResultData = testResult as unknown as TestsResults;
      const questionIds = extractQuestionIds(testResultData);
      const questionDataMap = await QuestionService.getQuestionTextsByIds(
        questionIds,
        assessmentTaker.organizationId
      );

      const enhancedResultData = enhanceResultData(
        testResultData,
        questionDataMap,
        assessmentTaker
      );

      return buildEnhancedTestResult(testResult, enhancedResultData);
    })
  );
}

function enhanceResultData(
  testResultData: TestsResults,
  questionDataMap: Record<string, unknown>,
  assessmentTaker
) {
  return testResultData.result.map((item: TestsResult) => {
    if (item.questionId && questionDataMap[item.questionId]) {
      const questionData = questionDataMap[item.questionId];

      // Handle case where item has answerStatus but not isAnswerCorrect
      if (item.answerStatus && !item.isAnswerCorrect) {
        item.isAnswerCorrect = item.answerStatus;
      }
      // Merge isAnswerCorrect and answerStatus
      else if (typeof item.isAnswerCorrect === 'boolean') {
        item.answerStatus = item.isAnswerCorrect
          ? ('CORRECT' as AnswerStatus)
          : ('WRONG' as AnswerStatus);
      } else if (item.isAnswerCorrect === 'CORRECT') {
        item.answerStatus = 'CORRECT' as AnswerStatus;
      } else if (item.isAnswerCorrect === 'WRONG') {
        item.answerStatus = 'WRONG' as AnswerStatus;
      }
      // Set to SKIPPED if no answer provided
      if (!item.testTakerAnswers || item.testTakerAnswers.length === 0) {
        item.isAnswerCorrect = 'SKIPPED';
        item.answerStatus = 'SKIPPED' as AnswerStatus;
      }
      item.isAnswerCorrect = item.answerStatus;
      return createEnhancedItem(item, questionData, assessmentTaker);
    }
    return item;
  });
}

function createEnhancedItem(item: TestsResult, questionData, assessmentTaker) {
  return {
    questionId: item.questionId,
    organizationId: questionData.organizationId,
    candidateId: assessmentTaker.id,
    candidateEmail: assessmentTaker.email,
    totalScore: questionData.score,
    strictMark: questionData.strictMark,
    questionTitle: questionData.questionTitle,
    candidateMarks: item.scored,
    questionText: questionData.questionText,
    questionType: questionData.questionType,
    difficultyLevel: questionData.difficultyLevel,
    domain: questionData.domain,
    answerStatus: item.answerStatus,
    answered: item.isAnswered,
    category: questionData.category,
    multipleChoiceAnswer: questionData.multipleChoiceAnswer,
    multipleSelectAnswer: questionData.multipleSelectAnswer,
    trueOrFalseAnswer: questionData.trueOrFalseAnswer,
    essayAnswer: questionData.essayAnswer,
    fillInAnswer: questionData.fillInAnswer,
    matchMatrixAnswer: questionData.matchMatrixAnswer,
    codeConstraint: questionData.codeConstraint,
    codeTemplates: questionData.codeTemplates,
    referenceSolution: questionData.referenceSolution,
    isAnswerCorrect: item.isAnswerCorrect,
    testTakerAnswers: item.testTakerAnswers,
    codeReview: item.codeReview,
    codeResults: item.codeResults,
    codeExecutionSummary: item.codeExecutionSummary,
  };
}

function buildEnhancedTestResult(
  testResult: Record<string, unknown>,
  enhancedResultData
) {
  return {
    name: testResult.title,
    questionsAnswered: testResult.numberOfQuestionsAnswered,
    totalNumQuestions: testResult.numberOfQuestions,
    testTime: testResult.duration,
    candidateScore: parseFloat(Number(testResult.totalPassedScore).toFixed(2)),
    overallScore: testResult.totalScore,
    percentage: parseFloat(Number(testResult.testPercentage).toFixed(2)),
    numberOfQuestionsFailed: enhancedResultData.filter(
      (q) => q.isAnswerCorrect === 'WRONG'
    ).length,
    numberOfQuestionsPassed: enhancedResultData.filter(
      (q) => q.isAnswerCorrect === 'CORRECT'
    ).length,
    numberOfQuestionsAnswered: enhancedResultData.filter(
      (q) => q.isAnswerCorrect !== 'SKIPPED'
    ).length,
    numberOfQuestions: testResult.numberOfQuestions,
    testWindowViolationDuration: testResult.testWindowViolationDuration,
    testWindowViolationCount: testResult.testWindowViolationCount,
    testTakerShotCount: testResult.testTakerShotCount,
    testTakerViolationShotCount: testResult.testTakerViolationShotCount,
    testWindowShotCount: testResult.testWindowShotCount,
    testWindowViolationShotCount: testResult.testWindowViolationShotCount,
    status: testResult.status,
    passStatus: testResult.passStatus,
    questionResults: enhancedResultData,
  };
}

function buildResponseData(assessmentTaker, enhancedTestResults) {
  return {
    id: assessmentTaker.id,
    assessmentTitle: assessmentTaker.assessmentName,
    email: assessmentTaker.email,
    status: assessmentTaker.status,
    assessmentStartTime: assessmentTaker.startTime,
    assessmentEndTime: assessmentTaker.endTime,
    commenceDate: assessmentTaker.commenceDate,
    expireDate: assessmentTaker.expireDate,
    assessmentTime: 0,
    assessmentTimeTaken: assessmentTaker.assessmentDuration,
    assessmentCandidateScore: calculateTotalScore(
      assessmentTaker.testResults,
      'totalPassedScore'
    ),
    assessmentOverallScore: calculateTotalScore(
      assessmentTaker.testResults,
      'totalScore'
    ),
    assessmentCandidatePercentage: calculatePercentage(
      assessmentTaker.testResults
    ),
    assessmentWindowViolationCount:
      assessmentTaker.assessmentWindowViolationCount,
    assessmentWindowViolationDuration:
      assessmentTaker.assessmentWindowViolationDuration,
    assessmentTakerShotCount: assessmentTaker.assessmentTakerShotCount,
    assessmentTakerViolationShotCount:
      assessmentTaker.assessmentTakerViolationShotCount,
    windowShotCount: assessmentTaker.windowShotCount,
    windowViolationShotCount: assessmentTaker.windowViolationShotCount,
    screenshotsInterval: assessmentTaker.screenshotsInterval,
    camerashotsInterval: assessmentTaker.camerashotsInterval,
    testResults: enhancedTestResults,
  };
}

function calculateTotalScore(testResults: TestsResult[], scoreType: string) {
  return parseFloat(
    testResults.reduce((sum, test) => sum + test[scoreType], 0).toFixed(2)
  );
}

function calculatePercentage(testResults: TestsResult[]) {
  const totalPassedScore = calculateTotalScore(testResults, 'totalPassedScore');
  const totalScore = calculateTotalScore(testResults, 'totalScore');
  return Number((totalPassedScore / totalScore) * 100).toFixed(2);
}

function extractQuestionIds(testResults: TestsResults): string[] {
  return testResults.result.map((result) => result.questionId);
}
