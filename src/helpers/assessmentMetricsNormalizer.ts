export interface NormalizedCandidate {
  candidateId: string;
  candidateName: string;
  candidateEmail: string;
  assessmentStatus: string;
  startTime: Date | null;
  endTime: Date | null;
  duration: number;
  totalPassedScore: number;
  totalScore: number;
  overallPercentage: number;
  hasTestResults: boolean;
}

export interface NormalizedTest {
  testId: string;
  testTitle: string;
  testScore: number;
  maxTestScore: number;
  testPercentage: number;
  questionsAnswered: number;
  questionCount: number;
  questionsPassed: number;
  questionsFailed: number;
}

export interface AssessmentMetrics {
  totalCandidates: number;
  completedCandidates: number;
  incompleteCandidates: number; // Candidates with INCOMPLETE status
  inProgressCandidates: number;
  candidatesWithResults: number;
  candidatesWhoTookTest: number; // COMPLETED or INCOMPLETE with test results
  averageScore: number;
  averagePercentage: number;
  totalPassedScore: number;
  totalMaxScore: number;
}

export class AssessmentMetricsNormalizer {
  static getCandidateFilter(assessmentId: string, organizationId: string) {
    return {
      AND: [
        { assessmentId, organizationId },
        { email: { not: null } },
        { testResults: { some: {} } },
      ],
    };
  }

  static getCandidateAssessmentsFilter(email: string, assessmentIds: string[]) {
    return {
      email,
      assessmentId: { in: assessmentIds },
      testResults: { some: {} },
    };
  }

  static getAssessmentTakerFilter(
    assessmentTakerId: string,
    organizationId: string
  ) {
    return {
      AND: [
        { id: assessmentTakerId, organizationId },
        { email: { not: null } },
        { testResults: { some: {} } },
      ],
    };
  }

  static normalizeCandidateData(candidate: any): NormalizedCandidate {
    let totalPassedScore = 0;
    let totalScore = 0;
    let overallPercentage = 0;
    const hasTestResults = Boolean(
      candidate.testResults && candidate.testResults.length > 0
    );

    if (hasTestResults) {
      // Calculate from test results for accuracy
      candidate.testResults.forEach((testResult: any) => {
        totalPassedScore += testResult.totalPassedScore || 0;
        totalScore += testResult.totalScore || 0;
      });
      overallPercentage =
        totalScore > 0 ? (totalPassedScore / totalScore) * 100 : 0;
    } else {
      overallPercentage = Number(candidate.assessmentTakerScorePercentage) || 0;
      totalPassedScore = Number(candidate.assessmentTakerScore) || 0;
      totalScore = Number(candidate.assessmentScore) || 0;
    }

    return {
      candidateId: candidate.id,
      candidateName: candidate.email?.split('@')[0] || 'Unknown',
      candidateEmail: candidate.email,
      assessmentStatus: candidate.status,
      startTime: candidate.startTime,
      endTime: candidate.endTime,
      duration: candidate.duration || candidate.assessmentDuration || 0,
      totalPassedScore: parseFloat(totalPassedScore.toFixed(2)),
      totalScore: parseFloat(totalScore.toFixed(2)),
      overallPercentage: parseFloat(overallPercentage.toFixed(2)),
      hasTestResults,
    };
  }

  static normalizeTestData(testResult: any): NormalizedTest {
    const questionsAnswered =
      testResult.result?.filter((q: any) => q.isAnswered).length || 0;
    const questionCount =
      testResult.result?.length || testResult.numberOfQuestions || 0;
    const questionsPassed =
      testResult.result?.filter((q: any) => q.isAnswerCorrect).length || 0;
    const questionsFailed = questionsAnswered - questionsPassed;

    return {
      testId: testResult.testId,
      testTitle: testResult.title,
      testScore: parseFloat((testResult.totalPassedScore || 0).toFixed(2)),
      maxTestScore: parseFloat((testResult.totalScore || 0).toFixed(2)),
      testPercentage: parseFloat((testResult.testPercentage || 0).toFixed(2)),
      questionsAnswered,
      questionCount,
      questionsPassed,
      questionsFailed,
    };
  }

  static calculateAssessmentMetrics(
    candidates: NormalizedCandidate[]
  ): AssessmentMetrics {
    const totalCandidates = candidates.length;
    const completedCandidates = candidates.filter(
      (c) => c.assessmentStatus === 'COMPLETED'
    ).length;
    const incompleteCandidates = candidates.filter(
      (c) => c.assessmentStatus === 'INCOMPLETE'
    ).length;
    const inProgressCandidates = candidates.filter(
      (c) => c.assessmentStatus === 'IN_PROGRESS'
    ).length;
    const candidatesWithResults = candidates.filter(
      (c) => c.hasTestResults
    ).length;

    // Count candidates who actually took the test (COMPLETED or INCOMPLETE with test results)
    const candidatesWhoTookTest = candidates.filter(
      (c) =>
        c.assessmentStatus === 'COMPLETED' ||
        (c.assessmentStatus === 'INCOMPLETE' && c.hasTestResults)
    ).length;

    const totalPassedScore = candidates.reduce(
      (sum, c) => sum + c.totalPassedScore,
      0
    );
    const totalMaxScore = candidates.reduce((sum, c) => sum + c.totalScore, 0);

    const avgPercentage =
      totalCandidates > 0
        ? candidates.reduce((sum, c) => sum + c.overallPercentage, 0) /
          totalCandidates
        : 0;

    return {
      totalCandidates,
      completedCandidates,
      incompleteCandidates,
      inProgressCandidates,
      candidatesWithResults,
      candidatesWhoTookTest, // New metric for candidates who actually took the test
      averageScore: parseFloat(avgPercentage.toFixed(2)),
      averagePercentage: parseFloat(avgPercentage.toFixed(2)),
      totalPassedScore: parseFloat(totalPassedScore.toFixed(2)),
      totalMaxScore: parseFloat(totalMaxScore.toFixed(2)),
    };
  }

  static calculateCompletedCandidateMetrics(
    candidates: NormalizedCandidate[]
  ): AssessmentMetrics {
    const completedCandidates = candidates.filter(
      (c) => c.assessmentStatus === 'COMPLETED'
    );
    return this.calculateAssessmentMetrics(completedCandidates);
  }

  static calculateCandidatesWithResultsMetrics(
    candidates: NormalizedCandidate[]
  ): AssessmentMetrics {
    const candidatesWithResults = candidates.filter((c) => c.hasTestResults);
    return this.calculateAssessmentMetrics(candidatesWithResults);
  }

  static calculateCandidatesWhoTookTestMetrics(
    candidates: NormalizedCandidate[]
  ): AssessmentMetrics {
    const candidatesWhoTookTest = candidates.filter(
      (c) =>
        c.assessmentStatus === 'COMPLETED' ||
        (c.assessmentStatus === 'INCOMPLETE' && c.hasTestResults)
    );
    return this.calculateAssessmentMetrics(candidatesWhoTookTest);
  }

  static calculateAssessmentLinksStats(
    allCandidates: any[],
    passMark: number,
    totalInvited: number
  ) {
    const completedAssessments = allCandidates.filter(
      (c) => c.status === 'COMPLETED' || c.status === 'INCOMPLETE'
    );

    const totalCandidates = completedAssessments.length;
    const totalCompletedCandidates = completedAssessments.filter(
      (c) => c.status === 'COMPLETED'
    ).length;

    const averageScore =
      totalCandidates > 0
        ? completedAssessments.reduce(
            (sum, candidate) =>
              sum + (Number(candidate.assessmentTakerScorePercentage) || 0),
            0
          ) / totalCandidates
        : 0;

    const passedCandidates = completedAssessments.filter(
      (c) => Number(c.assessmentTakerScorePercentage) >= Number(passMark)
    ).length;

    const passRate =
      totalCandidates > 0 ? (passedCandidates / totalCandidates) * 100 : 0;
    const completionRate =
      totalInvited > 0 ? (totalCompletedCandidates / totalInvited) * 100 : 0;

    return {
      averageScore: averageScore.toFixed(2),
      totalCandidatesInvited: totalInvited,
      totalCandidatesCompleted: totalCompletedCandidates,
      passRate: passRate.toFixed(2),
      completionRate: completionRate.toFixed(2),
      passMark,
    };
  }
}
