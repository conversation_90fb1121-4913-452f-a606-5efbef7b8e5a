import Joi from 'joi';

export const testIdSchema = Joi.string().required().messages({
  'string.empty': 'TestId is required',
  'any.required': 'TestId is required',
  'string.base': 'Provide a valid TestId',
  'string.guid': 'Test not found',
});
export const questionIdSchema = Joi.string().required().messages({
  'string.empty': 'QuestionId is required',
  'any.required': 'QuestionId is required',
  'string.base': 'Provide a valid QuestionId',
  'string.guid': 'Question not found',
});
export const questionTextSchema = Joi.string().required().messages({
  'string.empty': 'Provide flagged question',
  'any.required': 'Provide flagged question',
  'string.base': 'Provide flagged question',
});
export const flagReasonSchema = Joi.string().required().messages({
  'string.empty': 'Provide reason for flagging question',
  'any.required': 'Provide reason for flagging question',
  'string.base': 'Provide reason for flagging question',
});

export const questionTypeSchema = Joi.string()
  .valid(
    'Multiple_choice',
    'True_or_false',
    'Multi_select',
    'Essay',
    'Fill_in',
    'Matrix',
    'Code'
  )
  .required();

export const MatrixTestTakerAnswerSchema = Joi.object({
  subquestionId: Joi.string().guid(),
  answers: Joi.array().items(Joi.string().allow('')).required(),
}).required();

// this schema is required
export const testResultSchema = Joi.object({
  testId: testIdSchema,
  questionResults: Joi.array()
    .items(
      Joi.object({
        questionId: questionIdSchema,
        questionType: questionTypeSchema,
        testTakerAnswers: Joi.required().when('questionType', {
          is: 'Matrix',
          then: Joi.array().items(MatrixTestTakerAnswerSchema).required(),
          otherwise: Joi.when('questionType', {
            is: 'Code',
            then: Joi.array()
              .items(
                Joi.object({
                  code: Joi.string().required(),
                  languageId: Joi.number().required(),
                })
              )
              .required(),
            otherwise: Joi.array().items(Joi.string().allow('')).required(),
          }),
        }),
        idleTime: Joi.number().required(),
      })
    )
    .required(),
}).required();

export const nonViolationScreenShotSchema = Joi.object({
  imageURL: Joi.string().uri().required(),
  violation: Joi.boolean().valid(false).required(),
  timeStamp: Joi.date().required(),
  isIntegrityShot: Joi.boolean().required(),
});
export const nonViolationCandidateShotSchema = Joi.object({
  imageURL: Joi.string().uri().required(),
  violation: Joi.boolean().valid(false).required(),
  timeStamp: Joi.date().required(),
});

export const violationScreenShotSchema = Joi.object({
  violationNumber: Joi.number().required(),
  violation: Joi.boolean().valid(true).required(),
  testId: testIdSchema,
  isIntegrityShot: Joi.boolean().required(),
  testNumber: Joi.number().required(),
  questionId: questionIdSchema,
  questNumber: Joi.number().required(),
  startTime: Joi.date().required(),
  endTime: Joi.date().required(),
  shots: Joi.array().items(
    Joi.object({
      imageURL: Joi.string().uri().required(),
      timeStamp: Joi.date().required().required(),
    })
  ),
});
export const violationCandidateShotSchema = Joi.object({
  violationNumber: Joi.number().required(),
  violation: Joi.boolean().valid(true).required(),
  testId: testIdSchema,
  testNumber: Joi.number().required(),
  questionId: questionIdSchema,
  questNumber: Joi.number().required(),
  startTime: Joi.date().required(),
  endTime: Joi.date().required(),
  shots: Joi.array().items(
    Joi.object({
      imageURL: Joi.string().uri().required(),
      timeStamp: Joi.date().required().required(),
    })
  ),
});

export const ScreenshotSchema = Joi.alternatives().try(
  nonViolationScreenShotSchema,
  violationScreenShotSchema
);

export const CandidateShotSchema = Joi.alternatives().try(
  nonViolationCandidateShotSchema,
  violationCandidateShotSchema
);

export const identitySchema = Joi.object({
  linkId: Joi.string().uri().required(),
  linkHead: Joi.string().uri().required(),
});

export const submitTestSchema = Joi.object({
  startTime: Joi.date().required(),
  finishTime: Joi.date().required(),
  testResults: Joi.array().items(testResultSchema).length(1),
  screenMonitoring: Joi.array().items(ScreenshotSchema),
  candidateMonitoring: Joi.array().items(CandidateShotSchema),
  isAutoSubmission: Joi.boolean(),
});

export const draftAssessmentSchema = Joi.object({
  testResults: Joi.array().items(testResultSchema),
  screenMonitoring: Joi.array().items(ScreenshotSchema),
  candidateMonitoring: Joi.array().items(CandidateShotSchema),
});
