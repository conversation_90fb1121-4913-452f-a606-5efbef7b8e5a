import express from 'express';
import jwt, { JsonWebTokenError, TokenExpiredError } from 'jsonwebtoken';
import { RequestExtension, UnEncryptedJwtPayload } from '../helpers/types';
import jwtPayloadEncryption from '../helpers/jwtPayloadEncryption';
import accountStatus from '../helpers/accountStatus';
import axios from 'axios';
import sentry from '@sentry/node';
interface User {
  userId?: string;
  email: string;
  role?: string;
  system?: boolean;
  organizationId: string;
  permissions: string[];
}

// Extend the Request interface to include the `user` property
// Middleware to check token expiration
export const authenticate = async (
  req: RequestExtension,
  res: express.Response,
  next: express.NextFunction
) => {
  try {
    const accessToken = req.headers.authorization?.split(' ')[1];
    if (!accessToken) {
      return res.status(400).send({
        success: false,
        message: 'Missing token',
      });
    }

    const unencryptedPayload: UnEncryptedJwtPayload = jwt.verify(
      accessToken,
      process.env.JWT_SECRET
    ) as UnEncryptedJwtPayload;

    const decoded = jwtPayloadEncryption.decryptPayload(unencryptedPayload);

    req.user = decoded as User;
    await accountStatus.checkAccountStatus(req);
  } catch (err) {
    if (err instanceof TokenExpiredError) {
      return res.status(401).send({
        success: false,
        message: 'Expired Token',
      });
    }
    if (err instanceof JsonWebTokenError) {
      return res.status(401).send({
        success: false,
        message: 'Invalid Token',
      });
    }

    next(err);
  }
  next();
};

export const apiTokenValidation = async (
  req: RequestExtension,
  res: express.Response,
  next: express.NextFunction
) => {
  try {
    const apiToken = req.headers['x-api-key'];
    if (!apiToken) {
      sentry.captureMessage('Missing token');
      return res.status(400).send({
        success: false,
        message: 'Missing API Token',
      });
    }

    const baseURL = `${process.env.API_INTEGRATION_URL}`;
    if (!baseURL) {
      sentry.captureMessage('Missing baseUrl');
      return res.status(500).send({
        success: false,
        message: 'API Integration URL is not configured',
      });
    }

    const response = await axios.get(baseURL, {
      headers: {
        'x-api-key': apiToken,
      },
    });

    if (response.status !== 200) {
      sentry.captureMessage('Invalid API Token');
      return res.status(401).send({
        success: false,
        message: 'Invalid API Tokens',
      });
    }
    next();
  } catch (error) {
    if (axios.isAxiosError(error)) {
      sentry.captureMessage('Throw Axios Error');
      return res.status(error.response?.status || 500).send({
        success: false,
        message: error.response?.data?.message || 'Error validating API Token',
      });
    }
    sentry.captureMessage(error.message);
    return res.status(500).send({
      success: false,
      message: 'Internal Server Error',
    });
  }
};
