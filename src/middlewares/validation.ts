import Joi from 'joi';
import { NextFunction, Request, Response } from 'express';
import {
  draftAssessmentSchema,
  flagReasonSchema,
  identitySchema,
  questionIdSchema,
  questionTextSchema,
  submitTestSchema,
  testIdSchema,
} from './validationSchemas';
import { ErrorMessage, RequestExtension } from '../helpers/types';
import { getCachedData } from '../helpers/redisCache';
import { AppError, HttpCode } from './errorHandler';

export const validateCreateFlagQuestion = (
  req: Request,
  res: Response,
  next: NextFunction
) => {
  const schema = Joi.object({
    testId: testIdSchema,
    questionId: questionIdSchema,
    questionText: questionTextSchema,
    reasonOfFlagging: Joi.array().items(flagReasonSchema).min(1).messages({
      'array.min': 'Provide at least one reason for flagging question',
      'array.includesRequiredUnknowns': 'Provide reasons for flagging question',
    }),
  });

  const { error } = schema.validate(req.body);
  if (error) {
    return res.status(400).json({
      success: false,
      message: error.details[0].message,
    });
  }
  next();
};

export const validateCreateUnflagQuestion = (
  req: Request,
  res: Response,
  next: NextFunction
) => {
  const schema = Joi.object({
    testId: testIdSchema,
    questionId: questionIdSchema,
  });

  const { error } = schema.validate(req.body);
  if (error) {
    return res.status(400).json({
      success: false,
      message: error.details[0].message,
    });
  }
  next();
};

export const sampleTestValidation = (
  req: Request,
  res: Response,
  next: NextFunction
) => {
  try {
    const schema = Joi.object({
      test: Joi.object({
        questions: Joi.array().items(
          Joi.object({
            questionType: Joi.string()
              .valid('Multiple_choice', 'True_or_false', 'Multi_select')
              .required(),
            score: Joi.number().required(),
            answer: Joi.array().items(Joi.string().required()).required(),
            testTakerAnswer: Joi.array().items(Joi.string()).required(),
          })
        ),
      }).required(),
    });

    const { value, error } = schema.validate(req.body);
    if (error) {
      return res.status(400).json({
        success: false,
        message: error.details[0].message,
      });
    }
    req.body = value;
    next();
  } catch (error) {
    next(error);
  }
};
export const submitAssessmentValidation = (
  req: Request,
  res: Response,
  next: NextFunction
) => {
  try {
    const schema = submitTestSchema;
    const { error, value } = schema.validate(req.body);

    if (error) {
      return res.status(400).json({
        success: false,
        message: error.details[0].message,
      });
    }
    req.body = value;
    next();
  } catch (error) {
    next(error);
  }
};

export const submitAssessmentTestValidation = (
  req: Request,
  res: Response,
  next: NextFunction
) => {
  try {
    const schema = submitTestSchema.required();

    const { error, value } = schema.validate(req.body);
    if (error) {
      return res.status(400).json({
        success: false,
        message: error.details[0].message,
      });
    }
    req.body = value;
    next();
  } catch (error) {
    next(error);
  }
};

export const assessmentInProgressValidation = (
  req: RequestExtension,
  res: Response,
  next: NextFunction
) => {
  try {
    const { proctorFeatures } = req.assessmentTakerInfo;
    const schema = Joi.object({
      proctorFeatures: Joi.array().items(
        Joi.string().valid(
          'Window Violation',
          'Honour Code',
          'Idle Time Tracking',
          'Screen Capture',
          'ID Capture',
          'Candidate Capture'
        )
      ),
      identity: Joi.when('proctorFeatures', {
        is: Joi.array()
          .items(Joi.string())
          .has(Joi.string().valid('ID Capture')),
        then: identitySchema.required(),
        otherwise: Joi.when('proctorFeatures', {
          is: Joi.array()
            .items(Joi.string())
            .has(Joi.string().valid('Candidate Capture')),
          then: Joi.object({
            linkHead: Joi.string().required(),
          }).required(),
          otherwise: Joi.forbidden(),
        }),
      }),
    });

    const { error, value } = schema.validate({ ...req.body, proctorFeatures });

    if (error) {
      return res.status(400).json({
        success: false,
        message: error.details[0].message,
      });
    }

    delete req.body.proctorFeatures;

    req.body = value;
    next();
  } catch (error) {
    next(error);
  }
};

export const fingerprintValidation = async (
  req: RequestExtension,
  res: Response,
  next: NextFunction
) => {
  const fingerprint = req.headers['x-device-fingerprint'];
  const assessmentTakerId = req.params.assessmentTakerId;
  const redisKey = `fingerprint:${assessmentTakerId}`;

  const storedFingerprint = await getCachedData(redisKey);

  if (storedFingerprint && storedFingerprint !== fingerprint) {
    throw new AppError({
      httpCode: HttpCode.FORBIDDEN,
      description: ErrorMessage.INVALID_DEVICE,
    });
  }

  next();
};

export const draftCandidateAnswerValidation = (
  req: Request,
  res: Response,
  next: NextFunction
) => {
  try {
    const { error, value } = draftAssessmentSchema.validate(req.body);

    if (error) {
      return res.status(400).json({
        success: false,
        message: error.details[0].message,
      });
    }
    req.body = value;
    next();
  } catch (error) {
    next(error);
  }
};

export const getPresignedURLValidation = async (
  req: Request,
  res: Response,
  next: NextFunction
) => {
  try {
    const schema = Joi.object({
      mimeType: Joi.string()
        .required()
        .valid('application/pdf', 'image/jpeg', 'image/png'),
      keyType: Joi.string()
        .required()
        .valid(
          'head-shot',
          'id-shot',
          'candidate-monitoring',
          'screen-monitoring'
        ),
      contentMD5: Joi.string().required(),
    });

    const { error } = schema.validate(req.body);
    if (error) {
      return res.status(400).json({
        success: false,
        message: error.details[0].message,
      });
    }

    next();
  } catch (error) {
    next(error);
  }
};

export const verifyTestTakerValidation = (
  req: Request,
  res: Response,
  next: NextFunction
) => {
  try {
    const schema = Joi.object({
      email: Joi.string().email().required().messages({
        'string.email': 'Please provide a valid email address',
        'string.empty': 'Email is required',
        'any.required': 'Email is required',
      }),
      assessmentTakerId: Joi.string().guid().required().messages({
        'string.guid': 'Invalid assessment taker ID format',
        'string.empty': 'Assessment taker ID is required',
        'any.required': 'Assessment taker ID is required',
      }),
    });

    const { error } = schema.validate(req.body);
    if (error) {
      return res.status(400).json({
        success: false,
        message: error.details[0].message,
      });
    }

    next();
  } catch (error) {
    next(error);
  }
};
