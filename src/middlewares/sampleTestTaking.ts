import { NextFunction, Request, Response } from 'express';

export const sampleTestQuestionFlaggingCheck = (
  req: Request,
  res: Response,
  next: NextFunction
) => {
  try {
    const { sampleTestMode } = req.body as {
      sampleTestMode: boolean;
    };
    if (sampleTestMode) {
      return res.status(201).json({
        success: true,
        data: {
          message: 'Question flagged successfully',
        },
      });
    }
    next();
  } catch (error) {
    next(error);
  }
};

export const sampleTestQuestionUnflaggingCheck = (
  req: Request,
  res: Response,
  next: NextFunction
) => {
  try {
    const { sampleTestMode } = req.body as {
      sampleTestMode: boolean;
    };
    if (sampleTestMode) {
      return res.status(201).json({
        success: true,
        data: {
          message: 'Question unflagged successfully',
        },
      });
    }
    next();
  } catch (error) {
    next(error);
  }
};
