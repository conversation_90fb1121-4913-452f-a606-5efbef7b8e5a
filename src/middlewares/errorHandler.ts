import express from 'express';
import * as winston from 'winston';
import { sendMail } from '../helpers/sendmail';

export const enum HttpCode {
  OK = 200,
  NO_CONTENT = 204,
  BAD_REQUEST = 400,
  UNAUTHORIZED = 401,
  FORBIDDEN = 403,
  NOT_FOUND = 404,
  REQUEST_TIMEOUT = 408,
  TOO_MANY_REQUESTS = 429,
  INTERNAL_SERVER_ERROR = 500,
  BAD_GATEWAY = 502,
  SERVICE_UNAVAILABLE = 503,
  GATEWAY_TIMEOUT = 504,
}
interface AppErrorArgs {
  name?: string;
  httpCode: HttpCode;
  description: string;
  isOperational?: boolean;
  data?: object;
}

export class AppError extends Error {
  public readonly name: string;
  public httpCode: HttpCode;
  public readonly isOperational: boolean = true;
  public data: object = {};
  constructor(args: AppErrorArgs) {
    super(args.description);

    Object.setPrototypeOf(this, new.target.prototype);

    this.name = args.name || 'Error';
    this.httpCode = args.httpCode;
    this.data = args.data;

    if (args.isOperational !== undefined) {
      this.isOperational = args.isOperational;
    }
    Error.captureStackTrace(this, this.constructor);
  }
}

class ErrorHandler {
  // configure log format
  private readonly logFormat = winston.format.combine(
    winston.format.timestamp({ format: 'YYYY-MM-DD HH:mm:ss' }),
    winston.format.json(),
    winston.format.prettyPrint()
  );

  private readonly logger = winston.createLogger({
    level: 'error', // Set the desired log level
    format: this.logFormat,
    transports: [
      new winston.transports.Console(),
      new winston.transports.File({ level: 'error', filename: 'error.log' }), // log to file
    ],
  });
  //send mail to admin when error logs file
  private async sendMailToDevelopers(
    err: AppError | Error,
    req: express.Request
  ) {
    try {
      await sendMail(
        [
          process.env.DEV_EMAIL_1,
          process.env.DEV_EMAIL_2,
          process.env.DEV_EMAIL_3,
        ],
        'Application Server Error',
        `<p>error: ${err.message}, path: ${req.method} ${req.path}<p>`,
        [{ filename: 'error.log', path: './error.log' }]
      );
    } catch (error) {
      this.logger.error({
        error: 'Error sending mail to developers',
        path: `${req.method}: ${req.path}`,
        stackTrace: error,
      });
    }
  }

  // check if error is operational
  private isTrustedError(err: Error) {
    if (err instanceof AppError) {
      return err.isOperational;
    }
    return false;
  }

  // handle trusted errors
  private handleTrustedError(err: AppError, res: express.Response) {
    res
      .status(err.httpCode)
      .send({ success: false, message: err.message, data: err.data });
  }
  // handle untrusted errors
  private handleCriticalError(err: Error | AppError, res?: express.Response) {
    //send response if response object exists
    if (res) {
      res
        .status(HttpCode.INTERNAL_SERVER_ERROR)
        .send({ success: false, message: 'Internal Server Error' });
    }
    //finally exit the process
    process.exit(1);
  }
  async handleError(
    err: Error | AppError,
    req?: express.Request,
    res?: express.Response
  ): Promise<void> {
    if (this.isTrustedError(err) && res) {
      this.handleTrustedError(err as AppError, res);
    } else {
      this.logger.error({
        error: err,
        path: `${req.method}: ${req.path}`,
      });
      await this.sendMailToDevelopers(err, req);
      this.handleCriticalError(err, res);
    }
  }
}

export default new ErrorHandler();
