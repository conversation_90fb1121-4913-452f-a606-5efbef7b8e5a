import { NextFunction, Response } from 'express';
import { validate as UUID_validator } from 'uuid';
import { AppError, HttpCode } from './errorHandler';
import prisma from '../prisma';
import { ONE_DAY, RequestExtension, ErrorMessage } from '../helpers/types';
import { getCachedData, setCacheData } from '../helpers/redisCache';

export const assessmentTakerAccessPermission = async (
  req: RequestExtension,
  res: Response,
  next: NextFunction
) => {
  try {
    const { assessmentTakerId } = req.params;
    const url = req.url;

    if (!UUID_validator(assessmentTakerId)) {
      return res.status(HttpCode.FORBIDDEN).json({
        success: false,
        message: ErrorMessage.CANDIDATE_NOT_FOUND,
      });
    }

    const assessmentTakerExist = await prisma.assessmentTaker.findFirst({
      where: {
        id: assessmentTakerId,
      },
    });

    if (!assessmentTakerExist) {
      return res.status(HttpCode.FORBIDDEN).json({
        success: false,
        message: ErrorMessage.CANDIDATE_NOT_FOUND,
      });
    }
    const isGettingResult = url.includes('assessment-result');
    if (
      isGettingResult &&
      assessmentTakerExist.phase !== 'ASSESSMENT_COMPLETION'
    ) {
      throw new AppError({
        httpCode: HttpCode.BAD_REQUEST,
        description: ErrorMessage.ASSESSMENT_RESULTS_UNAVAILABLE,
      });
    }
    if (isGettingResult && req.method === 'GET') {
      req.assessmentTakerInfo = assessmentTakerExist;
      return next();
    }

    const isSubmitSurvey = url.includes('survey');
    if (isSubmitSurvey && req.method === 'POST') {
      req.assessmentTakerInfo = assessmentTakerExist;
      return next();
    }
    if (assessmentTakerExist.phase === 'ASSESSMENT_COMPLETION') {
      throw new AppError({
        httpCode: HttpCode.UNAUTHORIZED,
        description: ErrorMessage.ASSESSMENT_COMPLETED,
      });
    }
    req.assessmentTakerInfo = assessmentTakerExist;
    next();
  } catch (error) {
    next(error);
  }
};

export const fingerprintValidation = async (
  req: RequestExtension,
  res: Response,
  next: NextFunction
) => {
  try {
    const fingerprint = req.headers['x-device-fingerprint'];
    const assessmentTakerId = req.params.assessmentTakerId;

    if (!fingerprint) {
      throw new AppError({
        httpCode: HttpCode.FORBIDDEN,
        description: ErrorMessage.ACCESS_DENIED,
      });
    }

    if (!assessmentTakerId) {
      throw new AppError({
        httpCode: HttpCode.FORBIDDEN,
        description: ErrorMessage.INVALID_ACCESS,
      });
    }

    const redisKey = `fingerprint:${assessmentTakerId}`;
    const storedFingerprint = await getCachedData(redisKey);
    const assessmentPhase = await prisma.assessmentTaker.findUnique({
      where: {
        id: assessmentTakerId,
      },
      select: {
        phase: true,
      },
    });

    if (assessmentPhase) {
      if (
        storedFingerprint &&
        assessmentPhase.phase === 'TEST_TAKING' &&
        storedFingerprint !== fingerprint
      ) {
        throw new AppError({
          httpCode: HttpCode.FORBIDDEN,
          description: ErrorMessage.INVALID_DEVICE,
        });
      }
      if (
        assessmentPhase.phase !== 'TEST_TAKING' &&
        (!storedFingerprint || storedFingerprint !== fingerprint)
      ) {
        await setCacheData(redisKey, fingerprint, ONE_DAY);
      }
    } else {
      await setCacheData(redisKey, fingerprint, ONE_DAY);
    }

    next();
  } catch (error) {
    next(error);
  }
};
