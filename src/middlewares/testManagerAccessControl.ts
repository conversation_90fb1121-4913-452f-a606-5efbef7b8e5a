import { Response, NextFunction } from 'express';
import { AppError, HttpCode } from './errorHandler';
import { RequestExtension } from '../helpers/types';
import { AppPermissions } from '../helpers/defaultPermission';
import prisma from '../prisma';
import { validate as UUID_validator } from 'uuid';

export const testManagerAccessControl = async (
  req: RequestExtension,
  res: Response,
  next: NextFunction
) => {
  try {
    const { assessmentTakerId } = req.params;
    if (!UUID_validator(assessmentTakerId)) {
      return res.status(HttpCode.FORBIDDEN).json({
        success: false,
        message: 'Candidate not found',
      });
    }

    const asssessmentTakerExist = await prisma.assessmentTaker.findFirst({
      where: {
        id: assessmentTakerId,
      },
    });

    if (!asssessmentTakerExist) {
      return res.status(HttpCode.FORBIDDEN).json({
        success: false,
        message: 'Candidate not found',
      });
    }

    if (asssessmentTakerExist.organizationId !== req.user.organizationId) {
      throw new AppError({
        httpCode: HttpCode.FORBIDDEN,
        description: `Access denied. Contact your organization's admin.`,
        isOperational: true,
      });
    }
    const { permissions } = req.user;
    if (
      permissions.includes(AppPermissions.admin) ||
      permissions.includes(AppPermissions.organizationAdmin) ||
      permissions.includes(AppPermissions.manageAssessments) ||
      permissions.includes(AppPermissions.viewAssessments) ||
      permissions.includes(AppPermissions.viewReports) ||
      permissions.includes(AppPermissions.manageReports)
    ) {
      req.assessmentTakerInfo = asssessmentTakerExist;
      return next();
    }
    throw new AppError({
      httpCode: HttpCode.FORBIDDEN,
      description: `Access denied. Contact your organization's admin.`,
      isOperational: true,
    });
  } catch (error) {
    next(error);
  }
};

export const ResultsAccessControl = async (
  req: RequestExtension,
  res: Response,
  next: NextFunction
) => {
  try {
    const { permissions } = req.user;
    if (
      permissions.includes(AppPermissions.admin) ||
      permissions.includes(AppPermissions.organizationAdmin) ||
      permissions.includes(AppPermissions.manageAssessments) ||
      permissions.includes(AppPermissions.viewAssessments) ||
      permissions.includes(AppPermissions.viewReports) ||
      permissions.includes(AppPermissions.manageReports)
    ) {
      return next();
    }
    throw new AppError({
      httpCode: HttpCode.FORBIDDEN,
      description: `Access denied. Contact your organization's admin.`,
      isOperational: true,
    });
  } catch (error) {
    next(error);
  }
};
