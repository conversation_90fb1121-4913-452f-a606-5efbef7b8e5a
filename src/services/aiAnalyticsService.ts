import axios from 'axios';
import { AppError, HttpCode } from '../middlewares/errorHandler';
import logger from '../helpers/logger';
import prisma from '../prisma';
import puppeteer from 'puppeteer';
import {
  AIAnalysisResponse,
  AnalysisRequest,
  AssessmentStats,
} from '../helpers/types';
import { analysisTemplate } from '../helpers/template/aiAnalysis';

export class AIAnalyticsService {
  private static readonly AI_ENDPOINT = process.env.AI_URL;

  static async generateAnalyticsReport(data: AnalysisRequest): Promise<Buffer> {
    try {
      // Get AI analysis
      const aiAnalysis = await this.getAIAnalysis(data);

      // Get feedback data from either analysis field or parse from feedback string
      let feedbackData;

      if (aiAnalysis.analysis) {
        // Use the analysis field directly if available
        feedbackData = aiAnalysis.analysis;
        logger.info('Using analysis field from AI response');
      } else if (aiAnalysis.feedback) {
        // Try to parse the feedback JSON string if analysis field is not available
        try {
          // The feedback comes as a string with 'json' at the beginning
          const jsonString = aiAnalysis.feedback.replace(/^json\s*/, '');
          feedbackData = JSON.parse(jsonString);
          logger.info('Successfully parsed feedback JSON string');
        } catch (parseError) {
          logger.error('Error parsing feedback JSON:', parseError);
          feedbackData = {
            overall_performance: 'Unable to parse feedback data',
            strengths: [],
            weaknesses: [],
            development_recommendations: [],
            priority_learning_areas: [],
            suggested_learning_path: [],
          };
        }
      } else {
        // No analysis data available
        logger.warn('No analysis or feedback data available in AI response');
        feedbackData = {
          overall_performance: 'No analysis data available',
          strengths: [],
          weaknesses: [],
          development_recommendations: [],
          priority_learning_areas: [],
          suggested_learning_path: [],
        };
      }

      // Get assessment stats
      const assessmentStats = await this.getAssessmentStats(data);

      // Generate HTML report using the template
      const htmlContent = this.generateHTMLReportFromTemplate(
        aiAnalysis,
        assessmentStats,
        feedbackData
      );

      // Convert to PDF
      const pdfBuffer = await this.generatePDF(htmlContent);

      return pdfBuffer;
    } catch (error) {
      logger.error('Error generating analytics report:', error);
      throw new AppError({
        httpCode: HttpCode.INTERNAL_SERVER_ERROR,
        description: `Failed to generate analytics report:${error}`,
      });
    }
  }

  private static async getAIAnalysis(
    data: AnalysisRequest
  ): Promise<AIAnalysisResponse> {
    try {
      const requestBody = await this.buildAIRequestBody(data);
      logger.info(
        'Sending request to AI service:',
        JSON.stringify(requestBody, null, 2)
      );

      const response = await axios.post(
        `${this.AI_ENDPOINT}/assessment/analyze-test`,
        requestBody,
        {
          timeout: 30000,
          headers: {
            'Content-Type': 'application/json',
          },
          responseType: 'json',
        }
      );

      if (
        typeof response.data === 'string' &&
        response.data.startsWith('%PDF')
      ) {
        throw new Error('AI service returned PDF instead of JSON analysis');
      }

      logger.info('AI service response received');

      return response.data;
    } catch (error: any) {
      logger.error('AI analysis request failed:', {
        message: error.message,
        response: error.response?.data,
        status: error.response?.status,
      });
      throw new AppError({
        httpCode: HttpCode.SERVICE_UNAVAILABLE,
        description: 'AI analysis service unavailable',
      });
    }
  }

  private static async buildAIRequestBody(data: AnalysisRequest) {
    const assessmentTaker = await prisma.assessmentTaker.findUnique({
      where: { id: data.assessmentTakerId },
      include: {
        testResults: true,
      },
    });

    if (!assessmentTaker) {
      throw new AppError({
        httpCode: HttpCode.NOT_FOUND,
        description: 'Assessment taker not found',
      });
    }

    const sections = assessmentTaker.testResults.map((testResult) => ({
      section_name: testResult.title,
      questions: testResult.result.map((result: any) => ({
        question_id: result.questionId,
        question_detail: result.questionText ?? 'N/A',
        answer_options: result.answerOptions ?? 'N/A',
        correct_answer: result.correctAnswer ?? 'N/A',
        candidate_answer: Array.isArray(result.testTakerAnswers)
          ? result.testTakerAnswers.join(', ')
          : result.testTakerAnswers ?? 'N/A',
        question_score: result.score ?? 0,
        candidate_score: result.scored ?? 0,
        answer_status: result.answerStatus ?? 'UNANSWERED',
      })),
    }));

    return {
      candidate_email: assessmentTaker.email,
      assessment_name: assessmentTaker.assessmentName,
      sections,
    };
  }

  private static async getAssessmentStats(
    data: AnalysisRequest
  ): Promise<AssessmentStats> {
    const assessmentTaker = await prisma.assessmentTaker.findUnique({
      where: { id: data.assessmentTakerId },
      include: { testResults: true },
    });

    if (!assessmentTaker) {
      throw new AppError({
        httpCode: HttpCode.NOT_FOUND,
        description: 'Assessment taker not found',
      });
    }

    // Get all assessment takers for this assessment to calculate average and highest
    const allTakers = await prisma.assessmentTaker.findMany({
      where: { assessmentId: assessmentTaker.assessmentId },
      include: { testResults: true },
    });

    const candidateScore = assessmentTaker.testResults.reduce(
      (sum, test) => sum + test.totalPassedScore,
      0
    );
    const candidateMaxScore = assessmentTaker.testResults.reduce(
      (sum, test) => sum + test.totalScore,
      0
    );
    const candidatePercentage =
      candidateMaxScore > 0 ? (candidateScore / candidateMaxScore) * 100 : 0;

    const allScores = allTakers
      .map((taker) => {
        const score = taker.testResults.reduce(
          (sum, test) => sum + test.totalPassedScore,
          0
        );
        const maxScore = taker.testResults.reduce(
          (sum, test) => sum + test.totalScore,
          0
        );
        return maxScore > 0 ? (score / maxScore) * 100 : 0;
      })
      .filter((score) => score > 0);

    const assessmentAverage =
      allScores.length > 0
        ? allScores.reduce((sum, score) => sum + score, 0) / allScores.length
        : 0;
    const highestScore = allScores.length > 0 ? Math.max(...allScores) : 0;

    const sectionScores = assessmentTaker.testResults.map((test) => ({
      sectionName: test.title,
      score: test.totalPassedScore,
      maxScore: test.totalScore,
      percentage:
        test.totalScore > 0
          ? (test.totalPassedScore / test.totalScore) * 100
          : 0,
    }));

    // Format the candidate name from email
    const candidateName = assessmentTaker.email
      ? assessmentTaker.email
          .split('@')[0]
          .split('.')
          .map((name) => name.charAt(0).toUpperCase() + name.slice(1))
          .join(' ')
      : 'Candidate';

    // Format the completion date and duration
    const completedDate = assessmentTaker.endTime
      ? new Date(assessmentTaker.endTime).toLocaleDateString('en-US', {
          month: 'long',
          day: 'numeric',
          year: 'numeric',
        })
      : 'Not completed';

    // Calculate duration
    let duration = 'N/A';
    if (assessmentTaker.startTime && assessmentTaker.endTime) {
      const start = new Date(assessmentTaker.startTime);
      const end = new Date(assessmentTaker.endTime);
      const durationMs = end.getTime() - start.getTime();
      const hours = Math.floor(durationMs / (1000 * 60 * 60));
      const minutes = Math.floor((durationMs % (1000 * 60 * 60)) / (1000 * 60));
      duration = `${hours}h ${minutes}m`;
    }

    return {
      candidateEmail: assessmentTaker.email,
      candidateName,
      assessmentName: assessmentTaker.assessmentName,
      candidateScore,
      candidatePercentage,
      assessmentAverage,
      highestScore,
      sectionScores,
      completedDate,
      duration,
    };
  }

  private static generateHTMLReportFromTemplate(
    analysis: AIAnalysisResponse,
    stats: AssessmentStats,
    feedbackData: AIAnalysisResponse['analysis']
  ): string {
    // Read the HTML template
    const template = analysisTemplate;

    // Replace placeholders with actual data
    let html = template
      // Header section
      .replace(/Kweku Gyimah/g, stats.candidateName)
      .replace(/manan.amalitech @gmail\.com/g, stats.candidateEmail)
      .replace(/Senior Frontend Developer Assessment/g, stats.assessmentName)
      .replace(
        /Completed: March 15, 2025 at 2:30 PM Duration: 2h 45m/g,
        `Completed: ${stats.completedDate} Duration: ${stats.duration}`
      )
      .replace(/80%/g, `${Math.round(stats.candidatePercentage)}%`);

    // Process strengths
    let strengthsHtml = '';
    if (
      feedbackData &&
      feedbackData.concept_strengths &&
      feedbackData.concept_strengths.length > 0
    ) {
      strengthsHtml = feedbackData.concept_strengths
        .map(
          (strength, index: number) => `
        <div class="score-item">
          <div class="skill-name">
            <h4>${strength.concept || `Strength ${index + 1}`}</h4>
            <div class="score-badge">
              <span>${strength.score}</span>
              <span>${strength.percentage}%</span>
            </div>
          </div>
          <div class="skill-description">
            ${strength.description || 'No description available'}
          </div>
        </div>
      `
        )
        .join('');
    } else {
      strengthsHtml = `
        <div class="score-item">
          <div class="skill-name">
            <h4>General Knowledge</h4>
            <div class="score-badge">
              <span>N/A</span>
              <span>N/A</span>
            </div>
          </div>
          <div class="skill-description">
            No specific strengths identified.
          </div>
        </div>
      `;
    }

    // Process weaknesses
    // Process weaknesses
    let weaknessesHtml = '';
    if (
      feedbackData &&
      feedbackData.concept_weaknesses &&
      feedbackData.concept_weaknesses.length > 0
    ) {
      weaknessesHtml = feedbackData.concept_weaknesses
        .map(
          (weakness, index: number) => `
        <div class="score-item-improvement">
          <div class="skill-name">
            <h4>${weakness.concept || `Weakness ${index + 1}`}</h4>
            <div class="score-badge improvement">
              <span>${weakness.score}</span>
              <span>${weakness.percentage}%</span>
            </div>
          </div>
          <div class="skill-description">
            ${weakness.description || 'No description available'}
          </div>
        </div>
      `
        )
        .join('');
    } else {
      weaknessesHtml = `
        <div class="score-item-improvement">
          <div class="skill-name">
            <h4>Areas to Improve</h4>
            <div class="score-badge improvement">
              <span>N/A</span>
              <span>N/A</span>
            </div>
          </div>
          <div class="skill-description">
            No specific weaknesses identified.
          </div>
        </div>
      `;
    }

    // Process recommendations
    let recommendationsHtml = '';
    if (
      feedbackData &&
      feedbackData.development_recommendations &&
      feedbackData.development_recommendations.length > 0
    ) {
      recommendationsHtml = feedbackData.development_recommendations
        .map(
          (rec, index: number) => `
        <div class="rec-item">
          <div class="rec-title">
            <div>${index + 1}</div>
            <h3>${rec.concept || `Recommendation ${index + 1}`}</h3>
          </div>
          <div class="rec-description">
            ${rec.recommendation || 'No recommendation available'}
          </div>
        </div>
      `
        )
        .join('');
    } else {
      recommendationsHtml = `
        <div class="rec-item">
          <div class="rec-title">
            <div>1</div>
            <h3>General Recommendation</h3>
          </div>
          <div class="rec-description">
            No specific recommendations available.
          </div>
        </div>
      `;
    }

    // Process priority areas
    let priorityAreasHtml = '';
    if (
      feedbackData &&
      feedbackData.priority_learning_areas &&
      feedbackData.priority_learning_areas.length > 0
    ) {
      priorityAreasHtml = feedbackData.priority_learning_areas
        .map(
          (area, index: number) => `
        <div class="rec-item">
          <div class="rec-title">
            <div>${index + 1}</div>
            <h3>${area.concept || `Priority ${index + 1}`}</h3>
          </div>
          <div class="rec-description">
            ${area.description || 'No description available'}
          </div>
        </div>
      `
        )
        .join('');
    } else {
      priorityAreasHtml = `
        <div class="rec-item">
          <div class="rec-title">
            <div>1</div>
            <h3>General Priority</h3>
          </div>
          <div class="rec-description">
            No specific priority areas identified.
          </div>
        </div>
      `;
    }

    // Process learning path
    let learningPathHtml = '';
    if (
      feedbackData &&
      feedbackData.suggested_learning_path &&
      feedbackData.suggested_learning_path.length > 0
    ) {
      learningPathHtml = feedbackData.suggested_learning_path
        .map((path) => {
          const topics =
            path.concepts && Array.isArray(path.concepts)
              ? path.concepts
                  .map(
                    (concept: string) =>
                      `<div class="week-item">${concept}</div>`
                  )
                  .join('')
              : '<div class="week-item">No specific topics available</div>';

          return `
          <div class="week-section">
            <div class="rec-title">
              <div>${path.step || '?'}</div>
              <h3>${path.timeframe || 'Timeframe'}: ${
            path.priority || 'Normal'
          }</h3>
            </div>
            <div class="week-items">
              ${topics}
            </div>
          </div>
        `;
        })
        .join('');
    } else {
      learningPathHtml = `
        <div class="week-section">
          <div class="rec-title">
            <div>1</div>
            <h3>General Learning Path</h3>
          </div>
          <div class="week-items">
            <div class="week-item">No specific learning path available</div>
          </div>
        </div>
      `;
    }

    // Replace the content sections
    html = html
      .replace(/<!-- Top Strengths content -->/g, strengthsHtml)
      .replace(/<!-- Areas for Improvement content -->/g, weaknessesHtml)
      .replace(
        /<!-- Development Recommendations content -->/g,
        recommendationsHtml
      )
      .replace(/<!-- Priority Learning Areas content -->/g, priorityAreasHtml)
      .replace(/<!-- Suggested Learning Paths content -->/g, learningPathHtml);
    return html;
  }

  private static getBasicTemplate(): string {
    return `
<!DOCTYPE html>
<html>
<head>
    <meta charset="UTF-8">
    <title>Assessment Analytics Report</title>
    <style>
        body { font-family: Arial, sans-serif; margin: 40px; color: #333; }
        .header { text-align: center; margin-bottom: 40px; border-bottom: 2px solid #007bff; padding-bottom: 20px; }
        .section { margin-bottom: 30px; }
        .section h2 { color: #007bff; border-left: 4px solid #007bff; padding-left: 15px; }
        .score-box { background: #f8f9fa; padding: 20px; border-radius: 8px; text-align: center; margin: 20px 0; }
        .score { font-size: 48px; font-weight: bold; color: #28a745; }
    </style>
</head>
<body>
    <div class="header">
        <h1>Assessment Analytics Report</h1>
        <p>Generated on: ${new Date().toLocaleDateString()}</p>
    </div>

    <div class="section">
        <h2>Performance Summary</h2>
        <div class="score-box">
            <div class="score">{{SCORE}}%</div>
            <p>Your Score: {{SCORE_VALUE}}/{{MAX_SCORE}}</p>
        </div>
    </div>

    <div class="section">
        <h2>AI Analysis</h2>
        <div>{{FEEDBACK}}</div>
    </div>
</body>
</html>`;
  }

  private static async generatePDF(htmlContent: string): Promise<Buffer> {
    const browser = await puppeteer.launch({
      headless: true,
      args: ['--no-sandbox', '--disable-setuid-sandbox'],
    });
    const page = await browser.newPage();
    await page.setContent(htmlContent, { waitUntil: 'networkidle0' });
    const pdfUint8Array = await page.pdf({
      format: 'A4',
      printBackground: true,
      margin: {
        top: '15mm',
        bottom: '15mm',
        left: '15mm',
        right: '15mm',
      },
    });
    await browser.close();
    return Buffer.from(pdfUint8Array);
  }
  
  private static convertMarkdownToHTML(markdown: string): string {
    if (!markdown) return '';

    return markdown
      .replace(/^# (.*$)/gm, '<h1>$1</h1>')
      .replace(/^## (.*$)/gm, '<h2>$1</h2>')
      .replace(/^### (.*$)/gm, '<h3>$1</h3>')
      .replace(/\*\*(.*?)\*\*/g, '<strong>$1</strong>')
      .replace(/^- (.*$)/gm, '<li>$1</li>')
      .replace(/(<li>.*<\/li>)/gs, '<ul>$1</ul>')
      .replace(/\n\n/g, '</p><p>')
      .replace(/^(?!<[h|u|l])/gm, '<p>')
      .replace(/(?<!>)$/gm, '</p>')
      .replace(/<p><\/p>/g, '')
      .replace(/<p>(<[h|u])/g, '$1')
      .replace(/(<\/[h|u].*>)<\/p>/g, '$1');
  }
}
