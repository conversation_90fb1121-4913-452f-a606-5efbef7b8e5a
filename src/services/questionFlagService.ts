import prisma from '../prisma';
import { AssessmentTaker, QuestionFlag } from '@prisma/client';
import { QuestionFlagData } from '../helpers/types';
import { questionFlaggingPublish } from '../helpers/kafkaPublishMessage';

export const createFlag = async (
  questionFlagData: QuestionFlagData,
  assessmentTakerInfo: AssessmentTaker
): Promise<{
  questionFlagged: QuestionFlag;
}> => {
  const { id, assessmentId, email, organizationId } = assessmentTakerInfo;
  const { reasonOfFlagging } = questionFlagData;
  let reasonString = '';

  if (reasonOfFlagging.length == 1) {
    reasonString = reasonOfFlagging[0];
  } else if (reasonOfFlagging.length >= 2) {
    reasonString = reasonOfFlagging.reduce((prev, curr, index) => {
      if (index === reasonOfFlagging.length - 1) {
        return prev + ' and ' + curr;
      }
      return prev + curr;
    }, '');
  }

  const questionFlagged = await prisma.questionFlag.create({
    data: {
      ...questionFlagData,
      testTakerEmail: email,
      testTakerId: id,
      assessmentId,
      organizationId,
      reasonOfFlagging: reasonString,
    },
  });

  // publish question flagged event message
  await questionFlaggingPublish(assessmentTakerInfo, questionFlagged);
  return {
    questionFlagged,
  };
};

export const getFlagQuestionBy = async <T>(
  whereClause: T
): Promise<QuestionFlag> => {
  return prisma.questionFlag.findFirst({
    where: whereClause,
  });
};

export const deleteFlagQuestion = async (id: string): Promise<void> => {
  await prisma.questionFlag.delete({
    where: { id },
  });
};
