import * as Sentry from '@sentry/node';
import { nodeProfilingIntegration } from '@sentry/profiling-node';
import 'dotenv/config';
// Initialize Sentry with profiling
Sentry.init({
  dsn: process.env.SENTRY_DSN || '',
  environment: process.env.NODE_ENV || 'development',
  integrations: [
    // Add profiling integration
    nodeProfilingIntegration(),
  ],
  // Set tracesSampleRate to 1.0 to capture 100% of transactions for performance monitoring
  // In production, you'll want to adjust this to a lower value, like 0.2
  tracesSampleRate: process.env.NODE_ENV === 'production' ? 0.2 : 1.0,
  // Profile sampling rate is relative to tracesSampleRate
  profilesSampleRate: 1.0,
  // Enable debug mode in development
  debug: process.env.NODE_ENV !== 'production',
  // Set sample rate to 100% in dev, lower in prod
  sampleRate: process.env.NODE_ENV === 'production' ? 0.2 : 1.0,
});
