{"env": {"browser": true, "es2021": true, "node": true}, "extends": ["eslint:recommended", "plugin:@typescript-eslint/recommended"], "overrides": [], "parser": "@typescript-eslint/parser", "parserOptions": {"ecmaVersion": "latest"}, "plugins": ["@typescript-eslint"], "rules": {"no-console": 2, "@typescript-eslint/no-unused-vars": ["error", {"argsIgnorePattern": "req|res|next"}], "@typescript-eslint/no-explicit-any": "off"}}