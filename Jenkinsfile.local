pipeline {
    agent any
    tools { nodejs 'node22' }

    environment {
        NODE_ENV = 'development'
    }

    stages {
        stage('Install Dependencies') {
            steps {
                sh 'npm ci'
            }
        }

        stage('Lint') {
            steps {
                sh 'npm run lint'
            }
        }

        stage('Run Tests') {
            steps {
                // Create a test env file with dummy values for local testing
                sh '''
                    echo "DATABASE_URL=postgresql://localhost:5432/test_db
                    JWT_SECRET=test_secret
                    NODE_ENV=test" > .env
                '''
                sh 'npm run test'
            }
        }

        stage('Build') {
            steps {
                sh 'npm run build'
            }
        }

        stage('SonarQube Analysis') {
            when {
                anyOf {
                    branch 'enhance-coverage'
                    branch 'develop'
                }
            }

            steps {
                withSonarQubeEnv('sonarqube-test-execution-dev') {
                    script {
                        def scannerHome = tool 'SonarScanner'
                        sh "${scannerHome}/bin/sonar-scanner"
                    }
                }
            }
        }
    }

    post {
        always {
            cleanWs()
        }
        success {
            echo 'Pipeline completed successfully!'
        }
        failure {
            echo 'Pipeline failed!'
        }
    }
}
