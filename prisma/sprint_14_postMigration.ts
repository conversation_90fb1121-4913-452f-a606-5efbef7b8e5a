import { prisma } from './localInstance';
import { sanitizeURL } from '../src/helpers/urlUtils';

const updateURL = (url: string | null) => {
  if (!url) return url;
  const sanitized = sanitizeURL(url);
  const cloudFrontUrl = process.env.CLOUD_FRONT_URL;
  return sanitized ? `${cloudFrontUrl}${new URL(sanitized).pathname}` : null;
};

export const updateIdentityShot = async () => {
  const identities = await prisma.identity.findMany({
    where: {
      OR: [
        {
          linkId: {
            not: {
              startsWith: process.env.CLOUD_FRONT_URL,
            },
          },
        },
        {
          linkHead: {
            not: {
              startsWith: process.env.CLOUD_FRONT_URL,
            },
          },
        },
      ],
    },
    select: {
      id: true,
      linkId: true,
      linkHead: true,
    },
  });
  for (const identity of identities) {
    const { id, linkId, linkHead } = identity as {
      id: string;
      linkId: string | null;
      linkHead: string | null;
    };
    await prisma.identity.update({
      where: {
        id,
      },
      data: {
        linkId: updateURL(linkId),
        linkHead: updateURL(linkHead),
      },
      select: {
        id: true,
        linkHead: true,
        linkId: true,
      },
    });
  }
};

export const updateScreenShot = async () => {
  const screenshots = await prisma.screenShot.findMany({
    where: {
      imageURL: {
        not: {
          startsWith: process.env.CLOUD_FRONT_URL,
        },
      },
    },
    select: {
      id: true,
      imageURL: true,
    },
  });
  for (const screenshot of screenshots) {
    const { id, imageURL } = screenshot;
    await prisma.screenShot.update({
      where: {
        id,
      },
      data: {
        imageURL: updateURL(imageURL),
      },
      select: {
        id: true,
        imageURL: true,
      },
    });
  }
};

export const updateCandidateShots = async () => {
  const candidateShots = await prisma.assessmentTakerCapture.findMany({
    where: {
      imageURL: {
        not: {
          startsWith: process.env.CLOUD_FRONT_URL,
        },
      },
    },
    select: {
      id: true,
      imageURL: true,
    },
  });
  for (const candidateShot of candidateShots) {
    const { id, imageURL } = candidateShot;
    await prisma.assessmentTakerCapture.update({
      where: {
        id,
      },
      data: {
        imageURL: updateURL(imageURL),
      },
      select: {
        id: true,
        imageURL: true,
      },
    });
  }
};

export const main = async () => {
  await updateIdentityShot();
  await updateScreenShot();
  await updateCandidateShots();
};
