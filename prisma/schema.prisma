// This is your Prisma schema file,
// learn more about it in the docs: https://pris.ly/d/prisma-schema
generator client {
  provider = "prisma-client-js"
}

datasource db {
  provider = "postgresql"
  url      = env("DATABASE_URL")
}

model AssessmentTaker {
  id                                String                   @id @default(uuid()) @db.Uuid
  assessmentId                      String?                  @db.Uuid
  assessmentName                    String?
  dispatcher                        String?
  showResults                       Boolean?
  organizationId                    String?                  @db.Uuid()
  email                             String?
  assessmentLink                    String?
  assessmentWindowViolationCount    Int?                     @default(0)
  assessmentWindowViolationDuration Int?                     @default(0)
  assessmentTakerShotCount          Int?                     @default(0)
  assessmentTakerViolationShotCount Int?                     @default(0)
  windowShotCount                   Int?                     @default(0)
  windowViolationShotCount          Int?                     @default(0)
  assessmentDuration                Int?                     @default(0) // second
  estimatedEndTime                  DateTime?
  expireDate                        DateTime?
  commenceDate                      DateTime?
  startTime                         DateTime?
  endTime                           DateTime?
  proctor                           String?
  conductSurvey                     Boolean?
  proctorFeatures                   String[]
  screenshotsInterval               Int?                     @default(15)
  camerashotsInterval               Int?                     @default(15)
  testList                          String[]
  submittedTests                    String[]
  phase                             AssessmentPhase?
  linkStatus                        AssessmentLinkStatus?    @default(VALID)
  status                            AssessmentTakerStatus?   @default(IN_PROGRESS)
  assessmentTakerScreenShots        ScreenShot[]
  assessmentTakerCaptures           AssessmentTakerCapture[]
  idleTimeTrack                     Idletime[]
  windowViolationTrack              WindowViolation[]
  testResults                       TestResult[]
  logs                              AssessmentTakingLog[]
  logSummary                        String[]
  identity                          Identity?
  createdAt                         DateTime                 @default(now())
  updatedAt                         DateTime                 @updatedAt
}

model TestResult {
  assessmentTakerId                 String                @db.Uuid
  assessmentTaker                   AssessmentTaker       @relation(fields: [assessmentTakerId], references: [id], onDelete: Cascade)
  assessmentId                      String                @db.Uuid()
  passMark                          Int?
  passStatus                        PassStatus?           @default(FAIL)
  testId                            String                @db.Uuid()
  title                             String
  startTime                         DateTime?
  finishTime                        DateTime?
  duration                          Int?                  @default(0) // in seconds
  testPercentage                    Float?                @default(0.00)
  result                            Json[]
  totalScore                        Int
  order                             Int?                  @default(-1)
  totalPassedScore                  Float?                @default(0.00)
  numberOfQuestionsFailed           Int?                  @default(0)
  numberOfQuestionsPassed           Int?                  @default(0)
  numberOfQuestionsAnswered         Int?                  @default(0)
  numberOfQuestions                 Int
  testWindowViolationDuration       Int?                  @default(0) // in seconds
  testWindowViolationCount          Int?                  @default(0)
  testTakerShotCount                Int?                  @default(0)
  testTakerViolationShotCount       Int?                  @default(0)
  testWindowShotCount               Int?                  @default(0)
  testWindowViolationShotCount      Int?                  @default(0)
  status                            TestSubmissionStatus? @default(NOT_SUBMITTED)
  drafts                            Draft[]
  draftIntervalScreenshots          Json[]
  draftIntervalAssessmentTakerShots Json[]

  @@id([testId, assessmentTakerId])
}

model Draft {
  id                 String                   @id @default(uuid()) @db.Uuid
  questionId         String                   @db.Uuid
  questionType       String
  testId             String                   @db.Uuid
  assessmentTakerId  String                   @db.Uuid
  stringArrayAnswers String[]
  matchMatrixAnswers MatchMatrixDraftAnswer[]
  codeDraftInfo      CodeDraftInfo[]
  testResult         TestResult               @relation(fields: [testId, assessmentTakerId], references: [testId, assessmentTakerId])
}

model MatchMatrixDraftAnswer {
  id            String   @id @default(uuid()) @db.Uuid
  subquestionId String
  answers       String[]
  draftId       String   @db.Uuid
  draft         Draft    @relation(fields: [draftId], references: [id], onDelete: Cascade)
}

model CodeDraftInfo {
  id         String @id @default(uuid()) @db.Uuid
  code       String
  languageId Int
  Draft      Draft  @relation(fields: [draftId], references: [id], onDelete: Cascade)
  draftId    String @db.Uuid
}

model Identity {
  id                String          @id @default(uuid()) @db.Uuid
  assessmentTakerId String          @unique() @db.Uuid()
  assessmentTaker   AssessmentTaker @relation(fields: [assessmentTakerId], references: [id], onDelete: Cascade)
  linkId            String?
  linkHead          String?
  createdAt         DateTime        @default(now())
  updatedAt         DateTime        @updatedAt
}

model Idletime {
  id                   Int                      @id @default(autoincrement())
  assessmentTakerId    String                   @db.Uuid()
  assessmentTaker      AssessmentTaker          @relation(fields: [assessmentTakerId], references: [id], onDelete: Cascade)
  assessmentTakerShots AssessmentTakerCapture[]
  violationNumber      Int?
  testId               String                   @db.Uuid()
  testNumber           Int
  questionId           String                   @db.Uuid()
  questNumber          Int
  startTime            DateTime
  endTime              DateTime
  duration             Int //in seconds
  createdAt            DateTime                 @default(now())
  updatedAt            DateTime                 @updatedAt
}

model WindowViolation {
  id                Int             @id @default(autoincrement())
  assessmentTakerId String          @db.Uuid()
  assessmentTaker   AssessmentTaker @relation(fields: [assessmentTakerId], references: [id], onDelete: Cascade)
  screenShots       ScreenShot[]
  violationNumber   Int?
  testId            String          @db.Uuid()
  testNumber        Int
  questionId        String          @db.Uuid()
  questNumber       Int
  startTime         DateTime
  endTime           DateTime
  duration          Int //in seconds
  createdAt         DateTime        @default(now())
  updatedAt         DateTime        @updatedAt
}

model QuestionFlag {
  id               String   @id @default(uuid()) @db.Uuid
  assessmentId     String?  @db.Uuid()
  organizationId   String?  @db.Uuid()
  testId           String?  @db.Uuid()
  questionId       String?  @db.Uuid()
  questionText     String?
  testTakerId      String?  @db.Uuid()
  testTakerEmail   String?
  reasonOfFlagging String?
  createdAt        DateTime @default(now())
  updatedAt        DateTime @updatedAt
}

model TestTakerBasicAssessmentResult {
  id           String    @id @default(uuid()) @db.Uuid
  testTakerId  String?   @db.Uuid()
  assessmentId String?   @db.Uuid()
  startTime    DateTime?
  endTime      DateTime?
  timeTaken    String?
  startDate    DateTime?
  endDate      DateTime?
  status       String
}

model ScreenShot {
  id                Int              @id @default(autoincrement())
  assessmentTakerId String           @db.Uuid()
  assessmentTaker   AssessmentTaker  @relation(fields: [assessmentTakerId], references: [id], onDelete: Cascade)
  windowViolationId Int?
  windowViolation   WindowViolation? @relation(fields: [windowViolationId], references: [id], onDelete: Cascade)
  shots             String[]
  isViolationShot   Boolean?         @default(false)
  imageURL          String?
  isIntegrityShot   Boolean          @default(false)
  createdAt         DateTime         @default(now())
  updatedAt         DateTime         @updatedAt
}

model AssessmentTakerCapture {
  id                Int             @id @default(autoincrement())
  assessmentTakerId String          @db.Uuid()
  assessmentTaker   AssessmentTaker @relation(fields: [assessmentTakerId], references: [id], onDelete: Cascade)
  idleTimeId        Int?
  idleTime          Idletime?       @relation(fields: [idleTimeId], references: [id], onDelete: Cascade)
  shots             String[]
  isViolationShot   Boolean?        @default(false)
  imageURL          String?
  createdAt         DateTime        @default(now())
  updatedAt         DateTime        @updatedAt
}

model AssessmentTakingLog {
  id                Int             @id() @default(autoincrement())
  assessmentTakerId String          @db.Uuid()
  assessmentTaker   AssessmentTaker @relation(fields: [assessmentTakerId], references: [id], onDelete: Cascade)
  action            String
  addOn             Json?
  createdAt         DateTime        @default(now())
}

enum AssessmentTakerStatus {
  IN_PROGRESS
  COMPLETED
  INCOMPLETE
}

enum AssessmentPhase {
  LINK_VIEW
  CODE_CONDUCT_SIGNING
  TEST_TAKING
  ASSESSMENT_COMPLETION
}

enum TestSubmissionStatus {
  NOT_SUBMITTED
  SUBMITTED
}

enum AssessmentLinkStatus {
  INVALID
  VALID
}

enum PassStatus {
  PASS
  FAIL
}

enum AnswerStatus {
  CORRECT
  WRONG
  SKIPPED
}
