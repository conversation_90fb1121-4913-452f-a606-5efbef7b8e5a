-- CreateTable
CREATE TABLE "Window" (
    "id" UUID NOT NULL,
    "testTakerId" UUID,
    "organizationId" UUID,
    "assessmentId" UUID,
    "testId" UUID,
    "questionId" UUID,
    "timeStamp" TEXT,
    "screenshot" TEXT,
    "timeSpent" TEXT,
    "createdAt" TIMESTAMP(3) NOT NULL DEFAULT CURRENT_TIMESTAMP,
    "updatedAt" TIMESTAMP(3) NOT NULL,

    CONSTRAINT "Window_pkey" PRIMARY KEY ("id")
);
