-- CreateTable
CREATE TABLE "QuestionFlag" (
    "id" UUID NOT NULL,
    "assessmentId" UUID,
    "testId" UUID,
    "questionId" UUID,
    "testTakerId" UUID,
    "reasonOfFlagging" TEXT,
    "createdAt" TIMESTAMP(3) NOT NULL DEFAULT CURRENT_TIMESTAMP,
    "updatedAt" TIMESTAMP(3) NOT NULL,

    CONSTRAINT "QuestionFlag_pkey" PRIMARY KEY ("id")
);

-- CreateTable
CREATE TABLE "TestTakerBasicAssessmentResult" (
    "id" UUID NOT NULL,
    "testTakerId" UUID,
    "assessmentId" UUID,
    "startTime" TIMESTAMP(3),
    "endTime" TIMESTAMP(3),
    "timeTaken" TEXT,
    "startDate" TIMESTAMP(3),
    "endDate" TIMESTAMP(3),
    "status" TEXT NOT NULL,

    CONSTRAINT "TestTakerBasicAssessmentResult_pkey" PRIMARY KEY ("id")
);
