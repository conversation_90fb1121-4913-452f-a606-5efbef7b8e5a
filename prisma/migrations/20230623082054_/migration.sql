-- CreateTable
CREATE TABLE "AssessmentTaker" (
    "id" UUID NOT NULL,
    "assessmentId" UUID,
    "testManagerId" UUID,
    "organizationId" UUID,
    "email" TEXT,
    "assessmentLink" TEXT,

    CONSTRAINT "AssessmentTaker_pkey" PRIMARY KEY ("id")
);

-- CreateTable
CREATE TABLE "TestResult" (
    "id" UUID NOT NULL,
    "testTakerId" UUID,
    "assessmentId" UUID,
    "testId" UUID,
    "questionId" UUID,
    "optionAnswers" TEXT[],
    "correctAnswers" TEXT[],
    "testTakerAnswers" TEXT[],
    "startTime" TIMESTAMP(3),
    "finishTime" TIMESTAMP(3),
    "timeTaken" TEXT,
    "score" INTEGER,
    "percentage" DOUBLE PRECISION,

    CONSTRAINT "TestResult_pkey" PRIMARY KEY ("id")
);
