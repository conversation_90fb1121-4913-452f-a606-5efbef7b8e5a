{"name": "amap-test-execution-service", "version": "1.0.0", "description": "Amalitech Assessment Platform: Test Execution Service", "main": "app.ts", "prisma": {"seed": "ts-node prisma/seed.ts"}, "scripts": {"build": "tsc", "start": "node dist/bin/server.js", "dev": "nodemon --exec ts-node --files src/bin/server.ts", "db": "npx sequelize-cli db:create", "drop": "npx sequelize-cli db:drop", "seed": "npx sequelize-cli db:seed:all", "migrate": "npx sequelize-cli db:migrate", "make-model": "npx sequelize-cli model:generate --name", "create-migration": "npx sequelize-cli migration:create --name", "undo": "npx sequelize-cli db:migrate:undo", "undo-all": "npx sequelize-cli db:migrate:undo:all", "unseed": "npx sequelize-cli db:seed:undo", "unseed-all": "npx sequelize-cli db:seed:undo:all", "rebuild": "npx prisma migrate reset", "create-schema": "npx prisma migrate dev", "prisma-migrate": "npx prisma migrate deploy", "create-db": "npx prisma init --datasourc-provider sqlite", "prisma-studio": "npx prisma studio", "prisma-generate": "npx prisma generate", "prisma-update": "npx prisma db push", "prisma-seed": "npx prisma db seed", "test": "node --import tsx ./node_modules/.bin/nyc --reporter=lcov --reporter=html --reporter=text mocha './tests/**/*.test.ts' --timeout=10000 --exit", "win-test": "cross-env NODE_ENV=test npx nyc --reporter=lcov --reporter=html --reporter=text mocha './tests/**/*.test.ts' --timeout=10000 --exit", "prepare": "husky install", "lint": "npx eslint --ext .ts", "lint:fix": "npx eslint --ext .ts --fix", "prettier": "npx prettier --check **/*.ts", "format": "npx prettier --write **/*.ts"}, "repository": {"type": "git", "url": "git+https://github.com/Amali-Tech/amap-test-execution-service.git"}, "author": "", "license": "ISC", "bugs": {"url": "https://github.com/Amali-Tech/amap-test-execution-service/issues"}, "homepage": "https://github.com/Amali-Tech/amap-test-execution-service/blob/main/README.md", "dependencies": {"@prisma/client": "^5.0.0", "@sentry/node": "^9.6.0", "@sentry/profiling-node": "^9.6.0", "@types/node-cron": "^3.0.11", "@types/pg": "^8.6.6", "@types/puppeteer": "^5.4.7", "aws-sdk": "^2.1692.0", "axios": "^1.4.0", "cors": "^2.8.5", "dotenv": "^16.0.3", "express": "5.0.0", "helmet": "^7.0.0", "ioredis": "^5.3.2", "joi": "^17.9.2", "jsonwebtoken": "^9.0.2", "jwt-decode": "^3.1.2", "kafkajs": "^2.2.4", "morgan": "^1.10.0", "node-cron": "^3.0.3", "nodemailer": "^6.9.6", "openai": "^4.20.1", "pg": "^8.10.0", "pg-hstore": "^2.3.4", "puppeteer": "^24.12.0", "request": "^2.88.2", "ts-node": "^10.9.1", "ts-node-dev": "^2.0.0", "uuid": "^9.0.1", "winston": "^3.11.0"}, "devDependencies": {"@commitlint/cli": "^17.6.5", "@commitlint/config-conventional": "^17.6.5", "@types/axios": "^0.14.0", "@types/chai": "^5.2.1", "@types/cors": "^2.8.13", "@types/debug": "^4.1.7", "@types/express": "^4.17.17", "@types/jsonwebtoken": "^9.0.5", "@types/mocha": "^10.0.10", "@types/morgan": "^1.9.4", "@types/multer": "^1.4.7", "@types/node": "^18.16.19", "@types/nodemailer": "^6.4.11", "@types/proxyquire": "^1.3.31", "@types/sinon-chai": "^4.0.0", "@types/sinon-express-mock": "^1.3.12", "@types/uuid": "^9.0.7", "@typescript-eslint/eslint-plugin": "^5.59.11", "@typescript-eslint/parser": "^5.59.11", "chai": "^5.2.0", "chai-as-promised": "^8.0.1", "cross-env": "^7.0.3", "eslint": "^8.42.0", "eslint-config-prettier": "^8.8.0", "husky": "^8.0.3", "lint-staged": "^13.2.2", "mocha": "^10.2.0", "nodemon": "^2.0.22", "nyc": "^17.1.0", "prettier": "^2.8.8", "prisma": "^5.0.0", "proxyquire": "^2.1.3", "sinon": "^20.0.0", "sinon-chai": "^4.0.0", "sinon-express-mock": "^2.2.1", "supertest": "^6.3.3", "tsx": "^3.9.0", "typescript": "^5.1.3"}}